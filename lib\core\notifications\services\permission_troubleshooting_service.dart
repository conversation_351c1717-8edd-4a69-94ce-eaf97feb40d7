import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/permission_models.dart';
import '../models/troubleshooting_models.dart';
import '../providers/unified_notification_provider.dart';

/// Permission Troubleshooting Service
///
/// **Task 3.3.4: Create permission troubleshooting guides and helpers**
///
/// This service provides comprehensive troubleshooting guides and helper utilities
/// for notification permission issues following Context7 MCP patterns.
///
/// Features:
/// - Platform-specific troubleshooting guides
/// - Automated problem detection and diagnosis
/// - Step-by-step resolution instructions
/// - Interactive troubleshooting wizards
/// - Common issue resolution helpers
/// - System information gathering for support
/// - Accessibility-friendly guidance
/// - Localization support for multiple languages
class PermissionTroubleshootingService {
  final WidgetRef ref;

  PermissionTroubleshootingService({required this.ref});

  /// Get comprehensive troubleshooting guide for permission issues
  ///
  /// **Context7 MCP Implementation:**
  /// - Single responsibility: Focused on troubleshooting guidance
  /// - Dependency inversion: Uses abstract permission provider interface
  /// - Open/closed principle: Extensible for new troubleshooting scenarios
  /// - Interface segregation: Specific troubleshooting methods for different issues
  ///
  /// **Usage:**
  /// ```dart
  /// final service = PermissionTroubleshootingService(ref: ref);
  /// final guide = await service.getTroubleshootingGuide(
  ///   deniedTypes: [PermissionNotificationType.local],
  ///   includeSystemInfo: true,
  /// );
  /// ```
  Future<TroubleshootingGuide> getTroubleshootingGuide({
    List<PermissionNotificationType> deniedTypes = const [],
    bool includeSystemInfo = true,
    bool includeCommonSolutions = true,
    bool includePlatformSpecific = true,
    TroubleshootingLevel level = TroubleshootingLevel.comprehensive,
  }) async {
    try {
      // Gather system information
      final systemInfo = includeSystemInfo ? await _gatherSystemInformation() : null;

      // Analyze permission issues
      final analysis = await _analyzePermissionIssues(deniedTypes);

      // Generate troubleshooting steps
      final steps = await _generateTroubleshootingSteps(
        deniedTypes: deniedTypes,
        analysis: analysis,
        includeCommonSolutions: includeCommonSolutions,
        includePlatformSpecific: includePlatformSpecific,
        level: level,
      );

      // Get common solutions
      final commonSolutions = includeCommonSolutions
          ? _getCommonSolutions(deniedTypes, analysis)
          : <TroubleshootingSolution>[];

      // Get platform-specific guidance
      final platformGuidance = includePlatformSpecific
          ? _getPlatformSpecificGuidance(deniedTypes, analysis)
          : <PlatformGuidance>[];

      return TroubleshootingGuide(
        id: 'troubleshooting_${DateTime.now().millisecondsSinceEpoch}',
        title: _generateGuideTitle(deniedTypes, analysis),
        description: _generateGuideDescription(deniedTypes, analysis),
        deniedTypes: deniedTypes,
        analysis: analysis,
        steps: steps,
        commonSolutions: commonSolutions,
        platformGuidance: platformGuidance,
        systemInfo: systemInfo,
        level: level,
        generatedAt: DateTime.now(),
      );
    } on Exception catch (e, stackTrace) {
      throw TroubleshootingException('Failed to generate troubleshooting guide: $e', stackTrace: stackTrace);
    }
  }

  /// Run automated troubleshooting diagnosis
  Future<TroubleshootingDiagnosis> runAutomatedDiagnosis({
    bool checkSystemSettings = true,
    bool checkAppSettings = true,
    bool checkDeviceCompatibility = true,
    bool checkNetworkConnectivity = true,
  }) async {
    try {
      final issues = <TroubleshootingIssue>[];
      final recommendations = <String>[];

      // Check current permission status
      final permissionStatus = await ref.read(unifiedNotificationSettingsProvider.notifier).getPermissionStatusReport();

      // Analyze permission issues
      if (checkSystemSettings) {
        final systemIssues = await _checkSystemSettings(permissionStatus);
        issues.addAll(systemIssues);
      }

      if (checkAppSettings) {
        final appIssues = await _checkAppSettings(permissionStatus);
        issues.addAll(appIssues);
      }

      if (checkDeviceCompatibility) {
        final compatibilityIssues = await _checkDeviceCompatibility();
        issues.addAll(compatibilityIssues);
      }

      if (checkNetworkConnectivity) {
        final networkIssues = await _checkNetworkConnectivity();
        issues.addAll(networkIssues);
      }

      // Generate recommendations based on issues
      recommendations.addAll(_generateRecommendations(issues));

      return TroubleshootingDiagnosis(
        id: 'diagnosis_${DateTime.now().millisecondsSinceEpoch}',
        issues: issues,
        recommendations: recommendations,
        severity: _calculateDiagnosisSeverity(issues),
        permissionStatus: permissionStatus,
        systemInfo: await _gatherSystemInformation(),
        diagnosisTime: DateTime.now(),
      );
    } on Exception catch (e, stackTrace) {
      throw TroubleshootingException('Automated diagnosis failed: $e', stackTrace: stackTrace);
    }
  }

  /// Get quick fix suggestions for common permission issues
  List<QuickFix> getQuickFixes(List<PermissionNotificationType> deniedTypes) {
    final quickFixes = <QuickFix>[];

    for (final type in deniedTypes) {
      switch (type) {
        case PermissionNotificationType.local:
        case PermissionNotificationType.scheduled:
          quickFixes.addAll(_getLocalNotificationQuickFixes());
          break;
        case PermissionNotificationType.push:
          quickFixes.addAll(_getPushNotificationQuickFixes());
          break;
        case PermissionNotificationType.background:
          quickFixes.addAll(_getBackgroundNotificationQuickFixes());
          break;
        case PermissionNotificationType.critical:
          quickFixes.addAll(_getCriticalNotificationQuickFixes());
          break;
        case PermissionNotificationType.provisional:
          quickFixes.addAll(_getProvisionalNotificationQuickFixes());
          break;
      }
    }

    // Remove duplicates and sort by priority
    final uniqueFixes = quickFixes.toSet().toList();
    uniqueFixes.sort((a, b) => a.priority.compareTo(b.priority));

    return uniqueFixes;
  }

  /// Get platform-specific troubleshooting steps
  List<TroubleshootingStep> getPlatformSpecificSteps() {
    if (Platform.isAndroid) {
      return _getAndroidTroubleshootingSteps();
    } else if (Platform.isIOS) {
      return _getIOSTroubleshootingSteps();
    } else {
      return _getGenericTroubleshootingSteps();
    }
  }

  /// Check if permission issue can be resolved automatically
  Future<bool> canResolveAutomatically(PermissionNotificationType type) async {
    try {
      final currentStatus = await ref
          .read(unifiedNotificationSettingsProvider.notifier)
          .checkPermissions(types: [type]);

      final permissionStatus = currentStatus.permissionResults[type];

      // Can resolve if permission is denied but not permanently denied
      return permissionStatus == PermissionStatus.denied || permissionStatus == PermissionStatus.unknown;
    } on Exception {
      return false;
    }
  }

  /// Attempt automatic resolution of permission issues
  Future<AutoResolutionResult> attemptAutoResolution(List<PermissionNotificationType> deniedTypes) async {
    try {
      final results = <PermissionNotificationType, bool>{};
      final errors = <String>[];

      for (final type in deniedTypes) {
        if (await canResolveAutomatically(type)) {
          try {
            final result = await ref
                .read(unifiedNotificationSettingsProvider.notifier)
                .requestPermissions([type], showRationale: true, fallbackToSettings: false);

            results[type] = result.isSuccess;
          } on Exception catch (e) {
            results[type] = false;
            errors.add('Failed to resolve $type: $e');
          }
        } else {
          results[type] = false;
          errors.add('$type requires manual resolution');
        }
      }

      final resolvedCount = results.values.where((resolved) => resolved).length;

      return AutoResolutionResult(
        totalAttempted: deniedTypes.length,
        resolvedCount: resolvedCount,
        results: results,
        errors: errors,
        isFullyResolved: resolvedCount == deniedTypes.length,
        requiresManualIntervention: errors.isNotEmpty,
      );
    } on Exception catch (e, stackTrace) {
      throw TroubleshootingException('Auto-resolution failed: $e', stackTrace: stackTrace);
    }
  }

  /// Generate support information for help requests
  Future<SupportInformation> generateSupportInformation({
    bool includeSystemInfo = true,
    bool includePermissionStatus = true,
    bool includeAppLogs = false,
    bool includeDeviceInfo = true,
  }) async {
    try {
      final info = <String, dynamic>{};

      if (includeSystemInfo) {
        info['systemInfo'] = await _gatherSystemInformation();
      }

      if (includePermissionStatus) {
        final status = await ref.read(unifiedNotificationSettingsProvider.notifier).getPermissionStatusReport();
        info['permissionStatus'] = _serializePermissionStatus(status);
      }

      if (includeDeviceInfo) {
        info['deviceInfo'] = await _gatherDeviceInformation();
      }

      if (includeAppLogs) {
        info['appLogs'] = await _gatherRecentLogs();
      }

      return SupportInformation(
        id: 'support_${DateTime.now().millisecondsSinceEpoch}',
        generatedAt: DateTime.now(),
        appVersion: await _getAppVersion(),
        platformInfo: _getPlatformInfo(),
        supportData: info,
      );
    } on Exception catch (e, stackTrace) {
      throw TroubleshootingException('Failed to generate support information: $e', stackTrace: stackTrace);
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// Gather comprehensive system information
  Future<SystemInformation> _gatherSystemInformation() async {
    return SystemInformation(
      platform: Platform.operatingSystem,
      platformVersion: Platform.operatingSystemVersion,
      isPhysicalDevice: !kIsWeb && (Platform.isAndroid || Platform.isIOS),
      locale: Platform.localeName,
      timezone: DateTime.now().timeZoneName,
      dartVersion: Platform.version,
      gatherTime: DateTime.now(),
    );
  }

  /// Analyze permission issues
  Future<PermissionIssueAnalysis> _analyzePermissionIssues(List<PermissionNotificationType> deniedTypes) async {
    final issues = <PermissionIssueType>[];
    final causes = <String>[];
    final impacts = <String>[];

    for (final type in deniedTypes) {
      // Analyze each permission type
      final analysis = _analyzePermissionType(type);
      issues.addAll(analysis.issues);
      causes.addAll(analysis.causes);
      impacts.addAll(analysis.impacts);
    }

    return PermissionIssueAnalysis(
      deniedTypes: deniedTypes,
      issues: issues.toSet().toList(),
      causes: causes.toSet().toList(),
      impacts: impacts.toSet().toList(),
      severity: _calculateIssueSeverity(issues),
      analysisTime: DateTime.now(),
    );
  }

  /// Analyze specific permission type
  PermissionTypeAnalysis _analyzePermissionType(PermissionNotificationType type) {
    switch (type) {
      case PermissionNotificationType.local:
        return const PermissionTypeAnalysis(
          issues: [PermissionIssueType.localNotificationsDenied],
          causes: ['User denied notification permission', 'System restrictions'],
          impacts: ['No prayer time alerts', 'Missed reminders'],
        );
      case PermissionNotificationType.push:
        return const PermissionTypeAnalysis(
          issues: [PermissionIssueType.pushNotificationsDenied],
          causes: ['Network issues', 'Firebase configuration', 'User denial'],
          impacts: ['No real-time updates', 'Missed community announcements'],
        );
      case PermissionNotificationType.background:
        return const PermissionTypeAnalysis(
          issues: [PermissionIssueType.backgroundNotificationsDenied],
          causes: ['Battery optimization', 'Background app restrictions'],
          impacts: ['Notifications only when app is open', 'Delayed alerts'],
        );
      case PermissionNotificationType.critical:
        return const PermissionTypeAnalysis(
          issues: [PermissionIssueType.criticalAlertsDenied],
          causes: ['System security restrictions', 'User preference'],
          impacts: ['Missed critical prayer times', 'No emergency alerts'],
        );
      case PermissionNotificationType.scheduled:
        return const PermissionTypeAnalysis(
          issues: [PermissionIssueType.scheduledNotificationsDenied],
          causes: ['Exact alarm restrictions', 'System limitations'],
          impacts: ['Inaccurate timing', 'Delayed notifications'],
        );
      case PermissionNotificationType.provisional:
        return const PermissionTypeAnalysis(
          issues: [PermissionIssueType.provisionalNotificationsDenied],
          causes: ['iOS-specific restrictions', 'User settings'],
          impacts: ['No quiet notifications', 'Reduced engagement'],
        );
    }
  }

  /// Generate troubleshooting steps
  Future<List<TroubleshootingStep>> _generateTroubleshootingSteps({
    required List<PermissionNotificationType> deniedTypes,
    required PermissionIssueAnalysis analysis,
    required bool includeCommonSolutions,
    required bool includePlatformSpecific,
    required TroubleshootingLevel level,
  }) async {
    final steps = <TroubleshootingStep>[];

    // Add basic steps
    steps.addAll(_getBasicTroubleshootingSteps());

    // Add permission-specific steps
    for (final type in deniedTypes) {
      steps.addAll(_getPermissionSpecificSteps(type));
    }

    // Add platform-specific steps
    if (includePlatformSpecific) {
      steps.addAll(getPlatformSpecificSteps());
    }

    // Add advanced steps for comprehensive level
    if (level == TroubleshootingLevel.comprehensive) {
      steps.addAll(_getAdvancedTroubleshootingSteps());
    }

    // Sort by priority and remove duplicates
    final uniqueSteps = steps.toSet().toList();
    uniqueSteps.sort((a, b) => a.priority.compareTo(b.priority));

    return uniqueSteps;
  }

  /// Get basic troubleshooting steps
  List<TroubleshootingStep> _getBasicTroubleshootingSteps() {
    return [
      const TroubleshootingStep(
        id: 'check_app_settings',
        title: 'Check App Notification Settings',
        description: 'Verify that notifications are enabled in the app settings',
        instructions: [
          'Open the app settings',
          'Navigate to Notifications section',
          'Ensure notifications are enabled',
          'Check individual prayer notification settings',
        ],
        priority: 1,
        estimatedTime: Duration(minutes: 2),
        difficulty: TroubleshootingDifficulty.easy,
      ),
      const TroubleshootingStep(
        id: 'check_system_settings',
        title: 'Check System Notification Settings',
        description: 'Verify that the app has permission to send notifications',
        instructions: [
          'Open device Settings',
          'Navigate to Apps or Application Manager',
          'Find "Masajid Al Bahrain"',
          'Check notification permissions',
          'Enable all required permissions',
        ],
        priority: 2,
        estimatedTime: Duration(minutes: 3),
        difficulty: TroubleshootingDifficulty.easy,
      ),
    ];
  }

  /// Get permission-specific troubleshooting steps
  List<TroubleshootingStep> _getPermissionSpecificSteps(PermissionNotificationType type) {
    switch (type) {
      case PermissionNotificationType.local:
        return _getLocalNotificationSteps();
      case PermissionNotificationType.push:
        return _getPushNotificationSteps();
      case PermissionNotificationType.background:
        return _getBackgroundNotificationSteps();
      case PermissionNotificationType.critical:
        return _getCriticalNotificationSteps();
      case PermissionNotificationType.scheduled:
        return _getScheduledNotificationSteps();
      case PermissionNotificationType.provisional:
        return _getProvisionalNotificationSteps();
    }
  }

  /// Get local notification troubleshooting steps
  List<TroubleshootingStep> _getLocalNotificationSteps() {
    return [
      const TroubleshootingStep(
        id: 'enable_local_notifications',
        title: 'Enable Local Notifications',
        description: 'Allow the app to send local notifications',
        instructions: [
          'Go to device Settings > Apps > Masajid Al Bahrain',
          'Tap on "Notifications"',
          'Enable "Allow notifications"',
          'Ensure "Show notifications" is turned on',
        ],
        priority: 3,
        estimatedTime: Duration(minutes: 2),
        difficulty: TroubleshootingDifficulty.easy,
      ),
    ];
  }

  /// Get push notification troubleshooting steps
  List<TroubleshootingStep> _getPushNotificationSteps() {
    return [
      const TroubleshootingStep(
        id: 'check_internet_connection',
        title: 'Check Internet Connection',
        description: 'Push notifications require an active internet connection',
        instructions: [
          'Verify you have a stable internet connection',
          'Try switching between WiFi and mobile data',
          'Test with other apps that use push notifications',
        ],
        priority: 4,
        estimatedTime: Duration(minutes: 1),
        difficulty: TroubleshootingDifficulty.easy,
      ),
    ];
  }

  /// Get background notification troubleshooting steps
  List<TroubleshootingStep> _getBackgroundNotificationSteps() {
    return [
      const TroubleshootingStep(
        id: 'disable_battery_optimization',
        title: 'Disable Battery Optimization',
        description: 'Prevent the system from limiting background activity',
        instructions: [
          'Go to Settings > Battery > Battery Optimization',
          'Find "Masajid Al Bahrain"',
          'Select "Don\'t optimize"',
          'Confirm the change',
        ],
        priority: 5,
        estimatedTime: Duration(minutes: 3),
        difficulty: TroubleshootingDifficulty.medium,
      ),
    ];
  }

  /// Get critical notification troubleshooting steps
  List<TroubleshootingStep> _getCriticalNotificationSteps() {
    return [
      const TroubleshootingStep(
        id: 'enable_critical_alerts',
        title: 'Enable Critical Alerts',
        description: 'Allow critical notifications to bypass Do Not Disturb',
        instructions: [
          'Go to Settings > Notifications > Masajid Al Bahrain',
          'Enable "Critical Alerts"',
          'Confirm you want to allow critical alerts',
        ],
        priority: 6,
        estimatedTime: Duration(minutes: 2),
        difficulty: TroubleshootingDifficulty.medium,
      ),
    ];
  }

  /// Get scheduled notification troubleshooting steps
  List<TroubleshootingStep> _getScheduledNotificationSteps() {
    return [
      const TroubleshootingStep(
        id: 'enable_exact_alarms',
        title: 'Enable Exact Alarms',
        description: 'Allow precise timing for prayer notifications',
        instructions: [
          'Go to Settings > Apps > Special app access',
          'Find "Alarms & reminders"',
          'Enable for "Masajid Al Bahrain"',
        ],
        priority: 7,
        estimatedTime: Duration(minutes: 3),
        difficulty: TroubleshootingDifficulty.medium,
      ),
    ];
  }

  /// Get provisional notification troubleshooting steps
  List<TroubleshootingStep> _getProvisionalNotificationSteps() {
    return [
      const TroubleshootingStep(
        id: 'enable_provisional_notifications',
        title: 'Enable Provisional Notifications',
        description: 'Allow quiet notifications that can be reviewed later',
        instructions: [
          'Go to Settings > Notifications > Masajid Al Bahrain',
          'Select "Deliver Quietly"',
          'Enable "In Notification Center"',
        ],
        priority: 8,
        estimatedTime: Duration(minutes: 2),
        difficulty: TroubleshootingDifficulty.easy,
      ),
    ];
  }

  /// Get Android-specific troubleshooting steps
  List<TroubleshootingStep> _getAndroidTroubleshootingSteps() {
    return [
      const TroubleshootingStep(
        id: 'android_notification_channels',
        title: 'Check Android Notification Channels',
        description: 'Verify notification channels are properly configured',
        instructions: [
          'Go to Settings > Apps > Masajid Al Bahrain > Notifications',
          'Check each notification category',
          'Ensure all relevant categories are enabled',
          'Adjust importance levels as needed',
        ],
        priority: 10,
        estimatedTime: Duration(minutes: 4),
        difficulty: TroubleshootingDifficulty.medium,
      ),
    ];
  }

  /// Get iOS-specific troubleshooting steps
  List<TroubleshootingStep> _getIOSTroubleshootingSteps() {
    return [
      const TroubleshootingStep(
        id: 'ios_notification_settings',
        title: 'Check iOS Notification Settings',
        description: 'Verify iOS notification settings are properly configured',
        instructions: [
          'Go to Settings > Notifications > Masajid Al Bahrain',
          'Enable "Allow Notifications"',
          'Choose notification style (Banners or Alerts)',
          'Enable "Sounds" and "Badges" if desired',
        ],
        priority: 10,
        estimatedTime: Duration(minutes: 3),
        difficulty: TroubleshootingDifficulty.easy,
      ),
    ];
  }

  /// Get generic troubleshooting steps
  List<TroubleshootingStep> _getGenericTroubleshootingSteps() {
    return [
      const TroubleshootingStep(
        id: 'restart_app',
        title: 'Restart the App',
        description: 'Close and reopen the app to refresh permissions',
        instructions: [
          'Close the Masajid Al Bahrain app completely',
          'Wait a few seconds',
          'Reopen the app',
          'Check if notifications are working',
        ],
        priority: 9,
        estimatedTime: Duration(minutes: 1),
        difficulty: TroubleshootingDifficulty.easy,
      ),
    ];
  }

  /// Get advanced troubleshooting steps
  List<TroubleshootingStep> _getAdvancedTroubleshootingSteps() {
    return [
      const TroubleshootingStep(
        id: 'clear_app_cache',
        title: 'Clear App Cache and Data',
        description: 'Reset app data to resolve persistent issues',
        instructions: [
          'Go to Settings > Apps > Masajid Al Bahrain',
          'Tap "Storage"',
          'Clear Cache first, then Clear Data if needed',
          'Restart the app and reconfigure settings',
        ],
        priority: 15,
        estimatedTime: Duration(minutes: 5),
        difficulty: TroubleshootingDifficulty.hard,
        warning: 'This will reset all app settings and preferences',
      ),
    ];
  }

  /// Get common solutions for permission issues
  List<TroubleshootingSolution> _getCommonSolutions(
    List<PermissionNotificationType> deniedTypes,
    PermissionIssueAnalysis analysis,
  ) {
    final solutions = <TroubleshootingSolution>[];

    // Add solutions based on denied types and analysis
    if (deniedTypes.contains(PermissionNotificationType.local)) {
      solutions.add(
        const TroubleshootingSolution(
          id: 'enable_notifications_solution',
          title: 'Enable Notifications in Settings',
          description: 'The most common solution for notification issues',
          steps: [
            'Open device Settings',
            'Navigate to Apps > Masajid Al Bahrain',
            'Enable all notification permissions',
          ],
          successRate: 0.85,
          difficulty: TroubleshootingDifficulty.easy,
        ),
      );
    }

    if (deniedTypes.contains(PermissionNotificationType.background)) {
      solutions.add(
        const TroubleshootingSolution(
          id: 'battery_optimization_solution',
          title: 'Disable Battery Optimization',
          description: 'Prevents system from killing background notifications',
          steps: [
            'Go to Battery settings',
            'Find Battery Optimization',
            'Exclude Masajid Al Bahrain from optimization',
          ],
          successRate: 0.75,
          difficulty: TroubleshootingDifficulty.medium,
        ),
      );
    }

    return solutions;
  }

  /// Get platform-specific guidance
  List<PlatformGuidance> _getPlatformSpecificGuidance(
    List<PermissionNotificationType> deniedTypes,
    PermissionIssueAnalysis analysis,
  ) {
    final guidance = <PlatformGuidance>[];

    if (Platform.isAndroid) {
      guidance.add(
        PlatformGuidance(
          platform: 'Android',
          version: Platform.operatingSystemVersion,
          guidance: 'Android notification permissions are managed through notification channels',
          specificSteps: [
            'Check notification channels in app settings',
            'Verify battery optimization settings',
            'Ensure exact alarm permissions for scheduled notifications',
          ],
          commonIssues: [
            'Battery optimization killing background notifications',
            'Notification channels disabled by user',
            'Exact alarm permissions required for Android 12+',
          ],
        ),
      );
    } else if (Platform.isIOS) {
      guidance.add(
        PlatformGuidance(
          platform: 'iOS',
          version: Platform.operatingSystemVersion,
          guidance: 'iOS requires explicit user consent for all notification types',
          specificSteps: [
            'Enable notifications in iOS Settings',
            'Configure notification style and sounds',
            'Check Focus/Do Not Disturb settings',
          ],
          commonIssues: [
            'Notifications disabled in iOS Settings',
            'Focus modes blocking notifications',
            'Provisional notifications not properly configured',
          ],
        ),
      );
    }

    return guidance;
  }

  /// Get local notification quick fixes
  List<QuickFix> _getLocalNotificationQuickFixes() {
    return [
      const QuickFix(
        id: 'enable_local_notifications_quick',
        title: 'Enable Local Notifications',
        description: 'Quick fix to enable basic notifications',
        action: 'Open Settings > Apps > Masajid Al Bahrain > Notifications',
        priority: 1,
        estimatedTime: Duration(minutes: 1),
      ),
    ];
  }

  /// Get push notification quick fixes
  List<QuickFix> _getPushNotificationQuickFixes() {
    return [
      const QuickFix(
        id: 'check_internet_quick',
        title: 'Check Internet Connection',
        description: 'Verify network connectivity for push notifications',
        action: 'Test internet connection and try again',
        priority: 2,
        estimatedTime: Duration(seconds: 30),
      ),
    ];
  }

  /// Get background notification quick fixes
  List<QuickFix> _getBackgroundNotificationQuickFixes() {
    return [
      const QuickFix(
        id: 'disable_battery_optimization_quick',
        title: 'Disable Battery Optimization',
        description: 'Prevent system from limiting background activity',
        action: 'Settings > Battery > Battery Optimization > Don\'t optimize',
        priority: 3,
        estimatedTime: Duration(minutes: 2),
      ),
    ];
  }

  /// Get critical notification quick fixes
  List<QuickFix> _getCriticalNotificationQuickFixes() {
    return [
      const QuickFix(
        id: 'enable_critical_alerts_quick',
        title: 'Enable Critical Alerts',
        description: 'Allow critical notifications to bypass Do Not Disturb',
        action: 'Settings > Notifications > Masajid Al Bahrain > Critical Alerts',
        priority: 4,
        estimatedTime: Duration(minutes: 1),
      ),
    ];
  }

  /// Get provisional notification quick fixes
  List<QuickFix> _getProvisionalNotificationQuickFixes() {
    return [
      const QuickFix(
        id: 'enable_provisional_quick',
        title: 'Enable Provisional Notifications',
        description: 'Allow quiet notifications in notification center',
        action: 'Settings > Notifications > Masajid Al Bahrain > Deliver Quietly',
        priority: 5,
        estimatedTime: Duration(minutes: 1),
      ),
    ];
  }

  /// Check system settings for issues
  Future<List<TroubleshootingIssue>> _checkSystemSettings(PermissionStatusReport status) async {
    final issues = <TroubleshootingIssue>[];

    // Check if notifications are globally disabled
    if (!status.permissionCheck.hasAllRequired) {
      issues.add(
        const TroubleshootingIssue(
          id: 'notifications_disabled',
          title: 'Notifications Disabled',
          description: 'App notifications are disabled in system settings',
          severity: TroubleshootingSeverity.high,
          category: TroubleshootingCategory.systemSettings,
          autoFixable: false,
        ),
      );
    }

    // Check for permanently denied permissions
    if (status.permissionCheck.permanentlyDeniedPermissions.isNotEmpty) {
      issues.add(
        const TroubleshootingIssue(
          id: 'permissions_permanently_denied',
          title: 'Permissions Permanently Denied',
          description: 'Some permissions are permanently denied and require manual intervention',
          severity: TroubleshootingSeverity.critical,
          category: TroubleshootingCategory.permissions,
          autoFixable: false,
        ),
      );
    }

    return issues;
  }

  /// Check app settings for issues
  Future<List<TroubleshootingIssue>> _checkAppSettings(PermissionStatusReport status) async {
    final issues = <TroubleshootingIssue>[];

    // Check app-specific notification settings
    final settings = await ref.read(unifiedNotificationSettingsProvider.future);

    if (!settings.generalSettings.globallyEnabled) {
      issues.add(
        const TroubleshootingIssue(
          id: 'app_notifications_disabled',
          title: 'App Notifications Disabled',
          description: 'Notifications are disabled in the app settings',
          severity: TroubleshootingSeverity.medium,
          category: TroubleshootingCategory.appSettings,
          autoFixable: true,
        ),
      );
    }

    return issues;
  }

  /// Check device compatibility
  Future<List<TroubleshootingIssue>> _checkDeviceCompatibility() async {
    final issues = <TroubleshootingIssue>[];

    // Check if device supports notifications
    if (kIsWeb) {
      issues.add(
        const TroubleshootingIssue(
          id: 'web_platform_limitations',
          title: 'Web Platform Limitations',
          description: 'Some notification features may be limited on web platforms',
          severity: TroubleshootingSeverity.low,
          category: TroubleshootingCategory.compatibility,
          autoFixable: false,
        ),
      );
    }

    return issues;
  }

  /// Check network connectivity
  Future<List<TroubleshootingIssue>> _checkNetworkConnectivity() async {
    final issues = <TroubleshootingIssue>[];

    // This would implement actual network connectivity checks
    // For now, return empty list as placeholder
    return issues;
  }

  /// Generate recommendations based on issues
  List<String> _generateRecommendations(List<TroubleshootingIssue> issues) {
    final recommendations = <String>[];

    for (final issue in issues) {
      switch (issue.severity) {
        case TroubleshootingSeverity.critical:
          recommendations.add('Immediate action required: ${issue.title}');
          break;
        case TroubleshootingSeverity.high:
          recommendations.add('High priority: ${issue.title}');
          break;
        case TroubleshootingSeverity.medium:
          recommendations.add('Consider addressing: ${issue.title}');
          break;
        case TroubleshootingSeverity.low:
          recommendations.add('Optional improvement: ${issue.title}');
          break;
      }
    }

    return recommendations;
  }

  /// Calculate diagnosis severity
  TroubleshootingSeverity _calculateDiagnosisSeverity(List<TroubleshootingIssue> issues) {
    if (issues.any((issue) => issue.severity == TroubleshootingSeverity.critical)) {
      return TroubleshootingSeverity.critical;
    } else if (issues.any((issue) => issue.severity == TroubleshootingSeverity.high)) {
      return TroubleshootingSeverity.high;
    } else if (issues.any((issue) => issue.severity == TroubleshootingSeverity.medium)) {
      return TroubleshootingSeverity.medium;
    } else {
      return TroubleshootingSeverity.low;
    }
  }

  /// Calculate issue severity
  TroubleshootingSeverity _calculateIssueSeverity(List<PermissionIssueType> issues) {
    if (issues.contains(PermissionIssueType.criticalAlertsDenied)) {
      return TroubleshootingSeverity.critical;
    } else if (issues.contains(PermissionIssueType.localNotificationsDenied) ||
        issues.contains(PermissionIssueType.pushNotificationsDenied)) {
      return TroubleshootingSeverity.high;
    } else if (issues.contains(PermissionIssueType.backgroundNotificationsDenied)) {
      return TroubleshootingSeverity.medium;
    } else {
      return TroubleshootingSeverity.low;
    }
  }

  /// Generate guide title
  String _generateGuideTitle(List<PermissionNotificationType> deniedTypes, PermissionIssueAnalysis analysis) {
    if (deniedTypes.isEmpty) {
      return 'General Notification Troubleshooting Guide';
    } else if (deniedTypes.length == 1) {
      return 'Troubleshooting ${_getTypeDisplayName(deniedTypes.first)} Issues';
    } else {
      return 'Troubleshooting Multiple Notification Permission Issues';
    }
  }

  /// Generate guide description
  String _generateGuideDescription(List<PermissionNotificationType> deniedTypes, PermissionIssueAnalysis analysis) {
    if (deniedTypes.isEmpty) {
      return 'This guide helps you resolve common notification issues and optimize your notification experience.';
    } else {
      final typeNames = deniedTypes.map(_getTypeDisplayName).join(', ');
      return 'This guide helps you resolve issues with $typeNames and restore full notification functionality.';
    }
  }

  /// Get display name for permission type
  String _getTypeDisplayName(PermissionNotificationType type) {
    switch (type) {
      case PermissionNotificationType.local:
        return 'Local Notifications';
      case PermissionNotificationType.push:
        return 'Push Notifications';
      case PermissionNotificationType.scheduled:
        return 'Scheduled Notifications';
      case PermissionNotificationType.background:
        return 'Background Notifications';
      case PermissionNotificationType.critical:
        return 'Critical Alerts';
      case PermissionNotificationType.provisional:
        return 'Provisional Notifications';
    }
  }

  /// Serialize permission status for support
  Map<String, dynamic> _serializePermissionStatus(PermissionStatusReport status) {
    return {
      'hasAllRequired': status.permissionCheck.hasAllRequired,
      'grantedPermissions': status.permissionCheck.grantedPermissions.map((e) => e.name).toList(),
      'deniedPermissions': status.permissionCheck.deniedPermissions.map((e) => e.name).toList(),
      'permanentlyDeniedPermissions': status.permissionCheck.permanentlyDeniedPermissions.map((e) => e.name).toList(),
      'overallHealth': status.overallHealth.name,
      'recommendations': status.recommendations,
    };
  }

  /// Gather device information
  Future<Map<String, dynamic>> _gatherDeviceInformation() async {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'locale': Platform.localeName,
      'isPhysicalDevice': !kIsWeb,
    };
  }

  /// Gather recent logs (placeholder)
  Future<List<String>> _gatherRecentLogs() async {
    // This would implement actual log gathering
    return ['Log gathering not implemented'];
  }

  /// Get app version (placeholder)
  Future<String> _getAppVersion() async {
    // This would get actual app version from package info
    return '1.0.0';
  }

  /// Get platform info
  Map<String, String> _getPlatformInfo() {
    return {'platform': Platform.operatingSystem, 'version': Platform.operatingSystemVersion, 'dart': Platform.version};
  }
}
