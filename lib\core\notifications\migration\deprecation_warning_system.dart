import 'dart:async';
import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'migration_tracking_service.dart';

/// Deprecation Warning System
///
/// **Task 4.1.2: Create deprecation warnings for old provider usage**
///
/// This system provides comprehensive deprecation warnings for legacy provider usage
/// following Context7 MCP patterns for developer guidance and migration assistance.
///
/// Features:
/// - Contextual deprecation warnings with migration guidance
/// - Visual deprecation indicators in debug mode
/// - Automated migration suggestions with code examples
/// - Severity-based warning levels
/// - Developer-friendly documentation links
/// - IDE integration for better developer experience
/// - Suppression mechanisms for gradual migration
/// - Analytics integration for tracking warning effectiveness
class DeprecationWarningSystem {
  static final DeprecationWarningSystem _instance = DeprecationWarningSystem._internal();
  factory DeprecationWarningSystem() => _instance;
  DeprecationWarningSystem._internal();

  final Set<String> _suppressedWarnings = {};
  final Map<String, DeprecationWarningConfig> _warningConfigs = {};
  final StreamController<DeprecationWarning> _warningController = StreamController.broadcast();

  /// Stream of deprecation warnings
  Stream<DeprecationWarning> get warnings => _warningController.stream;

  /// Initialize deprecation warning system
  ///
  /// **Context7 MCP Implementation:**
  /// - Single responsibility: Focused on deprecation warning management
  /// - Open/closed principle: Extensible for new warning types and configurations
  /// - Dependency inversion: Uses abstract warning interfaces
  /// - Interface segregation: Specific warning methods for different scenarios
  ///
  /// **Usage:**
  /// ```dart
  /// DeprecationWarningSystem().initialize();
  /// ```
  void initialize() {
    _setupDefaultWarningConfigs();
    _setupWarningListeners();
  }

  /// Show deprecation warning for legacy provider usage
  ///
  /// **Usage:**
  /// ```dart
  /// DeprecationWarningSystem().showDeprecationWarning(
  ///   'legacyPrayerNotificationProvider',
  ///   'enableNotifications',
  ///   severity: DeprecationSeverity.high,
  ///   stackTrace: StackTrace.current,
  /// );
  /// ```
  void showDeprecationWarning(
    String providerName,
    String methodName, {
    DeprecationSeverity severity = DeprecationSeverity.medium,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
    String? customMessage,
  }) {
    final warningKey = '$providerName.$methodName';
    
    // Check if warning is suppressed
    if (_suppressedWarnings.contains(warningKey)) {
      return;
    }

    // Get warning configuration
    final config = _warningConfigs[warningKey] ?? _getDefaultWarningConfig(providerName, methodName);

    // Create deprecation warning
    final warning = DeprecationWarning(
      providerName: providerName,
      methodName: methodName,
      severity: severity,
      message: customMessage ?? config.message,
      migrationGuide: config.migrationGuide,
      codeExample: config.codeExample,
      documentationUrl: config.documentationUrl,
      removalVersion: config.removalVersion,
      stackTrace: stackTrace,
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );

    // Track warning for analytics
    MigrationTrackingService().trackLegacyUsage(
      providerName,
      methodName,
      stackTrace: stackTrace,
      metadata: {
        ...metadata ?? {},
        'deprecationSeverity': severity.name,
        'warningShown': true,
      },
    );

    // Emit warning event
    _warningController.add(warning);

    // Display warning based on severity and environment
    _displayWarning(warning);
  }

  /// Show visual deprecation indicator in debug mode
  void showVisualDeprecationIndicator(
    BuildContext context,
    String providerName,
    String methodName, {
    Widget? child,
  }) {
    if (!kDebugMode) return;

    final warningKey = '$providerName.$methodName';
    if (_suppressedWarnings.contains(warningKey)) return;

    // Show visual indicator overlay
    _showDeprecationOverlay(context, providerName, methodName, child);
  }

  /// Suppress deprecation warning for specific provider/method
  void suppressWarning(String providerName, String methodName) {
    final warningKey = '$providerName.$methodName';
    _suppressedWarnings.add(warningKey);
  }

  /// Unsuppress deprecation warning
  void unsuppressWarning(String providerName, String methodName) {
    final warningKey = '$providerName.$methodName';
    _suppressedWarnings.remove(warningKey);
  }

  /// Configure custom deprecation warning
  void configureWarning(
    String providerName,
    String methodName,
    DeprecationWarningConfig config,
  ) {
    final warningKey = '$providerName.$methodName';
    _warningConfigs[warningKey] = config;
  }

  /// Get migration suggestions for deprecated provider
  List<MigrationSuggestion> getMigrationSuggestions(
    String providerName,
    String methodName,
  ) {
    return _generateMigrationSuggestions(providerName, methodName);
  }

  /// Generate deprecation report
  DeprecationReport generateDeprecationReport() {
    final migrationReport = MigrationTrackingService().getMigrationProgressReport();
    final activeWarnings = _getActiveWarnings();
    final suppressedWarnings = _suppressedWarnings.toList();

    return DeprecationReport(
      totalDeprecatedUsages: migrationReport.totalLegacyUsage,
      activeWarnings: activeWarnings,
      suppressedWarnings: suppressedWarnings,
      migrationProgress: migrationReport.migrationPercentage,
      recommendations: _generateDeprecationRecommendations(migrationReport),
      reportGeneratedAt: DateTime.now(),
    );
  }

  /// Dispose resources
  void dispose() {
    _warningController.close();
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// Setup default warning configurations
  void _setupDefaultWarningConfigs() {
    // Prayer notification provider warnings
    _warningConfigs['legacyPrayerNotificationProvider.enableNotifications'] = const DeprecationWarningConfig(
      message: 'legacyPrayerNotificationProvider.enableNotifications is deprecated',
      migrationGuide: 'Use unifiedNotificationSettingsProvider.notifier.updateGlobalSettings(globallyEnabled: true) instead',
      codeExample: '''
// Old (deprecated)
await ref.read(legacyPrayerNotificationProvider.notifier).enableNotifications();

// New (recommended)
await ref.read(unifiedNotificationSettingsProvider.notifier).updateGlobalSettings(
  globallyEnabled: true,
);''',
      documentationUrl: 'https://docs.masajid-albahrain.com/migration/prayer-notifications',
      removalVersion: 'v2.0.0',
    );

    _warningConfigs['legacyPrayerNotificationProvider.setPrayerNotification'] = const DeprecationWarningConfig(
      message: 'legacyPrayerNotificationProvider.setPrayerNotification is deprecated',
      migrationGuide: 'Use unifiedNotificationSettingsProvider.notifier.updatePrayerSettings instead',
      codeExample: '''
// Old (deprecated)
await ref.read(legacyPrayerNotificationProvider.notifier).setPrayerNotification('fajr', true);

// New (recommended)
await ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(
  PrayerType.fajr,
  enabled: true,
);''',
      documentationUrl: 'https://docs.masajid-albahrain.com/migration/prayer-settings',
      removalVersion: 'v2.0.0',
    );

    // Community notification provider warnings
    _warningConfigs['legacyCommunityNotificationProvider.enableCommunityNotifications'] = const DeprecationWarningConfig(
      message: 'legacyCommunityNotificationProvider.enableCommunityNotifications is deprecated',
      migrationGuide: 'Use unifiedNotificationSettingsProvider.notifier.updateCommunitySettings instead',
      codeExample: '''
// Old (deprecated)
await ref.read(legacyCommunityNotificationProvider.notifier).enableCommunityNotifications();

// New (recommended)
await ref.read(unifiedNotificationSettingsProvider.notifier).updateCommunitySettings(
  enabled: true,
);''',
      documentationUrl: 'https://docs.masajid-albahrain.com/migration/community-notifications',
      removalVersion: 'v2.0.0',
    );
  }

  /// Setup warning listeners
  void _setupWarningListeners() {
    // Listen to warnings and perform additional actions
    warnings.listen((warning) {
      _logWarningToAnalytics(warning);
      _updateWarningStatistics(warning);
    });
  }

  /// Get default warning configuration
  DeprecationWarningConfig _getDefaultWarningConfig(String providerName, String methodName) {
    return DeprecationWarningConfig(
      message: '$providerName.$methodName is deprecated',
      migrationGuide: 'Please migrate to the unified notification provider',
      codeExample: '''
// Old (deprecated)
ref.read($providerName.notifier).$methodName();

// New (recommended)
ref.read(unifiedNotificationSettingsProvider.notifier).updateSettings();''',
      documentationUrl: 'https://docs.masajid-albahrain.com/migration',
      removalVersion: 'v2.0.0',
    );
  }

  /// Display warning based on severity and environment
  void _displayWarning(DeprecationWarning warning) {
    if (kDebugMode) {
      _displayDebugWarning(warning);
    }

    if (kReleaseMode && warning.severity == DeprecationSeverity.critical) {
      _displayProductionWarning(warning);
    }
  }

  /// Display debug warning
  void _displayDebugWarning(DeprecationWarning warning) {
    final severityEmoji = _getSeverityEmoji(warning.severity);
    final severityColor = _getSeverityColor(warning.severity);

    final warningMessage = '''
$severityEmoji DEPRECATION WARNING [$severityColor${warning.severity.name.toUpperCase()}$_resetColor]

Provider: ${warning.providerName}
Method: ${warning.methodName}
Removal: ${warning.removalVersion}

${warning.message}

Migration Guide:
${warning.migrationGuide}

Code Example:
${warning.codeExample}

Documentation: ${warning.documentationUrl}

${warning.stackTrace != null ? 'Stack Trace:\n${warning.stackTrace}' : ''}
''';

    debugPrint(warningMessage);

    // Also log to developer console for better IDE integration
    developer.log(
      warning.message,
      name: 'DeprecationWarning',
      level: _getSeverityLogLevel(warning.severity),
      error: 'Deprecated: ${warning.providerName}.${warning.methodName}',
      stackTrace: warning.stackTrace,
    );
  }

  /// Display production warning (only for critical deprecations)
  void _displayProductionWarning(DeprecationWarning warning) {
    // In production, only show critical warnings and log them
    developer.log(
      'CRITICAL DEPRECATION: ${warning.providerName}.${warning.methodName} will be removed in ${warning.removalVersion}',
      name: 'CriticalDeprecation',
      level: 1000, // Error level
    );
  }

  /// Show deprecation overlay in debug mode
  void _showDeprecationOverlay(
    BuildContext context,
    String providerName,
    String methodName,
    Widget? child,
  ) {
    if (!kDebugMode) return;

    // Create overlay entry for deprecation indicator
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: 50,
        right: 20,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.9),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.warning, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                Text(
                  'DEPRECATED: $providerName',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () => overlayEntry.remove(),
                  child: const Icon(Icons.close, color: Colors.white, size: 16),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto-remove after 5 seconds
    Timer(const Duration(seconds: 5), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  /// Generate migration suggestions
  List<MigrationSuggestion> _generateMigrationSuggestions(
    String providerName,
    String methodName,
  ) {
    final suggestions = <MigrationSuggestion>[];

    // Generate specific suggestions based on provider and method
    if (providerName.contains('Prayer')) {
      suggestions.addAll(_getPrayerMigrationSuggestions(methodName));
    } else if (providerName.contains('Community')) {
      suggestions.addAll(_getCommunityMigrationSuggestions(methodName));
    }

    // Add general suggestions
    suggestions.addAll(_getGeneralMigrationSuggestions());

    return suggestions;
  }

  /// Get prayer-specific migration suggestions
  List<MigrationSuggestion> _getPrayerMigrationSuggestions(String methodName) {
    return [
      const MigrationSuggestion(
        title: 'Use Unified Prayer Settings',
        description: 'Replace legacy prayer notification methods with unified prayer settings API',
        priority: MigrationSuggestionPriority.high,
        effort: MigrationEffort.low,
        codeExample: '''
// Replace legacy method
await ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(
  PrayerType.fajr,
  enabled: true,
);''',
      ),
    ];
  }

  /// Get community-specific migration suggestions
  List<MigrationSuggestion> _getCommunityMigrationSuggestions(String methodName) {
    return [
      const MigrationSuggestion(
        title: 'Use Unified Community Settings',
        description: 'Replace legacy community notification methods with unified community settings API',
        priority: MigrationSuggestionPriority.high,
        effort: MigrationEffort.low,
        codeExample: '''
// Replace legacy method
await ref.read(unifiedNotificationSettingsProvider.notifier).updateCommunitySettings(
  enabled: true,
  announcements: true,
);''',
      ),
    ];
  }

  /// Get general migration suggestions
  List<MigrationSuggestion> _getGeneralMigrationSuggestions() {
    return [
      const MigrationSuggestion(
        title: 'Update Import Statements',
        description: 'Remove deprecated provider imports and add unified provider import',
        priority: MigrationSuggestionPriority.medium,
        effort: MigrationEffort.low,
        codeExample: '''
// Remove deprecated imports
// import 'legacy_providers.dart';

// Add unified import
import 'unified_notification_provider.dart';''',
      ),
      const MigrationSuggestion(
        title: 'Update Tests',
        description: 'Update unit tests to use unified provider instead of legacy providers',
        priority: MigrationSuggestionPriority.medium,
        effort: MigrationEffort.medium,
        codeExample: '''
// Update test setup
testWidgets('notification settings test', (tester) async {
  // Use unified provider in tests
  final container = ProviderContainer();
  final notifier = container.read(unifiedNotificationSettingsProvider.notifier);
  // ... test implementation
});''',
      ),
    ];
  }

  /// Get active warnings
  List<String> _getActiveWarnings() {
    final migrationReport = MigrationTrackingService().getMigrationProgressReport();
    return migrationReport.activeLegacyProviders;
  }

  /// Generate deprecation recommendations
  List<DeprecationRecommendation> _generateDeprecationRecommendations(
    MigrationProgressReport migrationReport,
  ) {
    final recommendations = <DeprecationRecommendation>[];

    if (migrationReport.migrationPercentage < 50) {
      recommendations.add(const DeprecationRecommendation(
        priority: DeprecationRecommendationPriority.high,
        title: 'Accelerate Migration',
        description: 'Migration progress is below 50%. Consider dedicating more resources to migration.',
        action: 'Review migration timeline and allocate additional development time',
      ));
    }

    if (migrationReport.activeLegacyProviders.length > 5) {
      recommendations.add(const DeprecationRecommendation(
        priority: DeprecationRecommendationPriority.medium,
        title: 'Focus on High-Usage Providers',
        description: 'Multiple legacy providers are still active. Focus on the most frequently used ones first.',
        action: 'Prioritize migration of providers with highest usage counts',
      ));
    }

    return recommendations;
  }

  /// Log warning to analytics
  void _logWarningToAnalytics(DeprecationWarning warning) {
    // This would integrate with analytics service
    developer.log(
      'Deprecation warning logged',
      name: 'Analytics',
      level: 800,
    );
  }

  /// Update warning statistics
  void _updateWarningStatistics(DeprecationWarning warning) {
    // This would update internal statistics
    // Implementation would depend on specific requirements
  }

  /// Get severity emoji
  String _getSeverityEmoji(DeprecationSeverity severity) {
    switch (severity) {
      case DeprecationSeverity.low:
        return 'ℹ️';
      case DeprecationSeverity.medium:
        return '⚠️';
      case DeprecationSeverity.high:
        return '🚨';
      case DeprecationSeverity.critical:
        return '💥';
    }
  }

  /// Get severity color code
  String _getSeverityColor(DeprecationSeverity severity) {
    switch (severity) {
      case DeprecationSeverity.low:
        return '\x1B[36m'; // Cyan
      case DeprecationSeverity.medium:
        return '\x1B[33m'; // Yellow
      case DeprecationSeverity.high:
        return '\x1B[31m'; // Red
      case DeprecationSeverity.critical:
        return '\x1B[35m'; // Magenta
    }
  }

  /// Reset color code
  String get _resetColor => '\x1B[0m';

  /// Get severity log level
  int _getSeverityLogLevel(DeprecationSeverity severity) {
    switch (severity) {
      case DeprecationSeverity.low:
        return 500; // Info
      case DeprecationSeverity.medium:
        return 900; // Warning
      case DeprecationSeverity.high:
        return 1000; // Error
      case DeprecationSeverity.critical:
        return 1200; // Severe
    }
  }
}

// ============================================================================
// DATA MODELS
// ============================================================================

/// Deprecation Warning
class DeprecationWarning {
  final String providerName;
  final String methodName;
  final DeprecationSeverity severity;
  final String message;
  final String migrationGuide;
  final String codeExample;
  final String documentationUrl;
  final String removalVersion;
  final StackTrace? stackTrace;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const DeprecationWarning({
    required this.providerName,
    required this.methodName,
    required this.severity,
    required this.message,
    required this.migrationGuide,
    required this.codeExample,
    required this.documentationUrl,
    required this.removalVersion,
    this.stackTrace,
    required this.timestamp,
    required this.metadata,
  });
}

/// Deprecation Severity
enum DeprecationSeverity {
  low,
  medium,
  high,
  critical,
}

/// Deprecation Warning Configuration
class DeprecationWarningConfig {
  final String message;
  final String migrationGuide;
  final String codeExample;
  final String documentationUrl;
  final String removalVersion;

  const DeprecationWarningConfig({
    required this.message,
    required this.migrationGuide,
    required this.codeExample,
    required this.documentationUrl,
    required this.removalVersion,
  });
}

/// Migration Suggestion
class MigrationSuggestion {
  final String title;
  final String description;
  final MigrationSuggestionPriority priority;
  final MigrationEffort effort;
  final String codeExample;

  const MigrationSuggestion({
    required this.title,
    required this.description,
    required this.priority,
    required this.effort,
    required this.codeExample,
  });
}

/// Migration Suggestion Priority
enum MigrationSuggestionPriority {
  low,
  medium,
  high,
}

/// Deprecation Report
class DeprecationReport {
  final int totalDeprecatedUsages;
  final List<String> activeWarnings;
  final List<String> suppressedWarnings;
  final int migrationProgress;
  final List<DeprecationRecommendation> recommendations;
  final DateTime reportGeneratedAt;

  const DeprecationReport({
    required this.totalDeprecatedUsages,
    required this.activeWarnings,
    required this.suppressedWarnings,
    required this.migrationProgress,
    required this.recommendations,
    required this.reportGeneratedAt,
  });
}

/// Deprecation Recommendation
class DeprecationRecommendation {
  final DeprecationRecommendationPriority priority;
  final String title;
  final String description;
  final String action;

  const DeprecationRecommendation({
    required this.priority,
    required this.title,
    required this.description,
    required this.action,
  });
}

/// Deprecation Recommendation Priority
enum DeprecationRecommendationPriority {
  low,
  medium,
  high,
}

/// Migration Effort (imported from migration_tracking_service.dart)
enum MigrationEffort {
  low,
  medium,
  high,
}
