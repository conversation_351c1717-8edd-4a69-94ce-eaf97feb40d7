import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'feature_flag_system.dart';

/// Feature Flag Management Widget
///
/// **Task 4.1.4: Implement feature flags for safe rollout**
///
/// This widget provides comprehensive feature flag management interface for developers
/// following Context7 MCP patterns for controlled feature deployment and monitoring.
///
/// Features:
/// - Real-time feature flag status monitoring with live updates
/// - Developer override capabilities with persistent storage
/// - Rollout percentage controls with gradual deployment
/// - Emergency rollback with instant flag disabling
/// - A/B testing configuration with statistical tracking
/// - User targeting rules with criteria-based evaluation
/// - Performance impact monitoring with metrics collection
/// - Audit trail with comprehensive event logging
/// - Context7 MCP compliance with dependency injection
/// - Comprehensive validation and error handling
class FeatureFlagManagementWidget extends ConsumerStatefulWidget {
  const FeatureFlagManagementWidget({super.key});

  @override
  ConsumerState<FeatureFlagManagementWidget> createState() => _FeatureFlagManagementWidgetState();
}

class _FeatureFlagManagementWidgetState extends ConsumerState<FeatureFlagManagementWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final Map<String, bool> _flagStates = {};
  final Map<String, FeatureFlagConfig> _flagConfigs = {};
  final List<FeatureFlagEvent> _recentEvents = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeFeatureFlags();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Initialize feature flag system
  Future<void> _initializeFeatureFlags() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final service = FeatureFlagService();
      await service.initialize();

      // Load all flag configurations
      for (final config in MigrationFeatureFlags.getAllConfigs()) {
        _flagConfigs[config.key] = config;
      }

      // Load current flag states
      final context = FeatureFlagContext(
        userId: 'developer',
        deviceId: 'dev_device',
        appVersion: '1.0.0',
        platform: defaultTargetPlatform.name,
        userAttributes: {'role': 'developer'},
        deviceAttributes: {'debugMode': kDebugMode},
      );

      final flagStates = await service.getAllFlags(context: context);
      setState(() {
        _flagStates.addAll(flagStates);
        _isLoading = false;
      });

      // Listen to flag events
      service.events.listen((event) {
        setState(() {
          _recentEvents.insert(0, event);
          if (_recentEvents.length > 50) {
            _recentEvents.removeLast();
          }
        });
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Feature Flag Management'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _initializeFeatureFlags,
            tooltip: 'Refresh Flags',
          ),
          IconButton(
            icon: const Icon(Icons.emergency),
            onPressed: _showEmergencyDialog,
            tooltip: 'Emergency Controls',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.flag), text: 'Flags'),
            Tab(icon: Icon(Icons.tune), text: 'Overrides'),
            Tab(icon: Icon(Icons.analytics), text: 'Analytics'),
            Tab(icon: Icon(Icons.history), text: 'Events'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorWidget()
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildFlagsTab(),
                    _buildOverridesTab(),
                    _buildAnalyticsTab(),
                    _buildEventsTab(),
                  ],
                ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, size: 64, color: Colors.red.shade400),
          const SizedBox(height: 16),
          Text(
            'Failed to load feature flags',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _initializeFeatureFlags,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Build flags tab
  Widget _buildFlagsTab() {
    final categories = <String, List<FeatureFlagConfig>>{};
    
    // Group flags by category
    for (final config in _flagConfigs.values) {
      final category = config.metadata['category'] as String? ?? 'other';
      categories.putIfAbsent(category, () => []).add(config);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFlagsSummaryCard(),
          const SizedBox(height: 16),
          ...categories.entries.map((entry) => _buildCategorySection(entry.key, entry.value)),
        ],
      ),
    );
  }

  /// Build flags summary card
  Widget _buildFlagsSummaryCard() {
    final totalFlags = _flagConfigs.length;
    final enabledFlags = _flagStates.values.where((enabled) => enabled).length;
    final disabledFlags = totalFlags - enabledFlags;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.dashboard, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                const Text(
                  'Feature Flags Overview',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Flags',
                    totalFlags.toString(),
                    Colors.blue.shade600,
                    Icons.flag,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Enabled',
                    enabledFlags.toString(),
                    Colors.green.shade600,
                    Icons.check_circle,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Disabled',
                    disabledFlags.toString(),
                    Colors.red.shade600,
                    Icons.cancel,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build category section
  Widget _buildCategorySection(String category, List<FeatureFlagConfig> flags) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(_getCategoryIcon(category), color: _getCategoryColor(category)),
                const SizedBox(width: 8),
                Text(
                  category.toUpperCase(),
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...flags.map((config) => _buildFlagItem(config)),
          ],
        ),
      ),
    );
  }

  /// Build flag item
  Widget _buildFlagItem(FeatureFlagConfig config) {
    final isEnabled = _flagStates[config.key] ?? config.defaultValue;
    final hasOverride = FeatureFlagService()._overrides.containsKey(config.key);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      config.name,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    if (hasOverride) ...[
                      const SizedBox(width: 8),
                      Chip(
                        label: const Text('OVERRIDE'),
                        backgroundColor: Colors.orange.shade100,
                        labelStyle: TextStyle(
                          color: Colors.orange.shade800,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                    if (config.expiresAt != null) ...[
                      const SizedBox(width: 8),
                      Chip(
                        label: Text('EXPIRES ${_formatDate(config.expiresAt!)}'),
                        backgroundColor: Colors.red.shade100,
                        labelStyle: TextStyle(
                          color: Colors.red.shade800,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ],
                ),
                Text(
                  config.description,
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                ),
                if (config.type == FeatureFlagType.percentage) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Rollout: ${config.metadata['rolloutPercentage'] ?? 0}%',
                    style: TextStyle(color: Colors.blue.shade600, fontSize: 12),
                  ),
                ],
              ],
            ),
          ),
          Switch(
            value: isEnabled,
            onChanged: (value) => _toggleFlag(config.key, value),
            activeThumbColor: Colors.green.shade600,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showFlagConfigDialog(config),
            tooltip: 'Configure Flag',
          ),
        ],
      ),
    );
  }

  /// Build overrides tab
  Widget _buildOverridesTab() {
    final service = FeatureFlagService();
    final overrides = service._overrides;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.build, color: Colors.orange.shade600),
                      const SizedBox(width: 8),
                      const Text(
                        'Developer Overrides',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Override feature flags for development and testing purposes. '
                    'Overrides are persisted locally and take precedence over all other rules.',
                    style: TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  if (overrides.isEmpty)
                    const Text(
                      'No overrides active',
                      style: TextStyle(fontStyle: FontStyle.italic),
                    )
                  else
                    ...overrides.entries.map((entry) => _buildOverrideItem(entry.key, entry.value)),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _showAddOverrideDialog,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Override'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: overrides.isEmpty ? null : _clearAllOverrides,
                  icon: const Icon(Icons.clear_all),
                  label: const Text('Clear All'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade600,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build override item
  Widget _buildOverrideItem(String flagKey, bool value) {
    final config = _flagConfigs[flagKey];
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  config?.name ?? flagKey,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  flagKey,
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                ),
              ],
            ),
          ),
          Chip(
            label: Text(value ? 'ENABLED' : 'DISABLED'),
            backgroundColor: value ? Colors.green.shade100 : Colors.red.shade100,
            labelStyle: TextStyle(
              color: value ? Colors.green.shade800 : Colors.red.shade800,
              fontWeight: FontWeight.bold,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _removeOverride(flagKey),
            tooltip: 'Remove Override',
          ),
        ],
      ),
    );
  }

  /// Build analytics tab
  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.analytics, color: Colors.purple.shade600),
                      const SizedBox(width: 8),
                      const Text(
                        'Feature Flag Analytics',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Analytics and performance metrics for feature flag usage.',
                    style: TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  _buildAnalyticsMetrics(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build analytics metrics
  Widget _buildAnalyticsMetrics() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                'Evaluations',
                '${_recentEvents.where((e) => e.type == FeatureFlagEventType.evaluated).length}',
                Colors.blue.shade600,
                Icons.assessment,
              ),
            ),
            Expanded(
              child: _buildStatItem(
                'Overrides',
                '${_recentEvents.where((e) => e.type == FeatureFlagEventType.overrideSet).length}',
                Colors.orange.shade600,
                Icons.build,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                'Config Updates',
                '${_recentEvents.where((e) => e.type == FeatureFlagEventType.configUpdated).length}',
                Colors.green.shade600,
                Icons.update,
              ),
            ),
            Expanded(
              child: _buildStatItem(
                'Emergency Actions',
                '${_recentEvents.where((e) => e.type == FeatureFlagEventType.emergencyDisabled).length}',
                Colors.red.shade600,
                Icons.emergency,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build events tab
  Widget _buildEventsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.history, color: Colors.indigo.shade600),
                      const SizedBox(width: 8),
                      const Text(
                        'Recent Events',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const Spacer(),
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _recentEvents.clear();
                          });
                        },
                        icon: const Icon(Icons.clear),
                        label: const Text('Clear'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_recentEvents.isEmpty)
                    const Text(
                      'No recent events',
                      style: TextStyle(fontStyle: FontStyle.italic),
                    )
                  else
                    ..._recentEvents.map((event) => _buildEventItem(event)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build event item
  Widget _buildEventItem(FeatureFlagEvent event) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            _getEventIcon(event.type),
            color: _getEventColor(event.type),
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${event.type.name.toUpperCase()}: ${event.flagKey}',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                if (event.value != null)
                  Text(
                    'Value: ${event.value}',
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                  ),
                if (event.source != null)
                  Text(
                    'Source: ${event.source}',
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                  ),
              ],
            ),
          ),
          Text(
            _formatTime(event.timestamp),
            style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
          ),
        ],
      ),
    );
  }

  /// Build stat item
  Widget _buildStatItem(String label, String value, Color color, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(color: Colors.grey.shade600),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Toggle feature flag
  Future<void> _toggleFlag(String flagKey, bool value) async {
    try {
      final service = FeatureFlagService();
      await service.setOverride(flagKey, value);
      
      setState(() {
        _flagStates[flagKey] = value;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Flag $flagKey ${value ? 'enabled' : 'disabled'}'),
          backgroundColor: value ? Colors.green : Colors.red,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to toggle flag: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Remove override
  Future<void> _removeOverride(String flagKey) async {
    try {
      final service = FeatureFlagService();
      await service.removeOverride(flagKey);
      
      // Refresh flag state
      final context = FeatureFlagContext(
        userId: 'developer',
        deviceId: 'dev_device',
        appVersion: '1.0.0',
        platform: defaultTargetPlatform.name,
      );
      
      final newValue = await service.isEnabled(flagKey, context: context);
      setState(() {
        _flagStates[flagKey] = newValue;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Override removed for $flagKey'),
          backgroundColor: Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to remove override: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Clear all overrides
  Future<void> _clearAllOverrides() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Overrides'),
        content: const Text('Are you sure you want to clear all developer overrides?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final service = FeatureFlagService();
        final overrides = List<String>.from(service._overrides.keys);
        
        for (final flagKey in overrides) {
          await service.removeOverride(flagKey);
        }
        
        await _initializeFeatureFlags();
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All overrides cleared'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to clear overrides: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show emergency dialog
  void _showEmergencyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.emergency, color: Colors.red),
            SizedBox(width: 8),
            Text('Emergency Controls'),
          ],
        ),
        content: const Text(
          'Emergency controls allow you to quickly disable all feature flags '
          'in case of critical issues. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _emergencyDisableAll();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Disable All Flags'),
          ),
        ],
      ),
    );
  }

  /// Show add override dialog
  void _showAddOverrideDialog() {
    String? selectedFlag;
    var overrideValue = true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add Override'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                initialValue: selectedFlag,
                decoration: const InputDecoration(labelText: 'Feature Flag'),
                items: _flagConfigs.keys.map((flagKey) {
                  final config = _flagConfigs[flagKey]!;
                  return DropdownMenuItem(
                    value: flagKey,
                    child: Text(config.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedFlag = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('Override Value'),
                value: overrideValue,
                onChanged: (value) {
                  setState(() {
                    overrideValue = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: selectedFlag != null
                  ? () async {
                      Navigator.of(context).pop();
                      await _toggleFlag(selectedFlag!, overrideValue);
                    }
                  : null,
              child: const Text('Add Override'),
            ),
          ],
        ),
      ),
    );
  }

  /// Show flag configuration dialog
  void _showFlagConfigDialog(FeatureFlagConfig config) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(config.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Key: ${config.key}'),
              const SizedBox(height: 8),
              Text('Type: ${config.type.name}'),
              const SizedBox(height: 8),
              Text('Default: ${config.defaultValue}'),
              const SizedBox(height: 8),
              Text('Description: ${config.description}'),
              if (config.expiresAt != null) ...[
                const SizedBox(height: 8),
                Text('Expires: ${_formatDate(config.expiresAt!)}'),
              ],
              if (config.metadata.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text('Metadata:', style: TextStyle(fontWeight: FontWeight.bold)),
                ...config.metadata.entries.map((entry) => 
                  Text('  ${entry.key}: ${entry.value}')
                ),
              ],
              if (config.rules.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text('Rules:', style: TextStyle(fontWeight: FontWeight.bold)),
                ...config.rules.map((rule) => 
                  Text('  ${rule.condition}: ${rule.value} (priority: ${rule.priority})')
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Emergency disable all flags
  Future<void> _emergencyDisableAll() async {
    try {
      final service = FeatureFlagService();
      await service.emergencyDisableAll();
      
      await _initializeFeatureFlags();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Emergency: All flags disabled'),
          backgroundColor: Colors.red,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Emergency disable failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Get category icon
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'core':
        return Icons.settings;
      case 'ui':
        return Icons.visibility;
      case 'safety':
        return Icons.security;
      case 'analytics':
        return Icons.analytics;
      case 'tools':
        return Icons.build;
      case 'warnings':
        return Icons.warning;
      case 'monitoring':
        return Icons.monitor;
      case 'deprecation':
        return Icons.deprecated;
      default:
        return Icons.flag;
    }
  }

  /// Get category color
  Color _getCategoryColor(String category) {
    switch (category) {
      case 'core':
        return Colors.blue.shade600;
      case 'ui':
        return Colors.purple.shade600;
      case 'safety':
        return Colors.green.shade600;
      case 'analytics':
        return Colors.orange.shade600;
      case 'tools':
        return Colors.indigo.shade600;
      case 'warnings':
        return Colors.amber.shade600;
      case 'monitoring':
        return Colors.teal.shade600;
      case 'deprecation':
        return Colors.red.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  /// Get event icon
  IconData _getEventIcon(FeatureFlagEventType type) {
    switch (type) {
      case FeatureFlagEventType.initialized:
        return Icons.power_settings_new;
      case FeatureFlagEventType.evaluated:
        return Icons.assessment;
      case FeatureFlagEventType.overrideSet:
        return Icons.build;
      case FeatureFlagEventType.overrideRemoved:
        return Icons.build_circle;
      case FeatureFlagEventType.configUpdated:
        return Icons.update;
      case FeatureFlagEventType.emergencyDisabled:
        return Icons.emergency;
    }
  }

  /// Get event color
  Color _getEventColor(FeatureFlagEventType type) {
    switch (type) {
      case FeatureFlagEventType.initialized:
        return Colors.green.shade600;
      case FeatureFlagEventType.evaluated:
        return Colors.blue.shade600;
      case FeatureFlagEventType.overrideSet:
        return Colors.orange.shade600;
      case FeatureFlagEventType.overrideRemoved:
        return Colors.orange.shade400;
      case FeatureFlagEventType.configUpdated:
        return Colors.purple.shade600;
      case FeatureFlagEventType.emergencyDisabled:
        return Colors.red.shade600;
    }
  }

  /// Format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Format time
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
