import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:masajid_albahrain/features/prayer_times/presentation/providers/prayer_times_provider.dart'
    as prayer_provider;
import 'package:masajid_albahrain/core/sync/services/progress_tracking_service.dart';
import 'package:masajid_albahrain/core/utils/logger.dart';
import 'package:masajid_albahrain/core/notifications/models/notification_settings.dart';
import 'package:masajid_albahrain/core/notifications/models/unified_notification_settings.dart';
import 'package:masajid_albahrain/core/notifications/services/background_sync_notification_service.dart';
import 'package:masajid_albahrain/core/notifications/services/notification_analytics_service.dart';
import 'package:masajid_albahrain/core/notifications/services/notification_channel_manager.dart';
import 'package:masajid_albahrain/core/notifications/services/notification_scheduler.dart';
import 'package:masajid_albahrain/core/notifications/services/notification_service.dart';
import 'package:masajid_albahrain/core/notifications/services/prayer_notification_service.dart';
import 'package:masajid_albahrain/core/notifications/services/system_alert_notification_service.dart';
// Context7 MCP: Import unified notification provider with alias to avoid conflicts
import 'package:masajid_albahrain/core/notifications/providers/unified_notification_provider.dart' as unified;

part 'prayer_notification_provider.g.dart';

/// Prayer Notification Service Provider
///
/// Provides the prayer notification service instance following Context7 MCP best practices.
/// Context7 MCP: Integrates with unified notification provider dependency injection system.
@riverpod
Future<PrayerNotificationService> prayerNotificationService(Ref ref) async {
  // Context7 MCP: Get notification service from unified dependency injection container
  // This follows dependency inversion principle by using the unified service dependencies
  final dependencies = await ref.watch(unified.notificationServiceDependenciesProvider.future);
  final prayerTimesService = ref.watch(prayer_provider.prayerTimesServiceProvider);

  // Context7 MCP: Create service using dependency injection pattern
  final service = PrayerNotificationService(
    notificationService: dependencies.notificationService,
    prayerTimesService: prayerTimesService,
  );

  // Context7 MCP: Initialize service following proper lifecycle management
  await service.initialize();

  // Context7 MCP: Set up proper disposal following resource management best practices
  ref.onDispose(() async {
    try {
      await service.dispose();
      AppLogger.debug('✅ PrayerNotificationService disposed successfully');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to dispose PrayerNotificationService', e, stackTrace);
    }
  });

  return service;
}

/// Notification Service Provider
///
/// Provides the core notification service instance.
@riverpod
NotificationService notificationService(Ref ref) {
  final service = NotificationService();

  // Initialize the service
  ref.onDispose(() async {
    await service.dispose();
  });

  return service;
}

/// Progress Tracking Service Provider
///
/// Provides the progress tracking service instance.
@riverpod
ProgressTrackingService progressTrackingService(Ref ref) {
  final service = ProgressTrackingService();

  // Initialize the service
  ref.onDispose(() async {
    await service.dispose();
  });

  return service;
}

/// Prayer Notification Settings Provider
///
/// Manages prayer notification settings with automatic persistence and reactivity.
/// This provider follows Context7 MCP best practices for state management.
@riverpod
class PrayerNotificationSettingsNotifier extends _$PrayerNotificationSettingsNotifier {
  @override
  PrayerNotificationSettings build() {
    // Initialize with default settings
    // The actual settings will be loaded asynchronously
    return PrayerNotificationSettings.defaultSettings();
  }

  /// Initialize and load settings from storage
  Future<void> initialize() async {
    try {
      AppLogger.info('🔔 Initializing prayer notification settings');

      final service = ref.read(prayerNotificationServiceProvider);
      await service.initialize();

      // Update state with loaded settings
      state = service.settings;

      AppLogger.info('✅ Prayer notification settings initialized');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize prayer notification settings', e, stackTrace);
    }
  }

  /// Update prayer notification settings
  Future<void> updateSettings(PrayerNotificationSettings newSettings) async {
    try {
      AppLogger.info('⚙️ Updating prayer notification settings');

      final service = ref.read(prayerNotificationServiceProvider);
      await service.updateSettings(newSettings);

      // Update state
      state = newSettings;

      AppLogger.info('✅ Prayer notification settings updated');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to update prayer notification settings', e, stackTrace);
    }
  }

  /// Enable or disable global prayer notifications
  Future<void> setGloballyEnabled(bool enabled) async {
    await updateSettings(state.copyWith(globallyEnabled: enabled));
  }

  /// Enable or disable a specific prayer
  Future<void> setPrayerEnabled(String prayerName, bool enabled) async {
    final currentPrayerSettings = state.prayerSettings[prayerName] ?? const PrayerSettings();

    final updatedPrayerSettings = currentPrayerSettings.copyWith(enabled: enabled);

    await updateSettings(state.copyWith(prayerSettings: {...state.prayerSettings, prayerName: updatedPrayerSettings}));
  }

  /// Update reminder minutes for a specific prayer
  Future<void> setPrayerReminderMinutes(String prayerName, List<int> reminderMinutes) async {
    final currentPrayerSettings = state.prayerSettings[prayerName] ?? const PrayerSettings();

    final updatedPrayerSettings = currentPrayerSettings.copyWith(reminderMinutes: reminderMinutes);

    await updateSettings(state.copyWith(prayerSettings: {...state.prayerSettings, prayerName: updatedPrayerSettings}));
  }

  /// Enable or disable sound for a specific prayer
  Future<void> setPrayerSoundEnabled(String prayerName, bool enabled) async {
    final currentPrayerSettings = state.prayerSettings[prayerName] ?? const PrayerSettings();

    final updatedPrayerSettings = currentPrayerSettings.copyWith(enableSound: enabled);

    await updateSettings(state.copyWith(prayerSettings: {...state.prayerSettings, prayerName: updatedPrayerSettings}));
  }

  /// Enable or disable vibration for a specific prayer
  Future<void> setPrayerVibrationEnabled(String prayerName, bool enabled) async {
    final currentPrayerSettings = state.prayerSettings[prayerName] ?? const PrayerSettings();

    final updatedPrayerSettings = currentPrayerSettings.copyWith(enableVibration: enabled);

    await updateSettings(state.copyWith(prayerSettings: {...state.prayerSettings, prayerName: updatedPrayerSettings}));
  }

  /// Enable or disable daily summary
  Future<void> setDailySummaryEnabled(bool enabled) async {
    await updateSettings(state.copyWith(enableDailySummary: enabled));
  }

  /// Set daily summary hour
  Future<void> setDailySummaryHour(int hour) async {
    await updateSettings(state.copyWith(dailySummaryHour: hour));
  }

  /// Enable or disable post-prayer reminders
  Future<void> setPostPrayerRemindersEnabled(bool enabled) async {
    await updateSettings(state.copyWith(enablePostPrayerReminders: enabled));
  }

  /// Update default reminder minutes
  Future<void> setDefaultReminderMinutes(List<int> reminderMinutes) async {
    await updateSettings(state.copyWith(defaultReminderMinutes: reminderMinutes));
  }

  /// Update default follow-up minutes
  Future<void> setDefaultFollowUpMinutes(List<int> followUpMinutes) async {
    await updateSettings(state.copyWith(defaultFollowUpMinutes: followUpMinutes));
  }
}

/// Prayer Notification Scheduler Provider
///
/// Automatically schedules prayer notifications based on prayer times and settings.
/// This provider listens to changes in prayer times and notification settings
/// and reschedules notifications accordingly.
@riverpod
void prayerNotificationScheduler(Ref ref) {
  // Listen to prayer times changes
  ref.listen(prayer_provider.allPrayerTimesProvider, (previous, next) {
    AppLogger.debug('🔔 Prayer times changed, rescheduling notifications');
    _schedulePrayerNotifications(ref);
  });

  // Listen to notification settings changes
  ref.listen(prayerNotificationSettingsNotifierProvider, (previous, next) {
    AppLogger.debug('🔔 Prayer notification settings changed, rescheduling notifications');

    // If notifications were disabled, cancel all notifications
    if (!next.globallyEnabled && previous?.globallyEnabled == true) {
      _cancelAllPrayerNotifications(ref);
      return;
    }

    // If notifications were enabled or settings changed, schedule notifications
    if (next.globallyEnabled) {
      _schedulePrayerNotifications(ref);
    }
  });

  // Listen to user location changes
  ref.listen(prayer_provider.userLocationProvider, (previous, next) {
    AppLogger.debug('🔔 User location changed, rescheduling notifications');
    _schedulePrayerNotifications(ref);
  });
}

/// Schedule prayer notifications
void _schedulePrayerNotifications(Ref ref) {
  try {
    final settings = ref.read(prayerNotificationSettingsNotifierProvider);
    final location = ref.read(prayer_provider.userLocationProvider);
    final service = ref.read(prayerNotificationServiceProvider);

    // Only schedule if globally enabled
    if (!settings.globallyEnabled) {
      AppLogger.debug('🔕 Prayer notifications globally disabled');
      return;
    }

    // Schedule notifications for today and tomorrow
    final today = DateTime.now();
    final tomorrow = today.add(const Duration(days: 1));

    // Schedule for today
    service.********************************(date: today, latitude: location.latitude, longitude: location.longitude);

    // Schedule for tomorrow
    service.********************************(
      date: tomorrow,
      latitude: location.latitude,
      longitude: location.longitude,
    );

    AppLogger.debug('✅ Prayer notifications scheduled for today and tomorrow');
  } on Exception catch (e, stackTrace) {
    AppLogger.error('❌ Failed to schedule prayer notifications', e, stackTrace);
  }
}

/// Cancel all prayer notifications
void _cancelAllPrayerNotifications(Ref ref) {
  try {
    final service = ref.read(prayerNotificationServiceProvider);
    service.cancelAllPrayerNotifications();

    AppLogger.debug('✅ All prayer notifications cancelled');
  } on Exception catch (e, stackTrace) {
    AppLogger.error('❌ Failed to cancel prayer notifications', e, stackTrace);
  }
}

/// Prayer Notification Statistics Provider
///
/// Provides statistics and analytics for prayer notifications.
@riverpod
Map<String, dynamic> prayerNotificationStatistics(Ref ref) {
  final service = ref.watch(prayerNotificationServiceProvider);
  return service.getPrayerNotificationStatistics();
}

/// Pending Prayer Notifications Provider
///
/// Provides a list of pending prayer notifications.
/// Context7 MCP: Integrates with unified notification provider dependency injection system.
@riverpod
Future<List<PendingNotificationRequest>> pendingPrayerNotifications(Ref ref) async {
  // Context7 MCP: Get notification service from unified dependency injection container
  final dependencies = await ref.watch(unified.notificationServiceDependenciesProvider.future);
  final pendingNotifications = await dependencies.notificationService.getPendingNotifications();

  // Filter for prayer notifications (IDs starting from 10000)
  return pendingNotifications.where((notification) => notification.id >= 10000).toList();
}

/// Prayer Notification Initialization Provider
///
/// Handles the initialization of prayer notification services.
/// Context7 MCP: Integrates with unified notification provider dependency injection system.
@riverpod
Future<void> initializePrayerNotifications(Ref ref) async {
  try {
    AppLogger.info('🚀 Initializing prayer notification system');

    // Context7 MCP: Initialize unified notification dependencies
    // This ensures all notification services are properly initialized before proceeding
    await ref.read(unified.notificationServiceDependenciesProvider.future);
    AppLogger.debug('✅ Unified notification dependencies initialized');

    // Initialize prayer notification settings
    final settingsNotifier = ref.read(prayerNotificationSettingsNotifierProvider.notifier);
    await settingsNotifier.initialize();

    // Initialize sync notification settings
    final syncSettingsNotifier = ref.read(syncNotificationSettingsNotifierProvider.notifier);
    await syncSettingsNotifier.initialize();

    // Start the scheduler
    ref.read(prayerNotificationSchedulerProvider);

    AppLogger.info('✅ Prayer notification system initialized successfully');
  } on Exception catch (e, stackTrace) {
    AppLogger.error('❌ Failed to initialize prayer notification system', e, stackTrace);
    rethrow;
  }
}

/// Background Sync Notification Service Provider
///
/// Provides the background sync notification service instance following Context7 MCP best practices.
/// Context7 MCP: Integrates with unified notification provider dependency injection system.
@riverpod
Future<BackgroundSyncNotificationService> backgroundSyncNotificationService(Ref ref) async {
  // Context7 MCP: Get notification service from unified dependency injection container
  // This follows dependency inversion principle by using the unified service dependencies
  final dependencies = await ref.watch(unified.notificationServiceDependenciesProvider.future);
  final progressTrackingService = ref.watch(progressTrackingServiceProvider);

  // Context7 MCP: Create service using dependency injection pattern
  final service = BackgroundSyncNotificationService(
    notificationService: dependencies.notificationService,
    progressTrackingService: progressTrackingService,
  );

  // Context7 MCP: Initialize service following proper lifecycle management
  await service.initialize();

  // Context7 MCP: Set up proper disposal following resource management best practices
  ref.onDispose(() async {
    try {
      await service.dispose();
      AppLogger.debug('✅ BackgroundSyncNotificationService disposed successfully');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to dispose BackgroundSyncNotificationService', e, stackTrace);
    }
  });

  return service;
}

/// Sync Notification Settings Provider
///
/// Manages sync notification settings with automatic persistence and reactivity.
@riverpod
class SyncNotificationSettingsNotifier extends _$SyncNotificationSettingsNotifier {
  @override
  SyncNotificationSettings build() {
    // Initialize with default settings
    return SyncNotificationSettings.defaultSettings();
  }

  /// Initialize and load settings from storage
  Future<void> initialize() async {
    try {
      AppLogger.info('🔄 Initializing sync notification settings');

      final service = ref.read(backgroundSyncNotificationServiceProvider);
      await service.initialize();

      // Update state with loaded settings
      state = service.settings;

      AppLogger.info('✅ Sync notification settings initialized');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize sync notification settings', e, stackTrace);
    }
  }

  /// Update sync notification settings
  Future<void> updateSettings(SyncNotificationSettings newSettings) async {
    try {
      AppLogger.info('⚙️ Updating sync notification settings');

      final service = ref.read(backgroundSyncNotificationServiceProvider);
      await service.updateSettings(newSettings);

      // Update state
      state = newSettings;

      AppLogger.info('✅ Sync notification settings updated');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to update sync notification settings', e, stackTrace);
    }
  }

  /// Enable or disable progress notifications
  Future<void> setProgressNotificationsEnabled(bool enabled) async {
    await updateSettings(state.copyWith(showProgressNotifications: enabled));
  }

  /// Enable or disable completion notifications
  Future<void> setCompletionNotificationsEnabled(bool enabled) async {
    await updateSettings(state.copyWith(showCompletionNotifications: enabled));
  }

  /// Enable or disable error notifications
  Future<void> setErrorNotificationsEnabled(bool enabled) async {
    await updateSettings(state.copyWith(showErrorNotifications: enabled));
  }

  /// Set progress update threshold
  Future<void> setProgressUpdateThreshold(int threshold) async {
    await updateSettings(state.copyWith(progressUpdateThreshold: threshold));
  }

  /// Enable or disable auto-dismiss for completion notifications
  Future<void> setAutoDismissCompletion(bool enabled) async {
    await updateSettings(state.copyWith(autoDismissCompletion: enabled));
  }
}

/// System Alert Notification Service Provider
///
/// Provides the system alert notification service instance following Context7 MCP best practices.
/// Context7 MCP: Integrates with unified notification provider dependency injection system.
@riverpod
Future<SystemAlertNotificationService> systemAlertNotificationService(Ref ref) async {
  // Context7 MCP: Get notification service from unified dependency injection container
  // This follows dependency inversion principle by using the unified service dependencies
  final dependencies = await ref.watch(unified.notificationServiceDependenciesProvider.future);

  // Context7 MCP: Create service using dependency injection pattern
  final service = SystemAlertNotificationService(notificationService: dependencies.notificationService);

  // Context7 MCP: Initialize service following proper lifecycle management
  await service.initialize();

  // Context7 MCP: Set up proper disposal following resource management best practices
  ref.onDispose(() async {
    try {
      await service.dispose();
      AppLogger.debug('✅ SystemAlertNotificationService disposed successfully');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to dispose SystemAlertNotificationService', e, stackTrace);
    }
  });

  return service;
}

/// System Alert Settings Provider
///
/// Manages system alert settings with automatic persistence and reactivity.
@riverpod
class SystemAlertSettingsNotifier extends _$SystemAlertSettingsNotifier {
  @override
  SystemAlertSettings build() {
    // Initialize with default settings
    return SystemAlertSettings.defaultSettings();
  }

  /// Initialize and load settings from storage
  Future<void> initialize() async {
    try {
      AppLogger.info('🚨 Initializing system alert settings');

      final service = ref.read(systemAlertNotificationServiceProvider);
      await service.initialize();

      // Update state with loaded settings
      state = service.settings;

      AppLogger.info('✅ System alert settings initialized');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize system alert settings', e, stackTrace);
    }
  }

  /// Update system alert settings
  Future<void> updateSettings(SystemAlertSettings newSettings) async {
    try {
      AppLogger.info('⚙️ Updating system alert settings');

      final service = ref.read(systemAlertNotificationServiceProvider);
      await service.updateSettings(newSettings);

      // Update state
      state = newSettings;

      AppLogger.info('✅ System alert settings updated');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to update system alert settings', e, stackTrace);
    }
  }

  /// Enable or disable critical alerts
  Future<void> setCriticalAlertsEnabled(bool enabled) async {
    await updateSettings(state.copyWith(enableCriticalAlerts: enabled));
  }

  /// Enable or disable error alerts
  Future<void> setErrorAlertsEnabled(bool enabled) async {
    await updateSettings(state.copyWith(enableErrorAlerts: enabled));
  }

  /// Enable or disable warning alerts
  Future<void> setWarningAlertsEnabled(bool enabled) async {
    await updateSettings(state.copyWith(enableWarningAlerts: enabled));
  }

  /// Enable or disable security alerts
  Future<void> setSecurityAlertsEnabled(bool enabled) async {
    await updateSettings(state.copyWith(enableSecurityAlerts: enabled));
  }

  /// Enable or disable performance alerts
  Future<void> setPerformanceAlertsEnabled(bool enabled) async {
    await updateSettings(state.copyWith(enablePerformanceAlerts: enabled));
  }

  /// Enable or disable info alerts
  Future<void> setInfoAlertsEnabled(bool enabled) async {
    await updateSettings(state.copyWith(enableInfoAlerts: enabled));
  }

  /// Enable or disable alert escalation
  Future<void> setEscalationEnabled(bool enabled) async {
    await updateSettings(state.copyWith(enableEscalation: enabled));
  }

  /// Enable or disable full-screen alerts
  Future<void> setFullScreenAlertsEnabled(bool enabled) async {
    await updateSettings(state.copyWith(enableFullScreenAlerts: enabled));
  }
}

/// Notification Channel Manager Provider
///
/// Provides the notification channel manager instance following Context7 MCP best practices.
/// Context7 MCP: Integrates with unified notification provider dependency injection system.
@riverpod
Future<NotificationChannelManager> notificationChannelManager(Ref ref) async {
  // Context7 MCP: Get notification service from unified dependency injection container
  // This follows dependency inversion principle by using the unified service dependencies
  final dependencies = await ref.watch(unified.notificationServiceDependenciesProvider.future);

  // Context7 MCP: Create manager using dependency injection pattern
  final manager = NotificationChannelManager(flutterLocalNotificationsPlugin: dependencies.notificationService.plugin);

  // Context7 MCP: Set up proper disposal following resource management best practices
  ref.onDispose(() async {
    try {
      // Cleanup if needed - NotificationChannelManager doesn't have dispose method
      AppLogger.debug('✅ NotificationChannelManager disposed successfully');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to dispose NotificationChannelManager', e, stackTrace);
    }
  });

  return manager;
}

/// Notification Channel Settings Provider
///
/// Manages notification channel settings with automatic persistence and reactivity.
@riverpod
class NotificationChannelSettingsNotifier extends _$NotificationChannelSettingsNotifier {
  @override
  Map<String, NotificationChannelConfig> build() {
    // Initialize with default channel configurations
    final defaultConfigs = <String, NotificationChannelConfig>{};

    for (final channelKey in NotificationChannelKey.values) {
      final config = channelKey.defaultConfig;
      defaultConfigs[config.id] = config;
    }

    return defaultConfigs;
  }

  /// Initialize and load channel settings from storage
  Future<void> initialize() async {
    try {
      AppLogger.info('📢 Initializing notification channel settings');

      final manager = ref.read(notificationChannelManagerProvider);
      await manager.initialize();

      // Create all notification channels
      await manager.createAllChannels();

      AppLogger.info('✅ Notification channel settings initialized');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize notification channel settings', e, stackTrace);
    }
  }

  /// Update channel configuration
  Future<void> updateChannelConfig(NotificationChannelKey channelKey, NotificationChannelConfig config) async {
    try {
      AppLogger.info('📢 Updating channel configuration: ${channelKey.id}');

      final manager = ref.read(notificationChannelManagerProvider);
      await manager.updateChannel(
        channelKey,
        name: config.name,
        description: config.description,
        importance: config.importance,
        priority: config.priority,
        enableVibration: config.enableVibration,
        enableSound: config.enableSound,
      );

      // Update state
      state = {...state, config.id: config};

      AppLogger.info('✅ Channel configuration updated: ${channelKey.id}');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to update channel configuration', e, stackTrace);
    }
  }

  /// Create custom notification channel
  Future<void> createCustomChannel(
    String channelId,
    String name,
    String description, {
    Importance importance = Importance.defaultImportance,
    Priority priority = Priority.defaultPriority,
    bool enableVibration = true,
    bool enableSound = true,
    String? groupId,
  }) async {
    try {
      AppLogger.info('📢 Creating custom notification channel: $channelId');

      final manager = ref.read(notificationChannelManagerProvider);
      await manager.createChannel(
        NotificationChannelKey.general, // Use general as base
        customId: channelId,
        customName: name,
        customDescription: description,
        importance: importance,
        priority: priority,
        enableVibration: enableVibration,
        enableSound: enableSound,
        groupId: groupId,
      );

      // Add to state
      final config = NotificationChannelConfig(
        id: channelId,
        name: name,
        description: description,
        importance: importance,
        priority: priority,
        enableVibration: enableVibration,
        enableSound: enableSound,
        groupId: groupId,
      );

      state = {...state, channelId: config};

      AppLogger.info('✅ Custom notification channel created: $channelId');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to create custom notification channel', e, stackTrace);
    }
  }

  /// Delete notification channel
  Future<void> deleteChannel(NotificationChannelKey channelKey) async {
    try {
      AppLogger.info('📢 Deleting notification channel: ${channelKey.id}');

      final manager = ref.read(notificationChannelManagerProvider);
      await manager.deleteChannel(channelKey);

      // Remove from state
      final newState = Map<String, NotificationChannelConfig>.from(state);
      newState.remove(channelKey.id);
      state = newState;

      AppLogger.info('✅ Notification channel deleted: ${channelKey.id}');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to delete notification channel', e, stackTrace);
    }
  }

  /// Get channel configuration
  NotificationChannelConfig? getChannelConfig(String channelId) {
    return state[channelId];
  }

  /// Check if channel exists
  bool channelExists(String channelId) {
    return state.containsKey(channelId);
  }

  /// Get all channel configurations
  List<NotificationChannelConfig> getAllChannelConfigs() {
    return state.values.toList();
  }

  /// Get channel statistics
  Map<String, dynamic> getChannelStatistics() {
    final manager = ref.read(notificationChannelManagerProvider);
    return {...manager.getChannelStatistics(), 'configured_channels': state.length, 'channel_ids': state.keys.toList()};
  }
}

/// Notification Scheduler Provider
///
/// Provides the notification scheduler instance following Context7 MCP best practices.
/// Context7 MCP: Integrates with unified notification provider dependency injection system.
@riverpod
Future<NotificationScheduler> notificationScheduler(Ref ref) async {
  // Context7 MCP: Get notification service from unified dependency injection container
  // This follows dependency inversion principle by using the unified service dependencies
  final dependencies = await ref.watch(unified.notificationServiceDependenciesProvider.future);

  // Context7 MCP: Create scheduler using dependency injection pattern
  final scheduler = NotificationScheduler(flutterLocalNotificationsPlugin: dependencies.notificationService.plugin);

  // Context7 MCP: Set up proper disposal following resource management best practices
  ref.onDispose(() async {
    try {
      await scheduler.dispose();
      AppLogger.debug('✅ NotificationScheduler disposed successfully');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to dispose NotificationScheduler', e, stackTrace);
    }
  });

  return scheduler;
}

/// Scheduled Notifications Provider
///
/// Manages scheduled notifications with automatic persistence and reactivity.
@riverpod
class ScheduledNotificationsNotifier extends _$ScheduledNotificationsNotifier {
  @override
  List<ScheduledNotificationInfo> build() {
    // Initialize with empty list
    return [];
  }

  /// Initialize and load scheduled notifications
  Future<void> initialize() async {
    try {
      AppLogger.info('⏰ Initializing scheduled notifications');

      final scheduler = ref.read(notificationSchedulerProvider);
      await scheduler.initialize();

      // Load existing scheduled notifications
      final scheduledNotifications = scheduler.getAllScheduledNotifications();
      state = scheduledNotifications;

      AppLogger.info('✅ Scheduled notifications initialized');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize scheduled notifications', e, stackTrace);
    }
  }

  /// Schedule a notification
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    required NotificationChannelKey channelKey,
    Map<String, dynamic>? payloadData,
    bool matchDateTimeComponents = false,
  }) async {
    try {
      AppLogger.info('⏰ Scheduling notification: $id');

      final scheduler = ref.read(notificationSchedulerProvider);

      await scheduler.scheduleNotification(
        id: id,
        title: title,
        body: body,
        scheduledDate: scheduledDate,
        channelKey: channelKey,
        matchDateTimeComponents: matchDateTimeComponents,
      );

      // Update state
      final updatedNotifications = scheduler.getAllScheduledNotifications();
      state = updatedNotifications;

      AppLogger.info('✅ Notification scheduled successfully: $id');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to schedule notification: $id', e, stackTrace);
    }
  }

  /// Schedule periodic notification
  Future<void> schedulePeriodicNotification({
    required int id,
    required String title,
    required String body,
    required RepeatInterval interval,
    required NotificationChannelKey channelKey,
    Map<String, dynamic>? payloadData,
  }) async {
    try {
      AppLogger.info('⏰ Scheduling periodic notification: $id');

      final scheduler = ref.read(notificationSchedulerProvider);

      await scheduler.schedulePeriodicNotification(
        id: id,
        title: title,
        body: body,
        interval: interval,
        channelKey: channelKey,
      );

      // Update state
      final updatedNotifications = scheduler.getAllScheduledNotifications();
      state = updatedNotifications;

      AppLogger.info('✅ Periodic notification scheduled successfully: $id');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to schedule periodic notification: $id', e, stackTrace);
    }
  }

  /// Cancel notification
  Future<void> cancelNotification(int id) async {
    try {
      AppLogger.info('❌ Cancelling notification: $id');

      final scheduler = ref.read(notificationSchedulerProvider);
      await scheduler.cancelNotification(id);

      // Update state
      final updatedNotifications = scheduler.getAllScheduledNotifications();
      state = updatedNotifications;

      AppLogger.info('✅ Notification cancelled successfully: $id');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to cancel notification: $id', e, stackTrace);
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      AppLogger.info('❌ Cancelling all notifications');

      final scheduler = ref.read(notificationSchedulerProvider);
      await scheduler.cancelAllNotifications();

      // Update state
      state = [];

      AppLogger.info('✅ All notifications cancelled successfully');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to cancel all notifications', e, stackTrace);
    }
  }

  /// Get scheduled notification by ID
  ScheduledNotificationInfo? getScheduledNotification(int id) {
    final notifications = state.where((notification) => notification.id == id);
    return notifications.isNotEmpty ? notifications.first : null;
  }

  /// Get notifications by channel
  List<ScheduledNotificationInfo> getNotificationsByChannel(NotificationChannelKey channelKey) {
    return state.where((notification) => notification.channelKey == channelKey).toList();
  }

  /// Get active notifications
  List<ScheduledNotificationInfo> getActiveNotifications() {
    return state.where((notification) => notification.isActive && !notification.isExpired).toList();
  }

  /// Get expired notifications
  List<ScheduledNotificationInfo> getExpiredNotifications() {
    return state.where((notification) => notification.isExpired).toList();
  }

  /// Get notification statistics
  Map<String, dynamic> getNotificationStatistics() {
    final scheduler = ref.read(notificationSchedulerProvider);
    return {
      ...scheduler.getQueueStatus(),
      'total_scheduled': state.length,
      'active_notifications': getActiveNotifications().length,
      'expired_notifications': getExpiredNotifications().length,
      'recurring_notifications': state.where((n) => n.isRecurring).length,
    };
  }
}

/// Notification Analytics Provider
///
/// Provides the notification analytics service instance following Context7 MCP best practices.
/// Context7 MCP: Standalone service that integrates with unified notification provider system.
@riverpod
Future<NotificationAnalyticsService> notificationAnalyticsService(Ref ref) async {
  // Context7 MCP: Create standalone analytics service
  final service = NotificationAnalyticsService();

  // Context7 MCP: Initialize service following proper lifecycle management
  await service.initialize();

  // Context7 MCP: Set up proper disposal following resource management best practices
  ref.onDispose(() async {
    try {
      await service.dispose();
      AppLogger.debug('✅ NotificationAnalyticsService disposed successfully');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to dispose NotificationAnalyticsService', e, stackTrace);
    }
  });

  return service;
}

/// Notification Analytics Configuration Provider
///
/// Manages notification analytics configuration with reactive state management.
@riverpod
class NotificationAnalyticsConfigNotifier extends _$NotificationAnalyticsConfigNotifier {
  @override
  NotificationAnalyticsConfig build() {
    // Initialize with default configuration
    return NotificationAnalyticsConfig.defaultConfig();
  }

  /// Initialize analytics configuration
  Future<void> initialize() async {
    try {
      AppLogger.info('📊 Initializing analytics configuration');

      // Load configuration from storage
      // This would typically load from SharedPreferences or secure storage

      AppLogger.info('✅ Analytics configuration initialized');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize analytics configuration', e, stackTrace);
    }
  }

  /// Update analytics configuration
  Future<void> updateConfiguration(NotificationAnalyticsConfig config) async {
    try {
      AppLogger.info('📊 Updating analytics configuration');

      state = config;

      // Update the analytics service configuration
      final analyticsService = ref.read(notificationAnalyticsServiceProvider);
      await analyticsService.updateConfiguration(config);

      // Save configuration to storage
      // This would typically save to SharedPreferences or secure storage

      AppLogger.info('✅ Analytics configuration updated');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to update analytics configuration', e, stackTrace);
    }
  }

  /// Enable/disable delivery tracking
  Future<void> setDeliveryTracking(bool enabled) async {
    await updateConfiguration(state.copyWith(enableDeliveryTracking: enabled));
  }

  /// Enable/disable interaction tracking
  Future<void> setInteractionTracking(bool enabled) async {
    await updateConfiguration(state.copyWith(enableInteractionTracking: enabled));
  }

  /// Enable/disable error tracking
  Future<void> setErrorTracking(bool enabled) async {
    await updateConfiguration(state.copyWith(enableErrorTracking: enabled));
  }

  /// Enable/disable performance tracking
  Future<void> setPerformanceTracking(bool enabled) async {
    await updateConfiguration(state.copyWith(enablePerformanceTracking: enabled));
  }

  /// Set data retention period
  Future<void> setDataRetentionPeriod(Duration period) async {
    await updateConfiguration(state.copyWith(dataRetentionPeriod: period));
  }

  /// Use privacy-focused configuration
  Future<void> usePrivacyFocusedConfig() async {
    await updateConfiguration(NotificationAnalyticsConfig.privacyFocused());
  }

  /// Reset to default configuration
  Future<void> resetToDefault() async {
    await updateConfiguration(NotificationAnalyticsConfig.defaultConfig());
  }
}

/// Notification Analytics Data Provider
///
/// Manages analytics data and reports with reactive state management.
@riverpod
class NotificationAnalyticsDataNotifier extends _$NotificationAnalyticsDataNotifier {
  @override
  NotificationAnalyticsSummary build() {
    // Initialize with empty summary
    return NotificationAnalyticsSummary.empty();
  }

  /// Initialize analytics data
  Future<void> initialize() async {
    try {
      AppLogger.info('📊 Initializing analytics data');

      final analyticsService = ref.read(notificationAnalyticsServiceProvider);
      await analyticsService.initialize();

      // Load real-time analytics
      await refreshAnalytics();

      AppLogger.info('✅ Analytics data initialized');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize analytics data', e, stackTrace);
    }
  }

  /// Refresh analytics data
  Future<void> refreshAnalytics() async {
    try {
      final analyticsService = ref.read(notificationAnalyticsServiceProvider);
      final summary = analyticsService.getRealTimeAnalytics();
      state = summary;
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to refresh analytics', e, stackTrace);
    }
  }

  /// Generate analytics report
  Future<NotificationAnalyticsReport> generateReport({
    required DateTime startDate,
    required DateTime endDate,
    List<NotificationChannelKey>? channels,
  }) async {
    try {
      AppLogger.info('📊 Generating analytics report');

      final analyticsService = ref.read(notificationAnalyticsServiceProvider);
      final report = await analyticsService.generateAnalyticsReport(
        startDate: startDate,
        endDate: endDate,
        channels: channels,
      );

      AppLogger.info('✅ Analytics report generated');
      return report;
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to generate analytics report', e, stackTrace);
      return NotificationAnalyticsReport.empty(startDate: startDate, endDate: endDate);
    }
  }

  /// Track notification delivery
  Future<void> trackDelivery({
    required String notificationId,
    required NotificationChannelKey channelKey,
    required DateTime deliveryTime,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final analyticsService = ref.read(notificationAnalyticsServiceProvider);
      await analyticsService.trackNotificationDelivered(
        notificationId: notificationId,
        channelKey: channelKey,
        deliveryTime: deliveryTime,
        metadata: metadata,
      );

      // Refresh analytics after tracking
      await refreshAnalytics();
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to track delivery', e, stackTrace);
    }
  }

  /// Track notification interaction
  Future<void> trackInteraction({
    required String notificationId,
    required NotificationInteractionType interactionType,
    required DateTime timestamp,
    String? actionId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final analyticsService = ref.read(notificationAnalyticsServiceProvider);
      await analyticsService.trackNotificationInteraction(
        notificationId: notificationId,
        interactionType: interactionType,
        timestamp: timestamp,
        actionId: actionId,
        metadata: metadata,
      );

      // Refresh analytics after tracking
      await refreshAnalytics();
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to track interaction', e, stackTrace);
    }
  }

  /// Track notification error
  Future<void> trackError({
    required String errorType,
    required String errorMessage,
    String? notificationId,
    NotificationChannelKey? channelKey,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final analyticsService = ref.read(notificationAnalyticsServiceProvider);
      await analyticsService.trackNotificationError(
        errorType: errorType,
        errorMessage: errorMessage,
        notificationId: notificationId,
        channelKey: channelKey,
        metadata: metadata,
      );

      // Refresh analytics after tracking
      await refreshAnalytics();
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to track error', e, stackTrace);
    }
  }

  /// Clear all analytics data
  Future<void> clearAnalyticsData() async {
    try {
      AppLogger.info('📊 Clearing analytics data');

      final analyticsService = ref.read(notificationAnalyticsServiceProvider);
      await analyticsService.clearAnalyticsData();

      // Reset state
      state = NotificationAnalyticsSummary.empty();

      AppLogger.info('✅ Analytics data cleared');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ Failed to clear analytics data', e, stackTrace);
    }
  }

  /// Get analytics summary
  NotificationAnalyticsSummary getAnalyticsSummary() {
    return state;
  }

  /// Get system health score
  double getSystemHealthScore() {
    return state.systemHealth;
  }
}
