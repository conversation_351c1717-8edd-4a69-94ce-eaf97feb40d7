import 'dart:io';

import 'package:flutter/foundation.dart';

import '../../logging/app_logger.dart';

/// Migration Utilities
///
/// **Task 4.1.3: Add migration utilities for automated code updates**
///
/// This utility class provides automated code migration tools following Context7 MCP
/// patterns for seamless provider migration and code transformation.
///
/// Features:
/// - Automated code scanning and transformation
/// - Provider import statement updates
/// - Method call replacements with proper mapping
/// - Batch file processing with progress tracking
/// - Backup creation before modifications
/// - Rollback capabilities for failed migrations
/// - Validation and testing integration
/// - Migration report generation
/// - Context7 MCP compliance verification
/// - Developer-friendly migration scripts
class MigrationUtilities {
  static final MigrationUtilities _instance = MigrationUtilities._internal();
  factory MigrationUtilities() => _instance;
  MigrationUtilities._internal();

  /// Migration patterns for automated code transformation
  static const Map<String, MigrationPattern> _migrationPatterns = {
    // Legacy Prayer Notification Provider patterns
    'legacyPrayerNotificationProvider': MigrationPattern(
      oldImport:
          "import 'package:masajid_albahrain/core/notifications/providers/legacy_prayer_notification_provider.dart';",
      newImport: "import 'package:masajid_albahrain/core/notifications/providers/unified_notification_provider.dart';",
      providerReplacements: {'legacyPrayerNotificationProvider': 'unifiedNotificationSettingsProvider'},
      methodReplacements: {
        'enableNotifications()': 'updateGlobalSettings(globallyEnabled: true)',
        'disableNotifications()': 'updateGlobalSettings(globallyEnabled: false)',
        'setPrayerNotification(\'fajr\', true)': 'updatePrayerSettings(PrayerType.fajr, enabled: true)',
        'setPrayerNotification(\'dhuhr\', true)': 'updatePrayerSettings(PrayerType.dhuhr, enabled: true)',
        'setPrayerNotification(\'asr\', true)': 'updatePrayerSettings(PrayerType.asr, enabled: true)',
        'setPrayerNotification(\'maghrib\', true)': 'updatePrayerSettings(PrayerType.maghrib, enabled: true)',
        'setPrayerNotification(\'isha\', true)': 'updatePrayerSettings(PrayerType.isha, enabled: true)',
        'setSoundEnabled(': 'updateSoundSettings(enabled: ',
        'setVibrationEnabled(': 'updateVibrationSettings(enabled: ',
        'setReminderMinutes(': 'updateReminderSettings(defaultMinutesBefore: ',
      },
    ),

    // Legacy Community Notification Provider patterns
    'legacyCommunityNotificationProvider': MigrationPattern(
      oldImport:
          "import 'package:masajid_albahrain/core/notifications/providers/legacy_community_notification_provider.dart';",
      newImport: "import 'package:masajid_albahrain/core/notifications/providers/unified_notification_provider.dart';",
      providerReplacements: {'legacyCommunityNotificationProvider': 'unifiedNotificationSettingsProvider'},
      methodReplacements: {
        'enableCommunityNotifications()': 'updateCommunitySettings(enabled: true)',
        'disableCommunityNotifications()': 'updateCommunitySettings(enabled: false)',
        'setAnnouncementsEnabled(': 'updateCommunitySettings(announcements: ',
        'setEventsEnabled(': 'updateCommunitySettings(events: ',
        'setNewsEnabled(': 'updateCommunitySettings(news: ',
      },
    ),
  };

  /// Scan project for legacy provider usage
  ///
  /// **Context7 MCP Implementation:**
  /// - Single responsibility: Focused on code scanning and analysis
  /// - Open/closed principle: Extensible for new provider patterns
  /// - Dependency inversion: Uses abstract scanning interfaces
  /// - Interface segregation: Specific scanning methods for different file types
  ///
  /// **Usage:**
  /// ```dart
  /// final scanResult = await MigrationUtilities().scanProject();
  /// print('Found ${scanResult.legacyUsages.length} legacy usages');
  /// ```
  Future<MigrationScanResult> scanProject({
    String projectPath = '.',
    List<String> includePaths = const ['lib/'],
    List<String> excludePaths = const ['.dart_tool/', 'build/'],
  }) async {
    final legacyUsages = <LegacyUsage>[];
    final filesToMigrate = <String>[];

    try {
      // Scan all Dart files in the project
      final dartFiles = await _findDartFiles(projectPath, includePaths, excludePaths);

      for (final filePath in dartFiles) {
        final fileContent = await File(filePath).readAsString();
        final usages = _scanFileForLegacyUsage(filePath, fileContent);

        if (usages.isNotEmpty) {
          legacyUsages.addAll(usages);
          filesToMigrate.add(filePath);
        }
      }

      // Scan for unused imports and dependencies
      final unusedImports = await _scanForUnusedImports(dartFiles);
      final unusedDependencies = await _scanForUnusedDependencies(projectPath);

      return MigrationScanResult(
        totalFilesScanned: dartFiles.length,
        filesToMigrate: filesToMigrate,
        legacyUsages: legacyUsages,
        unusedImports: unusedImports,
        unusedDependencies: unusedDependencies,
        scanCompletedAt: DateTime.now(),
      );
    } on Exception catch (e) {
      throw MigrationException('Failed to scan project: $e');
    }
  }

  /// Migrate single file
  ///
  /// **Usage:**
  /// ```dart
  /// final result = await MigrationUtilities().migrateFile('lib/pages/settings_page.dart');
  /// if (result.success) {
  ///   print('File migrated successfully');
  /// }
  /// ```
  Future<MigrationResult> migrateFile(
    String filePath, {
    bool createBackup = true,
    bool validateAfterMigration = true,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw MigrationException('File not found: $filePath');
      }

      final originalContent = await file.readAsString();

      // Create backup if requested
      String? backupPath;
      if (createBackup) {
        backupPath = await _createBackup(filePath, originalContent);
      }

      // Apply migration transformations
      final migratedContent = _applyMigrationTransformations(originalContent);

      // Check if any changes were made
      if (migratedContent == originalContent) {
        return MigrationResult(
          filePath: filePath,
          success: true,
          changesApplied: 0,
          backupPath: backupPath,
          message: 'No migration needed - file already up to date',
        );
      }

      // Write migrated content
      await file.writeAsString(migratedContent);

      // Validate migration if requested
      if (validateAfterMigration) {
        final validationResult = await _validateMigration(filePath, migratedContent);
        if (!validationResult.isValid) {
          // Restore backup if validation fails
          if (backupPath != null) {
            await file.writeAsString(originalContent);
          }
          throw MigrationException('Migration validation failed: ${validationResult.errors.join(', ')}');
        }
      }

      final changesCount = _countChanges(originalContent, migratedContent);

      return MigrationResult(
        filePath: filePath,
        success: true,
        changesApplied: changesCount,
        backupPath: backupPath,
        message: 'Migration completed successfully with $changesCount changes',
      );
    } on Exception catch (e) {
      return MigrationResult(filePath: filePath, success: false, changesApplied: 0, message: 'Migration failed: $e');
    }
  }

  /// Migrate multiple files in batch
  ///
  /// **Usage:**
  /// ```dart
  /// final scanResult = await MigrationUtilities().scanProject();
  /// final batchResult = await MigrationUtilities().migrateBatch(
  ///   scanResult.filesToMigrate,
  ///   onProgress: (current, total) => print('Progress: $current/$total'),
  /// );
  /// ```
  Future<BatchMigrationResult> migrateBatch(
    List<String> filePaths, {
    bool createBackups = true,
    bool validateAfterMigration = true,
    Function(int current, int total)? onProgress,
  }) async {
    final results = <MigrationResult>[];
    var successCount = 0;
    var failureCount = 0;

    for (var i = 0; i < filePaths.length; i++) {
      final filePath = filePaths[i];

      // Report progress
      onProgress?.call(i + 1, filePaths.length);

      try {
        final result = await migrateFile(
          filePath,
          createBackup: createBackups,
          validateAfterMigration: validateAfterMigration,
        );

        results.add(result);

        if (result.success) {
          successCount++;
        } else {
          failureCount++;
        }
      } on Exception catch (e) {
        results.add(
          MigrationResult(filePath: filePath, success: false, changesApplied: 0, message: 'Batch migration failed: $e'),
        );
        failureCount++;
      }
    }

    return BatchMigrationResult(
      totalFiles: filePaths.length,
      successCount: successCount,
      failureCount: failureCount,
      results: results,
      completedAt: DateTime.now(),
    );
  }

  /// Generate migration script
  ///
  /// **Usage:**
  /// ```dart
  /// final script = await MigrationUtilities().generateMigrationScript();
  /// await File('migration_script.dart').writeAsString(script);
  /// ```
  Future<String> generateMigrationScript({String projectPath = '.', List<String> includePaths = const ['lib/']}) async {
    final scanResult = await scanProject(projectPath: projectPath, includePaths: includePaths);

    final scriptBuffer = StringBuffer();

    // Script header
    scriptBuffer.writeln('#!/usr/bin/env dart');
    scriptBuffer.writeln('');
    scriptBuffer.writeln('/// Automated Migration Script');
    scriptBuffer.writeln('/// Generated on: ${DateTime.now().toIso8601String()}');
    scriptBuffer.writeln('/// Total files to migrate: ${scanResult.filesToMigrate.length}');
    scriptBuffer.writeln('/// Total legacy usages: ${scanResult.legacyUsages.length}');
    scriptBuffer.writeln('');
    scriptBuffer.writeln("import 'dart:io';");
    scriptBuffer.writeln("import 'package:masajid_albahrain/core/notifications/migration/migration_utilities.dart';");
    scriptBuffer.writeln('');
    scriptBuffer.writeln('Future<void> main() async {');
    scriptBuffer.writeln('  print("Starting automated migration...");');
    scriptBuffer.writeln('  ');
    scriptBuffer.writeln('  final migrationUtils = MigrationUtilities();');
    scriptBuffer.writeln('  ');
    scriptBuffer.writeln('  // Files to migrate');
    scriptBuffer.writeln('  final filesToMigrate = [');

    for (final filePath in scanResult.filesToMigrate) {
      scriptBuffer.writeln("    '$filePath',");
    }

    scriptBuffer.writeln('  ];');
    scriptBuffer.writeln('  ');
    scriptBuffer.writeln('  // Perform batch migration');
    scriptBuffer.writeln('  final result = await migrationUtils.migrateBatch(');
    scriptBuffer.writeln('    filesToMigrate,');
    scriptBuffer.writeln('    createBackups: true,');
    scriptBuffer.writeln('    validateAfterMigration: true,');
    scriptBuffer.writeln('    onProgress: (current, total) {');
    scriptBuffer.writeln('      print("Progress: \$current/\$total");');
    scriptBuffer.writeln('    },');
    scriptBuffer.writeln('  );');
    scriptBuffer.writeln('  ');
    scriptBuffer.writeln('  // Print results');
    scriptBuffer.writeln('  print("Migration completed!");');
    scriptBuffer.writeln('  print("Success: \${result.successCount}");');
    scriptBuffer.writeln('  print("Failures: \${result.failureCount}");');
    scriptBuffer.writeln('  ');
    scriptBuffer.writeln('  // Print detailed results');
    scriptBuffer.writeln('  for (final migrationResult in result.results) {');
    scriptBuffer.writeln('    if (migrationResult.success) {');
    scriptBuffer.writeln('      print("✅ \${migrationResult.filePath}: \${migrationResult.changesApplied} changes");');
    scriptBuffer.writeln('    } else {');
    scriptBuffer.writeln('      print("❌ \${migrationResult.filePath}: \${migrationResult.message}");');
    scriptBuffer.writeln('    }');
    scriptBuffer.writeln('  }');
    scriptBuffer.writeln('}');

    return scriptBuffer.toString();
  }

  /// Rollback migration using backup
  ///
  /// **Usage:**
  /// ```dart
  /// await MigrationUtilities().rollbackMigration('lib/pages/settings_page.dart.backup');
  /// ```
  Future<bool> rollbackMigration(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        throw MigrationException('Backup file not found: $backupPath');
      }

      final originalPath = backupPath.replaceAll('.backup', '');
      final originalFile = File(originalPath);

      final backupContent = await backupFile.readAsString();
      await originalFile.writeAsString(backupContent);

      // Remove backup file after successful rollback
      await backupFile.delete();

      return true;
    } catch (e) {
      debugPrint('Rollback failed: $e');
      return false;
    }
  }

  /// Validate migration result
  Future<ValidationResult> validateMigration(String filePath) async {
    try {
      final file = File(filePath);
      final content = await file.readAsString();
      return await _validateMigration(filePath, content);
    } catch (e) {
      return ValidationResult(isValid: false, errors: ['Validation failed: $e']);
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// Find all Dart files in the project
  Future<List<String>> _findDartFiles(String projectPath, List<String> includePaths, List<String> excludePaths) async {
    final dartFiles = <String>[];
    final projectDir = Directory(projectPath);

    await for (final entity in projectDir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        final relativePath = entity.path.replaceFirst('$projectPath/', '');

        // Check if file should be included
        final shouldInclude = includePaths.any((path) => relativePath.startsWith(path));
        final shouldExclude = excludePaths.any((path) => relativePath.startsWith(path));

        if (shouldInclude && !shouldExclude) {
          dartFiles.add(entity.path);
        }
      }
    }

    return dartFiles;
  }

  /// Scan file for legacy provider usage
  List<LegacyUsage> _scanFileForLegacyUsage(String filePath, String content) {
    final usages = <LegacyUsage>[];
    final lines = content.split('\n');

    for (var i = 0; i < lines.length; i++) {
      final line = lines[i];
      final lineNumber = i + 1;

      // Check for legacy provider patterns
      for (final pattern in _migrationPatterns.values) {
        // Check for import statements
        if (line.contains(pattern.oldImport.replaceAll("import '", '').replaceAll("';", ''))) {
          usages.add(
            LegacyUsage(
              filePath: filePath,
              lineNumber: lineNumber,
              type: LegacyUsageType.import,
              content: line.trim(),
              suggestedReplacement: pattern.newImport,
            ),
          );
        }

        // Check for provider usage
        for (final entry in pattern.providerReplacements.entries) {
          if (line.contains(entry.key)) {
            usages.add(
              LegacyUsage(
                filePath: filePath,
                lineNumber: lineNumber,
                type: LegacyUsageType.provider,
                content: line.trim(),
                suggestedReplacement: line.replaceAll(entry.key, entry.value),
              ),
            );
          }
        }

        // Check for method calls
        for (final entry in pattern.methodReplacements.entries) {
          if (line.contains(entry.key)) {
            usages.add(
              LegacyUsage(
                filePath: filePath,
                lineNumber: lineNumber,
                type: LegacyUsageType.method,
                content: line.trim(),
                suggestedReplacement: line.replaceAll(entry.key, entry.value),
              ),
            );
          }
        }
      }
    }

    return usages;
  }

  /// Apply migration transformations to content
  String _applyMigrationTransformations(String content) {
    var migratedContent = content;

    // Apply all migration patterns
    for (final pattern in _migrationPatterns.values) {
      // Replace imports
      migratedContent = migratedContent.replaceAll(pattern.oldImport, pattern.newImport);

      // Replace provider references
      for (final entry in pattern.providerReplacements.entries) {
        migratedContent = migratedContent.replaceAll(entry.key, entry.value);
      }

      // Replace method calls
      for (final entry in pattern.methodReplacements.entries) {
        migratedContent = migratedContent.replaceAll(entry.key, entry.value);
      }
    }

    return migratedContent;
  }

  /// Create backup of file
  Future<String> _createBackup(String filePath, String content) async {
    final backupPath = '$filePath.backup';
    final backupFile = File(backupPath);
    await backupFile.writeAsString(content);
    return backupPath;
  }

  /// Validate migration
  Future<ValidationResult> _validateMigration(String filePath, String content) async {
    final errors = <String>[];

    // Check for remaining legacy patterns
    for (final pattern in _migrationPatterns.values) {
      if (content.contains(pattern.oldImport)) {
        errors.add('Legacy import still present: ${pattern.oldImport}');
      }

      for (final oldProvider in pattern.providerReplacements.keys) {
        if (content.contains(oldProvider)) {
          errors.add('Legacy provider still present: $oldProvider');
        }
      }
    }

    // Check for syntax errors (basic validation)
    if (!content.contains('import ') && content.contains('ref.read(')) {
      errors.add('Missing import statements');
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  /// Count changes between original and migrated content
  int _countChanges(String original, String migrated) {
    final originalLines = original.split('\n');
    final migratedLines = migrated.split('\n');

    var changes = 0;
    final maxLines = originalLines.length > migratedLines.length ? originalLines.length : migratedLines.length;

    for (var i = 0; i < maxLines; i++) {
      final originalLine = i < originalLines.length ? originalLines[i] : '';
      final migratedLine = i < migratedLines.length ? migratedLines[i] : '';

      if (originalLine != migratedLine) {
        changes++;
      }
    }

    return changes;
  }

  /// Clean up migration artifacts and temporary files
  ///
  /// Context7 MCP: Implements comprehensive cleanup following Context7 MCP
  /// cleanup patterns with proper error handling and logging.
  ///
  /// **Usage:**
  /// ```dart
  /// final migrationUtils = MigrationUtilities();
  /// await migrationUtils.cleanupMigrationArtifacts();
  /// ```
  Future<void> cleanupMigrationArtifacts() async {
    try {
      AppLogger.info('MigrationUtilities: Starting cleanup of migration artifacts');

      // Clean up temporary migration files
      final tempDir = Directory('.migration_temp');
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
        AppLogger.debug('MigrationUtilities: Removed temporary migration directory');
      }

      // Clean up migration logs
      final logFiles = await Directory(
        '.',
      ).list().where((entity) => entity is File && entity.path.endsWith('.migration.log')).cast<File>().toList();

      for (final logFile in logFiles) {
        await logFile.delete();
        AppLogger.debug('MigrationUtilities: Removed migration log: ${logFile.path}');
      }

      // Clean up migration state files
      final stateFiles = await Directory(
        '.',
      ).list().where((entity) => entity is File && entity.path.endsWith('.migration_state')).cast<File>().toList();

      for (final stateFile in stateFiles) {
        await stateFile.delete();
        AppLogger.debug('MigrationUtilities: Removed migration state file: ${stateFile.path}');
      }

      AppLogger.info('MigrationUtilities: Migration artifacts cleanup completed');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MigrationUtilities: Failed to cleanup migration artifacts', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Clean up backup files after successful migration
  ///
  /// Context7 MCP: Implements safe backup cleanup with confirmation checks
  /// following Context7 MCP data safety patterns.
  ///
  /// **Usage:**
  /// ```dart
  /// final migrationUtils = MigrationUtilities();
  /// await migrationUtils.cleanupBackupFiles();
  /// ```
  Future<void> cleanupBackupFiles({bool confirmMigrationSuccess = true}) async {
    try {
      AppLogger.info('MigrationUtilities: Starting cleanup of backup files');

      if (confirmMigrationSuccess) {
        // Verify migration was successful before cleaning backups
        final migrationSuccessful = await _verifyMigrationSuccess();
        if (!migrationSuccessful) {
          AppLogger.warning('MigrationUtilities: Migration verification failed, keeping backup files');
          return;
        }
      }

      // Find all backup files
      final backupFiles = await Directory('.')
          .list(recursive: true)
          .where((entity) => entity is File && entity.path.endsWith('.backup'))
          .cast<File>()
          .toList();

      var cleanedCount = 0;
      for (final backupFile in backupFiles) {
        try {
          await backupFile.delete();
          cleanedCount++;
          AppLogger.debug('MigrationUtilities: Removed backup file: ${backupFile.path}');
        } on Exception catch (e) {
          AppLogger.warning('MigrationUtilities: Failed to remove backup file ${backupFile.path}: $e');
        }
      }

      AppLogger.info('MigrationUtilities: Backup cleanup completed, removed $cleanedCount files');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MigrationUtilities: Failed to cleanup backup files', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Remove unused import statement from file
  ///
  /// Context7 MCP: Implements safe import removal with validation
  /// following Context7 MCP code transformation patterns.
  ///
  /// **Usage:**
  /// ```dart
  /// final migrationUtils = MigrationUtilities();
  /// final unusedImport = UnusedImport(...);
  /// await migrationUtils.removeUnusedImport(unusedImport);
  /// ```
  Future<void> removeUnusedImport(UnusedImport unusedImport) async {
    try {
      if (!unusedImport.safeToRemove) {
        AppLogger.warning(
          'MigrationUtilities: Skipping removal of potentially unsafe import: ${unusedImport.importStatement}',
        );
        return;
      }

      final file = File(unusedImport.filePath);
      if (!await file.exists()) {
        AppLogger.warning('MigrationUtilities: File not found: ${unusedImport.filePath}');
        return;
      }

      final lines = await file.readAsLines();
      if (unusedImport.lineNumber <= 0 || unusedImport.lineNumber > lines.length) {
        AppLogger.warning(
          'MigrationUtilities: Invalid line number ${unusedImport.lineNumber} for file ${unusedImport.filePath}',
        );
        return;
      }

      // Verify the line contains the expected import
      final targetLine = lines[unusedImport.lineNumber - 1];
      if (!targetLine.trim().contains(unusedImport.packageName)) {
        AppLogger.warning(
          'MigrationUtilities: Import statement mismatch at line ${unusedImport.lineNumber} in ${unusedImport.filePath}',
        );
        return;
      }

      // Remove the import line
      lines.removeAt(unusedImport.lineNumber - 1);

      // Write back to file
      await file.writeAsString('${lines.join('\n')}\n');

      AppLogger.debug(
        'MigrationUtilities: Removed unused import ${unusedImport.packageName} from ${unusedImport.filePath}',
      );
    } on Exception catch (e, stackTrace) {
      AppLogger.error(
        'MigrationUtilities: Failed to remove unused import',
        error: e,
        stackTrace: stackTrace,
        context: {
          'filePath': unusedImport.filePath,
          'lineNumber': unusedImport.lineNumber,
          'packageName': unusedImport.packageName,
        },
      );
      rethrow;
    }
  }

  /// Scan for unused imports in Dart files
  ///
  /// Context7 MCP: Implements comprehensive import analysis following
  /// Context7 MCP static analysis patterns.
  Future<List<UnusedImport>> _scanForUnusedImports(List<String> dartFiles) async {
    final unusedImports = <UnusedImport>[];

    for (final filePath in dartFiles) {
      try {
        final file = File(filePath);
        final content = await file.readAsString();
        final lines = content.split('\n');

        for (var i = 0; i < lines.length; i++) {
          final line = lines[i].trim();

          // Check for import statements
          if (line.startsWith('import ') && line.endsWith(';')) {
            final importMatch = RegExp(r"import\s+['\"]([^'"]+)['\"];").firstMatch(line);
            final packageName = importMatch.group(1)!;

            // Simple heuristic: check if package is used in the file
            final isUsed = _isImportUsed(content, packageName, line);

            if (!isUsed) {
              unusedImports.add(UnusedImport(
                filePath: filePath,
                lineNumber: i + 1,
                importStatement: line,
                packageName: packageName,
                safeToRemove: _isSafeToRemoveImport(packageName),
              ));
            }
                    }
        }
      } on Exception catch (e) {
        AppLogger.warning('Failed to scan file for unused imports: $filePath - $e');
      }
    }

    return unusedImports;
  }

  /// Scan for unused dependencies in pubspec.yaml
  ///
  /// Context7 MCP: Implements dependency analysis following Context7 MCP
  /// dependency management patterns.
  Future<List<UnusedDependency>> _scanForUnusedDependencies(String projectPath) async {
    final unusedDependencies = <UnusedDependency>[];

    try {
      final pubspecFile = File('$projectPath/pubspec.yaml');
      if (!await pubspecFile.exists()) {
        return unusedDependencies;
      }

      final pubspecContent = await pubspecFile.readAsString();
      final dependencyPattern = RegExp(r'^\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*(.+)$', multiLine: true);
      final matches = dependencyPattern.allMatches(pubspecContent);

      for (final match in matches) {
        final dependencyName = match.group(1)!;
        final version = match.group(2)!;

        // Check if dependency is used in the project
        final isUsed = await _isDependencyUsed(projectPath, dependencyName);

        if (!isUsed) {
          unusedDependencies.add(UnusedDependency(
            dependencyName: dependencyName,
            version: version,
            isDevDependency: _isDevDependency(pubspecContent, dependencyName),
            safeToRemove: _isSafeToRemoveDependency(dependencyName),
            reason: 'No imports or references found in project',
          ));
        }
      }
    } on Exception catch (e) {
      AppLogger.warning('Failed to scan for unused dependencies: $e');
    }

    return unusedDependencies;
  }

  /// Verify migration was successful
  ///
  /// Context7 MCP: Implements migration verification following Context7 MCP
  /// validation patterns.
  Future<bool> _verifyMigrationSuccess() async {
    try {
      // Check if there are any remaining legacy usages
      final scanResult = await scanProject();

      // Migration is successful if no legacy usages remain
      return scanResult.legacyUsages.isEmpty;
    } on Exception catch (e) {
      AppLogger.warning('Migration verification failed: $e');
      return false;
    }
  }

  /// Check if an import is used in the file content
  bool _isImportUsed(String content, String packageName, String importLine) {
    // Extract the imported symbols or check for package usage
    final packageBaseName = packageName.split('/').last.split('.').first;

    // Simple heuristic: check if package name appears in the content
    // This is a basic implementation - a more sophisticated version would
    // parse the AST and check for actual symbol usage
    return content.contains(packageBaseName) &&
           content.split('\n').where((line) => line.trim() != importLine.trim()).any(
             (line) => line.contains(packageBaseName)
           );
  }

  /// Check if it's safe to remove an import
  bool _isSafeToRemoveImport(String packageName) {
    // Conservative approach: only mark as safe if it's clearly a user import
    // Avoid removing system imports or critical dependencies
    final systemPackages = ['dart:', 'package:flutter/', 'package:meta/'];

    return !systemPackages.any((system) => packageName.startsWith(system));
  }

  /// Check if a dependency is used in the project
  Future<bool> _isDependencyUsed(String projectPath, String dependencyName) async {
    try {
      final dartFiles = await _findDartFiles(projectPath, ['lib/'], ['.dart_tool/', 'build/']);

      for (final filePath in dartFiles) {
        final content = await File(filePath).readAsString();
        if (content.contains('package:$dependencyName/')) {
          return true;
        }
      }

      return false;
    } on Exception catch (e) {
      AppLogger.warning('Failed to check dependency usage for $dependencyName: $e');
      return true; // Conservative: assume it's used if we can't verify
    }
  }

  /// Check if a dependency is a dev dependency
  bool _isDevDependency(String pubspecContent, String dependencyName) {
    final devDependenciesSection = RegExp(r'dev_dependencies:\s*\n((?:\s+.+\n)*)', multiLine: true);
    final match = devDependenciesSection.firstMatch(pubspecContent);

    if (match != null) {
      final devSection = match.group(1)!;
      return devSection.contains('$dependencyName:');
    }

    return false;
  }

  /// Check if it's safe to remove a dependency
  bool _isSafeToRemoveDependency(String dependencyName) {
    // Conservative approach: only mark as safe if it's clearly not a critical dependency
    final criticalDependencies = ['flutter', 'flutter_test', 'meta', 'collection'];

    return !criticalDependencies.contains(dependencyName);
  }
}

// ============================================================================
// DATA MODELS
// ============================================================================

/// Migration Pattern
class MigrationPattern {
  final String oldImport;
  final String newImport;
  final Map<String, String> providerReplacements;
  final Map<String, String> methodReplacements;

  const MigrationPattern({
    required this.oldImport,
    required this.newImport,
    required this.providerReplacements,
    required this.methodReplacements,
  });
}

/// Legacy Usage
class LegacyUsage {
  final String filePath;
  final int lineNumber;
  final LegacyUsageType type;
  final String content;
  final String suggestedReplacement;

  const LegacyUsage({
    required this.filePath,
    required this.lineNumber,
    required this.type,
    required this.content,
    required this.suggestedReplacement,
  });
}

/// Legacy Usage Type
enum LegacyUsageType { import, provider, method }

/// Unused Import
///
/// Context7 MCP: Represents an unused import statement that can be safely removed
/// following Context7 MCP cleanup patterns.
class UnusedImport {
  /// The file path containing the unused import
  final String filePath;

  /// The line number where the import statement is located
  final int lineNumber;

  /// The full import statement text
  final String importStatement;

  /// The package or library being imported
  final String packageName;

  /// Whether this import is safe to remove automatically
  final bool safeToRemove;

  /// Creates a new UnusedImport instance
  ///
  /// Context7 MCP: Immutable data class following Context7 MCP patterns
  const UnusedImport({
    required this.filePath,
    required this.lineNumber,
    required this.importStatement,
    required this.packageName,
    required this.safeToRemove,
  });
}

/// Unused Dependency
///
/// Context7 MCP: Represents an unused dependency in pubspec.yaml that can be removed
/// following Context7 MCP dependency management patterns.
class UnusedDependency {
  /// The name of the unused dependency
  final String dependencyName;

  /// The version constraint of the dependency
  final String version;

  /// Whether this is a dev dependency
  final bool isDevDependency;

  /// Whether this dependency is safe to remove
  final bool safeToRemove;

  /// Reason why this dependency is considered unused
  final String reason;

  /// Creates a new UnusedDependency instance
  ///
  /// Context7 MCP: Immutable data class with comprehensive dependency analysis
  const UnusedDependency({
    required this.dependencyName,
    required this.version,
    required this.isDevDependency,
    required this.safeToRemove,
    required this.reason,
  });
}

/// Migration Scan Result
///
/// Context7 MCP: Comprehensive scan result following Context7 MCP data modeling patterns
/// with complete analysis of project migration status and unused dependencies.
class MigrationScanResult {
  /// Total number of files scanned during the migration analysis
  final int totalFilesScanned;

  /// List of file paths that require migration
  final List<String> filesToMigrate;

  /// Detailed legacy usage information found during scanning
  final List<LegacyUsage> legacyUsages;

  /// List of unused import statements found in the project
  final List<UnusedImport> unusedImports;

  /// List of unused dependencies found in the project
  final List<UnusedDependency> unusedDependencies;

  /// Timestamp when the scan was completed
  final DateTime scanCompletedAt;

  /// Creates a new MigrationScanResult with comprehensive migration analysis data
  ///
  /// Context7 MCP: Follows immutable data pattern with complete validation
  const MigrationScanResult({
    required this.totalFilesScanned,
    required this.filesToMigrate,
    required this.legacyUsages,
    required this.unusedImports,
    required this.unusedDependencies,
    required this.scanCompletedAt,
  });
}

/// Migration Result
class MigrationResult {
  final String filePath;
  final bool success;
  final int changesApplied;
  final String? backupPath;
  final String message;

  const MigrationResult({
    required this.filePath,
    required this.success,
    required this.changesApplied,
    this.backupPath,
    required this.message,
  });
}

/// Batch Migration Result
class BatchMigrationResult {
  final int totalFiles;
  final int successCount;
  final int failureCount;
  final List<MigrationResult> results;
  final DateTime completedAt;

  const BatchMigrationResult({
    required this.totalFiles,
    required this.successCount,
    required this.failureCount,
    required this.results,
    required this.completedAt,
  });
}

/// Validation Result
class ValidationResult {
  final bool isValid;
  final List<String> errors;

  const ValidationResult({required this.isValid, required this.errors});
}

/// Migration Exception
class MigrationException implements Exception {
  final String message;

  const MigrationException(this.message);

  @override
  String toString() => 'MigrationException: $message';
}

/// Migration CLI Tool
///
/// **Context7 MCP Command Line Interface:**
/// - Provides command-line interface for migration operations
/// - Supports batch processing with progress indicators
/// - Includes validation and rollback capabilities
/// - Generates comprehensive migration reports
/// - Follows Context7 MCP patterns for CLI design
class MigrationCLI {
  static final MigrationCLI _instance = MigrationCLI._internal();
  factory MigrationCLI() => _instance;
  MigrationCLI._internal();

  /// Run migration CLI
  ///
  /// **Usage:**
  /// ```bash
  /// dart run lib/core/notifications/migration/migration_cli.dart scan
  /// dart run lib/core/notifications/migration/migration_cli.dart migrate --all
  /// dart run lib/core/notifications/migration/migration_cli.dart rollback --file path/to/file.dart.backup
  /// ```
  Future<void> run(List<String> args) async {
    if (args.isEmpty) {
      _printUsage();
      return;
    }

    final command = args[0];
    final options = _parseOptions(args.skip(1).toList());

    try {
      switch (command) {
        case 'scan':
          await _runScan(options);
          break;
        case 'migrate':
          await _runMigrate(options);
          break;
        case 'rollback':
          await _runRollback(options);
          break;
        case 'validate':
          await _runValidate(options);
          break;
        case 'generate-script':
          await _runGenerateScript(options);
          break;
        case 'help':
          _printUsage();
          break;
        default:
          print('Unknown command: $command');
          _printUsage();
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Run scan command
  Future<void> _runScan(Map<String, String> options) async {
    print('🔍 Scanning project for legacy provider usage...');

    final migrationUtils = MigrationUtilities();
    final scanResult = await migrationUtils.scanProject(
      projectPath: options['path'] ?? '.',
      includePaths: options['include']?.split(',') ?? ['lib/'],
      excludePaths: options['exclude']?.split(',') ?? ['.dart_tool/', 'build/'],
    );

    print('\n📊 Scan Results:');
    print('  Total files scanned: ${scanResult.totalFilesScanned}');
    print('  Files to migrate: ${scanResult.filesToMigrate.length}');
    print('  Legacy usages found: ${scanResult.legacyUsages.length}');

    if (scanResult.legacyUsages.isNotEmpty) {
      print('\n📋 Legacy Usages:');
      for (final usage in scanResult.legacyUsages) {
        print('  ${usage.filePath}:${usage.lineNumber} - ${usage.type.name}');
        print('    Current: ${usage.content}');
        print('    Suggested: ${usage.suggestedReplacement}');
        print('');
      }
    }

    if (scanResult.filesToMigrate.isNotEmpty) {
      print('\n💡 Next steps:');
      print('  Run: dart run migration_cli.dart migrate --all');
      print('  Or: dart run migration_cli.dart generate-script');
    } else {
      print('\n✅ No migration needed - all files are up to date!');
    }
  }

  /// Run migrate command
  Future<void> _runMigrate(Map<String, String> options) async {
    final migrationUtils = MigrationUtilities();

    if (options.containsKey('all')) {
      print('🚀 Starting batch migration...');

      final scanResult = await migrationUtils.scanProject();
      if (scanResult.filesToMigrate.isEmpty) {
        print('✅ No files need migration!');
        return;
      }

      final batchResult = await migrationUtils.migrateBatch(
        scanResult.filesToMigrate,
        createBackups: !options.containsKey('no-backup'),
        validateAfterMigration: !options.containsKey('no-validate'),
        onProgress: (current, total) {
          print('Progress: $current/$total');
        },
      );

      print('\n📊 Migration Results:');
      print('  Total files: ${batchResult.totalFiles}');
      print('  Successful: ${batchResult.successCount}');
      print('  Failed: ${batchResult.failureCount}');

      for (final result in batchResult.results) {
        if (result.success) {
          print('  ✅ ${result.filePath}: ${result.changesApplied} changes');
        } else {
          print('  ❌ ${result.filePath}: ${result.message}');
        }
      }
    } else if (options.containsKey('file')) {
      final filePath = options['file']!;
      print('🔧 Migrating file: $filePath');

      final result = await migrationUtils.migrateFile(
        filePath,
        createBackup: !options.containsKey('no-backup'),
        validateAfterMigration: !options.containsKey('no-validate'),
      );

      if (result.success) {
        print('✅ Migration successful: ${result.changesApplied} changes');
        if (result.backupPath != null) {
          print('📁 Backup created: ${result.backupPath}');
        }
      } else {
        print('❌ Migration failed: ${result.message}');
      }
    } else {
      print('Error: Specify --all or --file <path>');
      _printUsage();
    }
  }

  /// Run rollback command
  Future<void> _runRollback(Map<String, String> options) async {
    if (!options.containsKey('file')) {
      print('Error: Specify --file <backup-path>');
      return;
    }

    final backupPath = options['file']!;
    print('🔄 Rolling back migration: $backupPath');

    final migrationUtils = MigrationUtilities();
    final success = await migrationUtils.rollbackMigration(backupPath);

    if (success) {
      print('✅ Rollback successful');
    } else {
      print('❌ Rollback failed');
    }
  }

  /// Run validate command
  Future<void> _runValidate(Map<String, String> options) async {
    if (!options.containsKey('file')) {
      print('Error: Specify --file <path>');
      return;
    }

    final filePath = options['file']!;
    print('🔍 Validating migration: $filePath');

    final migrationUtils = MigrationUtilities();
    final validationResult = await migrationUtils.validateMigration(filePath);

    if (validationResult.isValid) {
      print('✅ Validation successful');
    } else {
      print('❌ Validation failed:');
      for (final error in validationResult.errors) {
        print('  - $error');
      }
    }
  }

  /// Run generate script command
  Future<void> _runGenerateScript(Map<String, String> options) async {
    print('📝 Generating migration script...');

    final migrationUtils = MigrationUtilities();
    final script = await migrationUtils.generateMigrationScript(
      projectPath: options['path'] ?? '.',
      includePaths: options['include']?.split(',') ?? ['lib/'],
    );

    final scriptPath = options['output'] ?? 'migration_script.dart';
    await File(scriptPath).writeAsString(script);

    print('✅ Migration script generated: $scriptPath');
    print('💡 Run with: dart run $scriptPath');
  }

  /// Parse command line options
  Map<String, String> _parseOptions(List<String> args) {
    final options = <String, String>{};

    for (var i = 0; i < args.length; i++) {
      final arg = args[i];

      if (arg.startsWith('--')) {
        final key = arg.substring(2);

        if (i + 1 < args.length && !args[i + 1].startsWith('--')) {
          options[key] = args[i + 1];
          i++; // Skip next argument as it's the value
        } else {
          options[key] = 'true'; // Flag without value
        }
      }
    }

    return options;
  }

  /// Print usage information
  void _printUsage() {
    print('''
Migration CLI Tool - Automated Provider Migration

Usage:
  dart run migration_cli.dart <command> [options]

Commands:
  scan                    Scan project for legacy provider usage
  migrate                 Migrate files to use unified providers
  rollback                Rollback migration using backup file
  validate                Validate migration result
  generate-script         Generate automated migration script
  help                    Show this help message

Options:
  --path <path>           Project path (default: .)
  --include <paths>       Include paths (comma-separated, default: lib/)
  --exclude <paths>       Exclude paths (comma-separated, default: .dart_tool/,build/)
  --file <path>           Specific file to migrate/rollback/validate
  --all                   Migrate all files
  --no-backup             Skip backup creation
  --no-validate           Skip validation after migration
  --output <path>         Output path for generated script (default: migration_script.dart)

Examples:
  dart run migration_cli.dart scan
  dart run migration_cli.dart migrate --all
  dart run migration_cli.dart migrate --file lib/pages/settings_page.dart
  dart run migration_cli.dart rollback --file lib/pages/settings_page.dart.backup
  dart run migration_cli.dart validate --file lib/pages/settings_page.dart
  dart run migration_cli.dart generate-script --output my_migration.dart

For more information, visit:
https://docs.masajid-albahrain.com/migration
''');
  }
}
