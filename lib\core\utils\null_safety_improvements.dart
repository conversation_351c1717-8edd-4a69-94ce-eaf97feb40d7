/// Null safety improvements implementation
///
/// This file demonstrates and implements improved null safety patterns
/// throughout the codebase, focusing on practical improvements that
/// enhance type safety without breaking existing functionality.
library null_safety_improvements;

import 'dart:async';
import 'package:flutter/foundation.dart';

/// Improved service base class with better null safety patterns
abstract class SafeService {
  bool _isInitialized = false;
  bool _isDisposed = false;

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if the service is disposed
  bool get isDisposed => _isDisposed;

  /// Safe initialization pattern
  Future<void> initialize() async {
    if (_isInitialized || _isDisposed) return;

    try {
      await onInitialize();
      _isInitialized = true;
    } catch (e) {
      debugPrint('Service initialization failed: $e');
      rethrow;
    }
  }

  /// Safe disposal pattern
  Future<void> dispose() async {
    if (_isDisposed) return;

    try {
      await onDispose();
    } catch (e) {
      debugPrint('Service disposal failed: $e');
    } finally {
      _isDisposed = true;
      _isInitialized = false;
    }
  }

  /// Override this method to implement initialization logic
  Future<void> onInitialize();

  /// Override this method to implement disposal logic
  Future<void> onDispose();

  /// Ensure service is initialized before use
  void ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('Service must be initialized before use');
    }
    if (_isDisposed) {
      throw StateError('Service has been disposed');
    }
  }
}

/// Safe timer management utility
class SafeTimer {
  Timer? _timer;
  final String? _debugName;

  SafeTimer({String? debugName}) : _debugName = debugName;

  /// Check if timer is active
  bool get isActive => _timer?.isActive ?? false;

  /// Start a periodic timer safely
  void startPeriodic(Duration duration, void Function() callback) {
    cancel(); // Cancel any existing timer
    _timer = Timer.periodic(duration, (_) => callback());
    if (_debugName != null) {
      debugPrint('SafeTimer($_debugName): Started periodic timer');
    }
  }

  /// Start a one-time timer safely
  void startOnce(Duration duration, void Function() callback) {
    cancel(); // Cancel any existing timer
    _timer = Timer(duration, callback);
    if (_debugName != null) {
      debugPrint('SafeTimer($_debugName): Started one-time timer');
    }
  }

  /// Cancel the timer safely
  void cancel() {
    if (_timer?.isActive == true) {
      _timer!.cancel();
      if (_debugName != null) {
        debugPrint('SafeTimer($_debugName): Timer cancelled');
      }
    }
    _timer = null;
  }

  /// Dispose the timer (alias for cancel)
  Future<void> dispose() async => cancel();
}

/// Safe subscription management utility
class SafeSubscription<T> {
  StreamSubscription<T>? _subscription;
  final String? _debugName;

  SafeSubscription({String? debugName}) : _debugName = debugName;

  /// Check if subscription is active
  bool get isActive => _subscription != null;

  /// Subscribe to a stream safely
  void subscribe(Stream<T> stream, void Function(T) onData, {Function? onError, void Function()? onDone}) {
    cancel(); // Cancel any existing subscription
    _subscription = stream.listen(onData, onError: onError, onDone: onDone);
    if (_debugName != null) {
      debugPrint('SafeSubscription($_debugName): Subscribed to stream');
    }
  }

  /// Cancel the subscription safely
  Future<void> cancel() async {
    if (_subscription != null) {
      await _subscription!.cancel();
      if (_debugName != null) {
        debugPrint('SafeSubscription($_debugName): Subscription cancelled');
      }
    }
    _subscription = null;
  }

  /// Dispose the subscription (alias for cancel)
  Future<void> dispose() => cancel();
}

/// Safe resource manager for complex initialization patterns
class SafeResourceManager<T> {
  T? _resource;
  bool _isInitializing = false;
  bool _isDisposed = false;
  final Future<T> Function() _initializer;
  final Future<void> Function(T)? _disposer;
  final String? _debugName;

  SafeResourceManager({
    required Future<T> Function() initializer,
    Future<void> Function(T)? disposer,
    String? debugName,
  }) : _initializer = initializer,
       _disposer = disposer,
       _debugName = debugName;

  /// Get the resource, initializing if necessary
  Future<T> get resource async {
    if (_isDisposed) {
      throw StateError('Resource manager has been disposed');
    }

    if (_resource != null) {
      return _resource!;
    }

    if (_isInitializing) {
      // Wait for ongoing initialization
      while (_isInitializing && !_isDisposed) {
        await Future.delayed(const Duration(milliseconds: 10));
      }
      if (_resource != null) {
        return _resource!;
      }
    }

    _isInitializing = true;
    try {
      _resource = await _initializer();
      if (_debugName != null) {
        debugPrint('SafeResourceManager($_debugName): Resource initialized');
      }
      return _resource!;
    } finally {
      _isInitializing = false;
    }
  }

  /// Check if resource is initialized
  bool get isInitialized => _resource != null;

  /// Dispose the resource safely
  Future<void> dispose() async {
    if (_isDisposed) return;

    _isDisposed = true;

    if (_resource != null && _disposer != null) {
      try {
        await _disposer!(_resource!);
        if (_debugName != null) {
          debugPrint('SafeResourceManager($_debugName): Resource disposed');
        }
      } catch (e) {
        if (_debugName != null) {
          debugPrint('SafeResourceManager($_debugName): Disposal failed: $e');
        }
      }
    }

    _resource = null;
  }
}

/// Improved error handling with null safety
class SafeErrorHandler {
  /// Safely execute a function with error handling
  static T? safeExecute<T>(T Function() function, {T? fallback, String? debugName, bool logErrors = true}) {
    try {
      return function();
    } on Exception catch (e, stackTrace) {
      if (logErrors) {
        final name = debugName ?? 'SafeExecute';
        debugPrint('$name: Error occurred: $e');
        if (kDebugMode) {
          debugPrint('Stack trace: $stackTrace');
        }
      }
      return fallback;
    }
  }

  /// Safely execute an async function with error handling
  static Future<T?> safeExecuteAsync<T>(
    Future<T> Function() function, {
    T? fallback,
    String? debugName,
    bool logErrors = true,
  }) async {
    try {
      return await function();
    } catch (e, stackTrace) {
      if (logErrors) {
        final name = debugName ?? 'SafeExecuteAsync';
        debugPrint('$name: Error occurred: $e');
        if (kDebugMode) {
          debugPrint('Stack trace: $stackTrace');
        }
      }
      return fallback;
    }
  }
}

/// Safe initialization pattern for late variables
class LateInitializer<T> {
  T? _value;
  bool _isInitialized = false;
  final T Function() _initializer;
  final String? _debugName;

  LateInitializer(this._initializer, {String? debugName}) : _debugName = debugName;

  /// Get the value, initializing if necessary
  T get value {
    if (!_isInitialized) {
      _value = _initializer();
      _isInitialized = true;
      if (_debugName != null) {
        debugPrint('LateInitializer($_debugName): Value initialized');
      }
    }
    return _value!;
  }

  /// Check if value is initialized
  bool get isInitialized => _isInitialized;

  /// Reset the initializer (for testing purposes)
  void reset() {
    _value = null;
    _isInitialized = false;
    if (_debugName != null) {
      debugPrint('LateInitializer($_debugName): Reset');
    }
  }
}

/// Extension methods for better null safety on common types
extension SafeStringExtensions on String? {
  /// Get a non-null string with fallback
  String orDefault([String defaultValue = '']) => this ?? defaultValue;

  /// Check if string is null or empty
  bool get isNullOrEmpty => this?.isEmpty ?? true;

  /// Check if string is not null and not empty
  bool get isNotNullOrEmpty => !isNullOrEmpty;

  /// Safely trim the string
  String? get safeTrim => this?.trim();
}

extension SafeListExtensions<T> on List<T>? {
  /// Get a non-null list with fallback
  List<T> orEmpty() => this ?? <T>[];

  /// Check if list is null or empty
  bool get isNullOrEmpty => this?.isEmpty ?? true;

  /// Check if list is not null and not empty
  bool get isNotNullOrEmpty => !isNullOrEmpty;

  /// Safely get element at index
  T? safeElementAt(int index) {
    final list = this;
    if (list == null || index < 0 || index >= list.length) {
      return null;
    }
    return list[index];
  }
}

extension SafeMapExtensions<K, V> on Map<K, V>? {
  /// Get a non-null map with fallback
  Map<K, V> orEmpty() => this ?? <K, V>{};

  /// Check if map is null or empty
  bool get isNullOrEmpty => this?.isEmpty ?? true;

  /// Check if map is not null and not empty
  bool get isNotNullOrEmpty => !isNullOrEmpty;

  /// Safely get value for key
  V? safeGet(K key) => this?[key];
}
