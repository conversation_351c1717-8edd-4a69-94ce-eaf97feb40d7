import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/notifications/models/prayer_notification_settings.dart';
// Context7 MCP: Use unified notification provider for consolidated settings management
import '../../../../core/notifications/providers/unified_notification_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/logger.dart';
import '../../../../shared/widgets/modern_toggle_switch.dart';

/// Individual prayer toggle item widget
class PrayerToggleItem extends ConsumerWidget {
  /// Prayer key (e.g., 'Fajr', 'Dhuhr')
  final String prayerKey;

  /// Prayer display name
  final String prayerName;

  /// Whether dark mode is enabled
  final bool isDarkMode;

  /// Constructor
  const PrayerToggleItem({super.key, required this.prayerKey, required this.prayerName, required this.isDarkMode});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Context7 MCP: Get notification settings from unified provider
    final notificationSettingsAsync = ref.watch(unifiedNotificationSettingsProvider);

    return notificationSettingsAsync.when(
      loading: () => _buildLoadingState(),
      error: (error, stackTrace) {
        AppLogger.error('PrayerToggleItem: Failed to load notification settings - $error');
        return _buildErrorState(context, error);
      },
      data: (notificationSettings) {
        final isEnabled = notificationSettings.prayerSettings.prayerSettings[prayerKey]?.enabled ?? false;
        return _buildToggleWidget(context, ref, notificationSettings, isEnabled);
      },
    );
  }

  /// Context7 MCP: Build loading state widget with proper error handling
  Widget _buildLoadingState() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue[50]!, Colors.blue[100]!],
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [BoxShadow(color: Colors.black.withAlpha(15), blurRadius: 3, offset: const Offset(0, 2))],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(_getPrayerIcon(prayerKey), size: 18, color: AppColors.inactive),
                const SizedBox(width: 10),
                Text(
                  prayerName,
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.normal,
                    color: AppColors.textPrimaryLight,
                  ),
                ),
              ],
            ),
            const SizedBox(width: 50.0, height: 26.0, child: CircularProgressIndicator(strokeWidth: 2)),
          ],
        ),
      ),
    );
  }

  /// Context7 MCP: Build error state widget with comprehensive error display
  Widget _buildErrorState(BuildContext context, Object error) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.red[50]!, Colors.red[100]!],
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [BoxShadow(color: Colors.black.withAlpha(15), blurRadius: 3, offset: const Offset(0, 2))],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(Icons.error_outline, size: 18, color: Colors.red[600]),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    'Error loading $prayerName settings',
                    style: TextStyle(fontSize: 15, fontWeight: FontWeight.normal, color: Colors.red[700]),
                  ),
                ),
              ],
            ),
            Icon(Icons.warning, size: 20, color: Colors.red[600]),
          ],
        ),
      ),
    );
  }

  /// Context7 MCP: Build the main toggle widget with unified provider integration
  Widget _buildToggleWidget(BuildContext context, WidgetRef ref, dynamic notificationSettings, bool isEnabled) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0), // Reduced from 8.0
      decoration: BoxDecoration(
        // Use blue gradient for all items, with slightly darker shade for enabled
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isEnabled
              ? [Colors.blue[100]!, Colors.blue[200]!] // Darker blue for enabled
              : [Colors.blue[50]!, Colors.blue[100]!], // Standard blue for disabled
        ),
        borderRadius: BorderRadius.circular(8), // Match sidebar border radius
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15), // Match sidebar shadow
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0), // Reduced vertical from 10.0
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  _getPrayerIcon(prayerKey),
                  size: 18, // Reduced from 20
                  color: isEnabled ? Colors.blue[600]! : AppColors.inactive, // Match sidebar icon color
                ),
                const SizedBox(width: 10), // Reduced from 12
                Text(
                  prayerName,
                  style: TextStyle(
                    fontSize: 15, // Reduced from 16
                    fontWeight: isEnabled ? FontWeight.w600 : FontWeight.normal,
                    color: AppColors.textPrimaryLight, // Use consistent dark gray
                  ),
                ),
              ],
            ),
            ModernToggleSwitch(
              isActive: isEnabled,
              onToggle: (value) async {
                // Context7 MCP: Use unified provider for prayer notification toggle
                try {
                  AppLogger.debug('PrayerToggleItem: Toggling $prayerKey notification to $value');

                  final currentSettings = notificationSettings.prayerSettings;
                  final currentPrayerSettings =
                      currentSettings.prayerSettings[prayerKey] ?? const PrayerSettings(enabled: false);

                  final updatedPrayerSettings = currentPrayerSettings.copyWith(enabled: value);
                  final updatedPrayerSettingsMap = Map<String, PrayerSettings>.from(currentSettings.prayerSettings);
                  updatedPrayerSettingsMap[prayerKey] = updatedPrayerSettings;

                  final updatedSettings = currentSettings.copyWith(prayerSettings: updatedPrayerSettingsMap);

                  await ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(updatedSettings);

                  // Context7 MCP: Trigger notification rescheduling through unified manager
                  // Note: Notification rescheduling will be handled automatically by the unified provider
                  AppLogger.debug(
                    'PrayerToggleItem: Prayer notification settings updated, automatic rescheduling will occur',
                  );

                  AppLogger.info('PrayerToggleItem: Successfully updated $prayerKey notification setting');
                } on Exception catch (error) {
                  AppLogger.error('PrayerToggleItem: Failed to toggle $prayerKey notification - $error');
                  // Context7 MCP: Show user-friendly error feedback
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Failed to update $prayerName notification setting'),
                        backgroundColor: Colors.red[600],
                        duration: const Duration(seconds: 3),
                      ),
                    );
                  }
                }
              },
              width: 50.0, // Reduced from 56.0
              height: 26.0, // Reduced from 28.0
              activeColor: Colors.blue[600]!, // Match sidebar toggle color
              inactiveColor: AppColors.inactive,
              activeThumbColor: AppColors.textOnPrimary,
              inactiveThumbColor: AppColors.textOnPrimary,
            ),
          ],
        ),
      ),
    );
  }

  /// Get prayer icon based on prayer name
  IconData _getPrayerIcon(String prayerKey) {
    switch (prayerKey) {
      case 'Fajr':
        return Icons.wb_twilight;
      case 'Dhuhr':
        return Icons.wb_sunny_outlined;
      case 'Asr':
        return Icons.sunny_snowing;
      case 'Maghrib':
        return Icons.nightlight_round;
      case 'Isha':
        return Icons.nights_stay_outlined;
      default:
        return Icons.access_time;
    }
  }
}
