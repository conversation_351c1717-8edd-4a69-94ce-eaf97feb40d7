import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../errors/app_error.dart';
import '../../logging/app_logger.dart';
import '../../utils/result.dart';

part 'migration_utilities.g.dart';

/// Migration Utilities for Authentication Provider Consolidation
///
/// Following Context7 MCP best practices for provider migration:
/// - Provides automated migration tools and validation
/// - Implements rollback mechanisms for safety
/// - Offers migration progress tracking and reporting
/// - Ensures data integrity during migration process

/// Migration status tracking
enum MigrationStatus {
  /// Migration has not been started yet
  notStarted,

  /// Migration is currently in progress
  inProgress,

  /// Migration completed successfully
  completed,

  /// Migration failed with errors
  failed,

  /// Migration was rolled back due to issues
  rolledBack,
}

/// Migration phase tracking
enum MigrationPhase {
  /// Preparing for migration
  preparation,

  /// Backing up existing data
  dataBackup,

  /// Migrating provider configurations
  providerMigration,

  /// Validating migrated data
  dataValidation,

  /// Cleaning up old data
  cleanup,

  /// Final verification of migration
  verification,
}

/// Migration result with detailed information
class MigrationResult {
  /// Current status of the migration
  final MigrationStatus status;

  /// Current phase of the migration process
  final MigrationPhase currentPhase;

  /// Human-readable message about the migration
  final String message;

  /// Additional details about the migration
  final Map<String, dynamic> details;

  /// When this result was created
  final DateTime timestamp;

  /// Non-critical warnings encountered during migration
  final List<String> warnings;

  /// Critical errors encountered during migration
  final List<String> errors;

  /// Creates a new migration result
  const MigrationResult({
    required this.status,
    required this.currentPhase,
    required this.message,
    this.details = const {},
    required this.timestamp,
    this.warnings = const [],
    this.errors = const [],
  });

  /// Whether the migration completed successfully
  bool get isSuccess => status == MigrationStatus.completed;

  /// Whether the migration is currently in progress
  bool get isInProgress => status == MigrationStatus.inProgress;

  /// Whether the migration has failed
  bool get hasFailed => status == MigrationStatus.failed;

  /// Whether there are any warnings
  bool get hasWarnings => warnings.isNotEmpty;

  /// Whether there are any errors
  bool get hasErrors => errors.isNotEmpty;
}

/// Provider migration manager
@riverpod
class AuthProviderMigrationManager extends _$AuthProviderMigrationManager {
  @override
  MigrationResult build() {
    return MigrationResult(
      status: MigrationStatus.notStarted,
      currentPhase: MigrationPhase.preparation,
      message: 'Migration not started',
      timestamp: DateTime.now(),
    );
  }

  /// Start the authentication provider migration process
  ///
  /// This method orchestrates the complete migration from legacy
  /// authentication providers to the unified authentication system.
  Future<Result<MigrationResult>> startMigration({
    bool dryRun = false,
    bool backupData = true,
    bool validateIntegrity = true,
  }) async {
    try {
      AppLogger.info(
        'Starting authentication provider migration',
        context: {'dryRun': dryRun, 'backupData': backupData, 'validateIntegrity': validateIntegrity},
      );

      state = MigrationResult(
        status: MigrationStatus.inProgress,
        currentPhase: MigrationPhase.preparation,
        message: 'Preparing migration environment',
        timestamp: DateTime.now(),
      );

      // Phase 1: Preparation
      final preparationResult = await _prepareMigration();
      if (!preparationResult.isSuccess) {
        return _failMigration('Preparation phase failed', preparationResult.errorOrNull);
      }

      // Phase 2: Data Backup (if enabled)
      if (backupData) {
        state = state._copyWith(currentPhase: MigrationPhase.dataBackup, message: 'Creating data backup');

        final backupResult = await _backupAuthenticationData();
        if (!backupResult.isSuccess) {
          return _failMigration('Data backup failed', backupResult.errorOrNull);
        }
      }

      // Phase 3: Provider Migration
      state = state._copyWith(
        currentPhase: MigrationPhase.providerMigration,
        message: 'Migrating authentication providers',
      );

      final migrationResult = await _migrateProviders(dryRun: dryRun);
      if (!migrationResult.isSuccess) {
        return _failMigration('Provider migration failed', migrationResult.errorOrNull);
      }

      // Phase 4: Data Validation (if enabled)
      if (validateIntegrity) {
        state = state._copyWith(currentPhase: MigrationPhase.dataValidation, message: 'Validating data integrity');

        final validationResult = await _validateDataIntegrity();
        if (!validationResult.isSuccess) {
          return _failMigration('Data validation failed', validationResult.errorOrNull);
        }
      }

      // Phase 5: Cleanup
      if (!dryRun) {
        state = state._copyWith(currentPhase: MigrationPhase.cleanup, message: 'Cleaning up legacy providers');

        final cleanupResult = await _cleanupLegacyProviders();
        if (!cleanupResult.isSuccess) {
          AppLogger.warning('Cleanup phase had issues', error: cleanupResult.errorOrNull);
          // Don't fail migration for cleanup issues, just log warnings
        }
      }

      // Phase 6: Final Verification
      state = state._copyWith(currentPhase: MigrationPhase.verification, message: 'Performing final verification');

      final verificationResult = await _verifyMigration();
      if (!verificationResult.isSuccess) {
        return _failMigration('Final verification failed', verificationResult.errorOrNull);
      }

      // Migration completed successfully
      final completedResult = MigrationResult(
        status: MigrationStatus.completed,
        currentPhase: MigrationPhase.verification,
        message: dryRun ? 'Dry run completed successfully' : 'Migration completed successfully',
        timestamp: DateTime.now(),
        details: {
          'dryRun': dryRun,
          'backupCreated': backupData,
          'validationPerformed': validateIntegrity,
          'duration': DateTime.now().difference(state.timestamp).inSeconds,
        },
      );

      state = completedResult;

      AppLogger.info(
        'Authentication provider migration completed successfully',
        context: {'duration': completedResult.details['duration'], 'dryRun': dryRun},
      );

      return Result.success(completedResult);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Unexpected error during migration', error: e, stackTrace: stackTrace);
      return _failMigration('Unexpected error: ${e.toString()}', e);
    }
  }

  /// Rollback the migration to previous state
  Future<Result<MigrationResult>> rollbackMigration() async {
    try {
      AppLogger.info('Starting migration rollback');

      state = state._copyWith(status: MigrationStatus.inProgress, message: 'Rolling back migration');

      // Implement rollback logic here
      // This would restore from backup and revert provider changes

      final rolledBackResult = MigrationResult(
        status: MigrationStatus.rolledBack,
        currentPhase: MigrationPhase.verification,
        message: 'Migration rolled back successfully',
        timestamp: DateTime.now(),
      );

      state = rolledBackResult;

      AppLogger.info('Migration rollback completed');
      return Result.success(rolledBackResult);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Error during migration rollback', error: e, stackTrace: stackTrace);
      return _failMigration('Rollback failed: ${e.toString()}', e);
    }
  }

  /// Get detailed migration progress report
  Map<String, dynamic> getMigrationReport() {
    return {
      'status': state.status.name,
      'currentPhase': state.currentPhase.name,
      'message': state.message,
      'timestamp': state.timestamp.toIso8601String(),
      'hasWarnings': state.hasWarnings,
      'hasErrors': state.hasErrors,
      'warnings': state.warnings,
      'errors': state.errors,
      'details': state.details,
    };
  }

  // Private helper methods

  Future<Result<void>> _prepareMigration() async {
    // Validate environment and prerequisites
    // Check if UnifiedAuthManager is available
    // Verify all dependencies are ready
    return const Result.success(null);
  }

  Future<Result<void>> _backupAuthenticationData() async {
    // Create backup of current authentication state
    // Store secure tokens and session data
    // Create rollback point
    return const Result.success(null);
  }

  Future<Result<void>> _migrateProviders({required bool dryRun}) async {
    // Migrate from legacy providers to UnifiedAuthManager
    // Update provider registrations
    // Maintain backward compatibility
    return const Result.success(null);
  }

  Future<Result<void>> _validateDataIntegrity() async {
    // Verify all authentication data is intact
    // Check token validity and session consistency
    // Validate user data integrity
    return const Result.success(null);
  }

  Future<Result<void>> _cleanupLegacyProviders() async {
    // Remove deprecated provider files
    // Clean up unused dependencies
    // Update import statements
    return const Result.success(null);
  }

  Future<Result<void>> _verifyMigration() async {
    // Final verification that migration was successful
    // Test authentication flows
    // Verify all functionality works
    return const Result.success(null);
  }

  Result<MigrationResult> _failMigration(String message, dynamic error) {
    final failedResult = MigrationResult(
      status: MigrationStatus.failed,
      currentPhase: state.currentPhase,
      message: message,
      timestamp: DateTime.now(),
      errors: [error?.toString() ?? 'Unknown error'],
    );

    state = failedResult;
    AppLogger.error('Migration failed: $message', error: error);

    return Result.error(AppError.server('Migration failed: $message', code: 'migration_failed'));
  }
}

/// Extension to add copyWith functionality to MigrationResult
extension _MigrationResultExtension on MigrationResult {
  MigrationResult _copyWith({
    MigrationStatus? status,
    MigrationPhase? currentPhase,
    String? message,
    Map<String, dynamic>? details,
    DateTime? timestamp,
    List<String>? warnings,
    List<String>? errors,
  }) {
    return MigrationResult(
      status: status ?? this.status,
      currentPhase: currentPhase ?? this.currentPhase,
      message: message ?? this.message,
      details: details ?? this.details,
      timestamp: timestamp ?? this.timestamp,
      warnings: warnings ?? this.warnings,
      errors: errors ?? this.errors,
    );
  }
}
