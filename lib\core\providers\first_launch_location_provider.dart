// ignore_for_file: invalid_annotation_target

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../controllers/first_launch_location_controller.dart';
import '../errors/app_error.dart';
import '../logging/app_logger.dart';
import '../models/location_data.dart';
import '../utils/result.dart';

part 'first_launch_location_provider.g.dart';

// ============================================================================
// Context7 MCP: First Launch Location Management
// ============================================================================

/// Context7 MCP: First Launch Detection Provider
///
/// Detects if this is the first launch of the app and manages
/// the first launch location initialization sequence.
@riverpod
class FirstLaunchDetector extends _$FirstLaunchDetector {
  static const String _firstLaunchKey = 'app_first_launch_completed';

  @override
  Future<bool> build() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFirstLaunch = !(prefs.getBool(_firstLaunchKey) ?? false);

      AppLogger.info('FirstLaunchDetector: isFirstLaunch = $isFirstLaunch');
      return isFirstLaunch;
    } on Exception catch (e) {
      AppLogger.error('FirstLaunchDetector: Error checking first launch status', error: e);
      // Default to false (not first launch) on error to be safe
      return false;
    }
  }

  /// Mark first launch as completed
  Future<void> markFirstLaunchCompleted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_firstLaunchKey, true);

      // Update the provider state
      ref.invalidateSelf();

      AppLogger.info('FirstLaunchDetector: First launch marked as completed');
    } on Exception catch (e) {
      AppLogger.error('FirstLaunchDetector: Error marking first launch completed', error: e);
    }
  }
}

/// Context7 MCP: First Launch Location Initialization Provider
///
/// Manages the first launch location initialization process
/// with proper error handling and retry logic.
@riverpod
class FirstLaunchLocationInitializer extends _$FirstLaunchLocationInitializer {
  FirstLaunchLocationController? _controller;

  @override
  Future<Result<LocationData>?> build() async {
    // Check if this is first launch
    final isFirstLaunch = await ref.watch(firstLaunchDetectorProvider.future);

    if (!isFirstLaunch) {
      AppLogger.debug('FirstLaunchLocationInitializer: Not first launch, skipping initialization');
      return null;
    }

    AppLogger.info('FirstLaunchLocationInitializer: Starting first launch location initialization');

    // Create controller if not exists
    _controller ??= FirstLaunchLocationController();

    // Setup cleanup
    ref.onDispose(() {
      _controller?.dispose();
      _controller = null;
    });

    try {
      // Start initialization
      final result = await _controller!.initializeForFirstLaunch(
        isFirstLaunch: true,
        timeout: const Duration(minutes: 2),
      );

      if (result.isSuccess) {
        // Mark first launch as completed
        await ref.read(firstLaunchDetectorProvider.notifier).markFirstLaunchCompleted();
        AppLogger.info('FirstLaunchLocationInitializer: First launch initialization completed successfully');
      } else {
        AppLogger.error(
          'FirstLaunchLocationInitializer: First launch initialization failed',
          error: result.errorOrNull,
        );
      }

      return result;
    } on Exception catch (e, stackTrace) {
      AppLogger.error(
        'FirstLaunchLocationInitializer: Unexpected error during initialization',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(AppError.unknown('First launch initialization failed: $e'));
    }
  }

  /// Get current initialization phase
  InitializationPhase? get currentPhase => _controller?.currentPhase;

  /// Get initialization progress (0.0 to 1.0)
  double get initializationProgress => _controller?.initializationProgress ?? 0.0;

  /// Check if initialization is in progress
  bool get isInitializing => _controller?.isInitializing ?? false;

  /// Retry initialization manually
  Future<void> retryInitialization() async {
    if (_controller != null && !_controller!.isInitializing) {
      AppLogger.info('FirstLaunchLocationInitializer: Manual retry requested');
      ref.invalidateSelf();
    }
  }
}

/// Context7 MCP: First Launch Status Provider
///
/// Provides easy access to first launch status and initialization state.
@riverpod
class FirstLaunchStatus extends _$FirstLaunchStatus {
  @override
  Map<String, dynamic> build() {
    final isFirstLaunchAsync = ref.watch(firstLaunchDetectorProvider);
    final initializationAsync = ref.watch(firstLaunchLocationInitializerProvider);

    return isFirstLaunchAsync.when(
      data: (isFirstLaunch) {
        if (!isFirstLaunch) {
          return {
            'isFirstLaunch': false,
            'isInitializing': false,
            'isCompleted': true,
            'hasError': false,
            'progress': 1.0,
            'phase': 'completed',
          };
        }

        return initializationAsync.when(
          data: (result) {
            final initializer = ref.read(firstLaunchLocationInitializerProvider.notifier);

            return {
              'isFirstLaunch': true,
              'isInitializing': initializer.isInitializing,
              'isCompleted': result?.isSuccess ?? false,
              'hasError': result?.isFailure ?? false,
              'progress': initializer.initializationProgress,
              'phase': initializer.currentPhase?.name ?? 'idle',
              'error': result?.isFailure == true ? result!.errorOrNull.toString() : null,
              'location': result?.isSuccess == true ? result!.valueOrNull : null,
            };
          },
          loading: () => {
            'isFirstLaunch': true,
            'isInitializing': true,
            'isCompleted': false,
            'hasError': false,
            'progress': 0.0,
            'phase': 'loading',
          },
          error: (error, stackTrace) => {
            'isFirstLaunch': true,
            'isInitializing': false,
            'isCompleted': false,
            'hasError': true,
            'progress': 0.0,
            'phase': 'failed',
            'error': error.toString(),
          },
        );
      },
      loading: () => {
        'isFirstLaunch': null,
        'isInitializing': false,
        'isCompleted': false,
        'hasError': false,
        'progress': 0.0,
        'phase': 'checking',
      },
      error: (error, stackTrace) => {
        'isFirstLaunch': null,
        'isInitializing': false,
        'isCompleted': false,
        'hasError': true,
        'progress': 0.0,
        'phase': 'error',
        'error': error.toString(),
      },
    );
  }
}

// ============================================================================
// Context7 MCP: Convenience Selectors
// ============================================================================

/// Check if first launch initialization is in progress
@riverpod
bool isFirstLaunchInitializing(Ref ref) {
  final status = ref.watch(firstLaunchStatusProvider);
  return status['isInitializing'] as bool? ?? false;
}

/// Check if first launch initialization completed successfully
@riverpod
bool isFirstLaunchCompleted(Ref ref) {
  final status = ref.watch(firstLaunchStatusProvider);
  return status['isCompleted'] as bool? ?? false;
}

/// Get first launch initialization progress
@riverpod
double firstLaunchProgress(Ref ref) {
  final status = ref.watch(firstLaunchStatusProvider);
  return status['progress'] as double? ?? 0.0;
}

/// Get first launch location data if available
@riverpod
LocationData? firstLaunchLocationData(Ref ref) {
  final status = ref.watch(firstLaunchStatusProvider);
  return status['location'] as LocationData?;
}
