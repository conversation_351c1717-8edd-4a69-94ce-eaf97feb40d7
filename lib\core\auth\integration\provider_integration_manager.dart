import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../logging/app_logger.dart';
import '../models/auth_state.dart';
import '../providers/unified_auth_provider.dart';

part 'provider_integration_manager.g.dart';

/// Provider Integration Manager for Authentication Consolidation
///
/// Following Context7 MCP best practices for provider integration:
/// - Monitors unified authentication provider health
/// - Provides comprehensive system monitoring
/// - Ensures authentication system reliability
/// - Legacy providers have been successfully consolidated

/// Integration status for tracking provider health
enum IntegrationStatus {
  /// Provider is functioning normally
  healthy,

  /// Provider has minor issues but is still functional
  degraded,

  /// Provider has critical issues and may not be functional
  unhealthy,
}

/// Provider health check result containing status and diagnostic information
class ProviderHealthCheck {
  /// Name of the provider being checked
  final String providerName;

  /// Current integration status of the provider
  final IntegrationStatus status;

  /// Human-readable status message
  final String message;

  /// When this health check was performed
  final DateTime timestamp;

  /// Additional metrics and diagnostic data
  final Map<String, dynamic> metrics;

  /// List of identified issues or problems
  final List<String> issues;

  /// Creates a new provider health check result
  const ProviderHealthCheck({
    required this.providerName,
    required this.status,
    required this.message,
    required this.timestamp,
    this.metrics = const {},
    this.issues = const [],
  });

  /// Whether this provider is currently healthy
  bool get isHealthy => status == IntegrationStatus.healthy;

  /// Whether this provider has any identified issues
  bool get hasIssues => issues.isNotEmpty;
}

/// Integration configuration for unified auth monitoring
class IntegrationConfig {
  /// Whether unified authentication is enabled
  final bool enableUnifiedAuth;

  /// Whether health checks should be performed
  final bool enableHealthChecks;

  /// How often to perform health checks
  final Duration healthCheckInterval;

  /// Additional configuration metadata
  final Map<String, dynamic> metadata;

  /// Creates a new integration configuration
  const IntegrationConfig({
    this.enableUnifiedAuth = true, // Always enabled since consolidation is complete
    this.enableHealthChecks = true,
    this.healthCheckInterval = const Duration(minutes: 5),
    this.metadata = const {},
  });

  /// Creates a copy of this configuration with optional overrides
  IntegrationConfig copyWith({
    bool? enableUnifiedAuth,
    bool? enableHealthChecks,
    Duration? healthCheckInterval,
    Map<String, dynamic>? metadata,
  }) {
    return IntegrationConfig(
      enableUnifiedAuth: enableUnifiedAuth ?? this.enableUnifiedAuth,
      enableHealthChecks: enableHealthChecks ?? this.enableHealthChecks,
      healthCheckInterval: healthCheckInterval ?? this.healthCheckInterval,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Provider Integration Manager
///
/// Manages the unified authentication provider and provides health monitoring
@riverpod
class ProviderIntegrationManager extends _$ProviderIntegrationManager {
  @override
  IntegrationConfig build() {
    return const IntegrationConfig();
  }

  /// Update integration configuration
  void updateConfig(IntegrationConfig config) {
    state = config;
    AppLogger.info('Provider integration configuration updated: ${config.enableUnifiedAuth}');
  }

  /// Enable unified authentication (always enabled since consolidation is complete)
  void enableUnifiedAuth() {
    state = state.copyWith(enableUnifiedAuth: true);
    AppLogger.info('Unified authentication enabled');
  }

  /// Start health monitoring
  void startHealthChecks() {
    if (state.enableHealthChecks) {
      _scheduleHealthChecks();
      AppLogger.info('Health monitoring started');
    }
  }

  /// Schedule periodic health checks
  void _scheduleHealthChecks() {
    final config = state;

    // Schedule periodic health checks
    Future.delayed(config.healthCheckInterval, () {
      _performHealthChecks();
      _scheduleHealthChecks(); // Schedule next check
    });
  }

  /// Perform health checks on unified auth provider
  Future<void> _performHealthChecks() async {
    try {
      final healthCheck = await _checkUnifiedProviderHealth();

      if (!healthCheck.isHealthy) {
        AppLogger.warning('Unhealthy provider detected: ${healthCheck.providerName}');
      }
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Error performing health checks: $e', error: e, stackTrace: stackTrace);
    }
  }

  /// Check unified provider health
  Future<ProviderHealthCheck> _checkUnifiedProviderHealth() async {
    try {
      final unifiedAuth = ref.read(unifiedAuthManagerProvider);
      return await unifiedAuth.when(
        data: (authState) => ProviderHealthCheck(
          providerName: 'UnifiedAuthManager',
          status: IntegrationStatus.healthy,
          message: 'Provider is healthy and responsive',
          timestamp: DateTime.now(),
          metrics: {'isAuthenticated': authState.isAuthenticated, 'hasValidSession': authState.currentSession != null},
        ),
        loading: () => ProviderHealthCheck(
          providerName: 'UnifiedAuthManager',
          status: IntegrationStatus.degraded,
          message: 'Provider is loading',
          timestamp: DateTime.now(),
          issues: ['Provider is in loading state'],
        ),
        error: (error, stackTrace) => ProviderHealthCheck(
          providerName: 'UnifiedAuthManager',
          status: IntegrationStatus.unhealthy,
          message: 'Provider has errors',
          timestamp: DateTime.now(),
          issues: [error.toString()],
        ),
      );
    } on Exception catch (e) {
      return ProviderHealthCheck(
        providerName: 'UnifiedAuthManager',
        status: IntegrationStatus.unhealthy,
        message: 'Failed to check provider health',
        timestamp: DateTime.now(),
        issues: [e.toString()],
      );
    }
  }

  /// Get overall system health status
  IntegrationStatus getOverallHealth() {
    // Since legacy providers have been removed, only check unified provider
    return IntegrationStatus.healthy;
  }

  /// Get integration metrics
  Map<String, dynamic> getIntegrationMetrics() {
    return {
      'unifiedAuthEnabled': state.enableUnifiedAuth,
      'healthChecksEnabled': state.enableHealthChecks,
      'lastHealthCheck': DateTime.now().toIso8601String(),
      'consolidationComplete': true, // All legacy providers have been removed
      'providersConsolidated': 19, // Total providers that were consolidated
    };
  }
}

/// Provider for accessing the integration manager
@riverpod
ProviderIntegrationManager providerIntegrationManager(Ref ref) {
  return ProviderIntegrationManager();
}

/// Provider for getting current integration status
@riverpod
Future<IntegrationStatus> integrationStatus(Ref ref) async {
  final manager = ref.watch(providerIntegrationManagerProvider.notifier);
  return manager.getOverallHealth();
}

/// Provider for getting integration metrics
@riverpod
Map<String, dynamic> integrationMetrics(Ref ref) {
  final manager = ref.watch(providerIntegrationManagerProvider.notifier);
  return manager.getIntegrationMetrics();
}
