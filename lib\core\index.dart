/// Core System Exports
///
/// This file provides a single entry point for all core functionality
/// in the Masajid AlBahrain application.
///
/// **Usage:**
/// ```dart
/// import 'package:masajid_bh/core/index.dart';
///
/// // All core components are now available
/// final apiClient = UnifiedApiClient.instance;
/// final result = await someOperation();
/// result.when(
///   success: (data) => handleSuccess(data),
///   failure: (error) => handleError(error),
/// );
/// ```
library;

// Imports for implementation code
import 'network/index.dart';
import 'storage/secure_storage_service.dart';

// API Components
export 'api/index.dart';

// Error Handling
export 'errors/app_error.dart';
export 'errors/error_analytics_system.dart';
export 'errors/error_recovery_system.dart';
export 'errors/error_reporting_integration.dart';
export 'errors/unified_error_system.dart';

// Network System (Complete unified network functionality)
export 'network/index.dart';

// Providers
export 'providers/supabase_client_provider.dart';
export 'providers/supabase_connection_provider.dart';

// Security
export 'security/network/certificate_pinning_service.dart';
export 'security/network/secure_http_client.dart';

// Services
// Note: Network optimization is available through optimization_domains/network_optimization_domain.dart
// export 'services/optimization_domains/network_optimization_domain.dart';

// Storage
export 'storage/secure_storage_service.dart';

// Utilities
export 'utils/app_logger.dart';
export 'utils/datetime/index.dart';
export 'utils/result.dart';
export 'utils/validation/index.dart';

/// Core System Initialization Helper
///
/// Provides convenient methods to initialize the entire core system
/// with appropriate configurations for different environments.
class CoreSystemInitializer {
  /// Initialize the complete core system for production use
  static Future<void> initializeProduction() async {
    AppLogger.info('Initializing core system for production...');

    // Initialize network system
    await NetworkSystemInitializer.initializeProduction();

    // Initialize storage services
    await _initializeStorageServices();

    // Initialize security services
    await _initializeSecurityServices();

    AppLogger.info('Core system initialized for production');
  }

  /// Initialize the core system for development use
  static Future<void> initializeDevelopment() async {
    AppLogger.info('Initializing core system for development...');

    // Initialize network system
    await NetworkSystemInitializer.initializeDevelopment();

    // Initialize storage services
    await _initializeStorageServices();

    // Initialize security services (with development settings)
    await _initializeSecurityServices(isDevelopment: true);

    AppLogger.info('Core system initialized for development');
  }

  /// Initialize the core system for testing
  static Future<void> initializeTesting() async {
    AppLogger.info('Initializing core system for testing...');

    // Initialize network system
    await NetworkSystemInitializer.initializeTesting();

    // Minimal storage initialization for testing
    await _initializeStorageServices(isMinimal: true);

    AppLogger.info('Core system initialized for testing');
  }

  /// Initialize storage services
  static Future<void> _initializeStorageServices({bool isMinimal = false}) async {
    try {
      // Verify secure storage is accessible
      final instance = SecureStorageService.instance;

      // Test storage access
      await instance.containsKey(key: 'test_key');

      AppLogger.info('Storage services initialized');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to initialize storage services', e, stackTrace);
      rethrow;
    }
  }

  /// Initialize security services
  static Future<void> _initializeSecurityServices({bool isDevelopment = false}) async {
    try {
      // Initialize certificate pinning service
      final result = await CertificatePinningService.instance.initialize();

      result.when(
        success: (_) => AppLogger.info('Security services initialized'),
        failure: (error) {
          AppLogger.error('Failed to initialize certificate pinning: $error');
          throw error;
        },
      );
    } on Exception catch (e, stackTrace) {
      AppLogger.error('Failed to initialize security services', e, stackTrace);
      rethrow;
    }
  }

  /// Get comprehensive system health status
  static Future<Map<String, dynamic>> getSystemHealth() async {
    final networkHealth = await NetworkSystemInitializer.getSystemHealth();

    return {
      'overall_health': networkHealth['overall_health'],
      'network': networkHealth,
      'storage': await _getStorageHealth(),
      'security': await _getSecurityHealth(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Get storage system health
  static Future<Map<String, dynamic>> _getStorageHealth() async {
    try {
      // Test secure storage by checking if it's accessible
      await SecureStorageService.instance.containsKey(key: 'health_check');

      return {'secure_storage': true, 'overall_healthy': true};
    } on Exception catch (e) {
      return {'secure_storage': false, 'overall_healthy': false, 'error': e.toString()};
    }
  }

  /// Get security system health
  static Future<Map<String, dynamic>> _getSecurityHealth() async {
    try {
      // Certificate pinning service doesn't have isHealthy method
      // Just return true if the instance exists
      final _ = CertificatePinningService.instance;

      return {'certificate_pinning': true, 'overall_healthy': true};
    } on Exception catch (e) {
      return {'certificate_pinning': false, 'overall_healthy': false, 'error': e.toString()};
    }
  }

  /// Dispose all core system resources
  static void dispose() {
    AppLogger.info('Disposing core system...');

    // Dispose network system
    NetworkSystemInitializer.dispose();

    // Note: Storage and security services don't have dispose methods
    // They will be cleaned up automatically

    AppLogger.info('Core system disposed');
  }

  /// Reset all core system components
  static Future<void> reset() async {
    AppLogger.info('Resetting core system...');

    // Reset network system
    await NetworkSystemInitializer.reset();

    // Clear storage caches (use deleteAll instead of clearAll)
    await SecureStorageService.instance.deleteAll();

    AppLogger.info('Core system reset completed');
  }

  /// Get optimization recommendations for the entire system
  static Future<List<String>> getOptimizationRecommendations() async {
    final recommendations = <String>[];

    // Get network recommendations
    final networkRecommendations = NetworkSystemInitializer.getOptimizationRecommendations();
    recommendations.addAll(networkRecommendations);

    // Get system-level recommendations
    final systemHealth = await getSystemHealth();
    final overallHealth = systemHealth['overall_health'] as double;

    if (overallHealth < 70) {
      recommendations.add('Overall system health is below optimal. Consider reviewing all components.');
    }

    final storageHealth = systemHealth['storage'] as Map<String, dynamic>;
    if (!(storageHealth['overall_healthy'] as bool)) {
      recommendations.add('Storage system issues detected. Check secure storage and cache service.');
    }

    final securityHealth = systemHealth['security'] as Map<String, dynamic>;
    if (!(securityHealth['overall_healthy'] as bool)) {
      recommendations.add('Security system issues detected. Check certificate pinning service.');
    }

    return recommendations;
  }
}

/// Core System Configuration Presets
class CoreSystemPresets {
  /// Production configuration with all optimizations enabled
  static const production = CoreSystemConfig(
    environment: Environment.production,
    networkConfig: NetworkSystemPresets.highPerformance,
    enableDetailedLogging: false,
    enablePerformanceMonitoring: true,
    enableSecurityFeatures: true,
  );

  /// Development configuration with enhanced debugging
  static const development = CoreSystemConfig(
    environment: Environment.development,
    networkConfig: NetworkSystemPresets.development,
    enableDetailedLogging: true,
    enablePerformanceMonitoring: true,
    enableSecurityFeatures: false,
  );

  /// Testing configuration with minimal overhead
  static const testing = CoreSystemConfig(
    environment: Environment.testing,
    networkConfig: NetworkSystemPresets.batteryOptimized,
    enableDetailedLogging: false,
    enablePerformanceMonitoring: false,
    enableSecurityFeatures: false,
  );

  /// Battery-optimized configuration for mobile devices
  static const batteryOptimized = CoreSystemConfig(
    environment: Environment.production,
    networkConfig: NetworkSystemPresets.batteryOptimized,
    enableDetailedLogging: false,
    enablePerformanceMonitoring: false,
    enableSecurityFeatures: true,
  );
}

/// Environment enumeration
enum Environment {
  /// Development environment
  development,

  /// Testing environment
  testing,

  /// Production environment
  production,
}

/// Core system configuration container
class CoreSystemConfig {
  /// The environment this configuration is for
  final Environment environment;

  /// Network system configuration
  final NetworkSystemConfig networkConfig;

  /// Whether to enable detailed logging
  final bool enableDetailedLogging;

  /// Whether to enable performance monitoring
  final bool enablePerformanceMonitoring;

  /// Whether to enable security features
  final bool enableSecurityFeatures;

  /// Creates a new core system configuration
  const CoreSystemConfig({
    required this.environment,
    required this.networkConfig,
    required this.enableDetailedLogging,
    required this.enablePerformanceMonitoring,
    required this.enableSecurityFeatures,
  });

  /// Check if this is a development environment
  bool get isDevelopment => environment == Environment.development;

  /// Check if this is a production environment
  bool get isProduction => environment == Environment.production;

  /// Check if this is a testing environment
  bool get isTesting => environment == Environment.testing;
}
