import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/permission_models.dart';
import '../services/graceful_degradation_service.dart';

/// Graceful Degradation Status Widget
///
/// **Task 3.3.5: Implement graceful degradation for denied permissions**
///
/// This widget displays the current degradation status and provides user guidance
/// for improving functionality when permissions are denied, following Context7 MCP
/// UI patterns for graceful degradation presentation.
///
/// Features:
/// - Real-time degradation status display
/// - Alternative method suggestions
/// - User-friendly guidance messages
/// - Interactive improvement recommendations
/// - Visual impact indicators
/// - Accessibility-friendly interface
/// - Contextual help and support
/// - Progressive enhancement indicators
class GracefulDegradationStatusWidget extends ConsumerStatefulWidget {
  final bool showDetailedStatus;
  final bool enableInteractiveActions;
  final VoidCallback? onImprovementRequested;
  final VoidCallback? onHelpRequested;

  const GracefulDegradationStatusWidget({
    super.key,
    this.showDetailedStatus = true,
    this.enableInteractiveActions = true,
    this.onImprovementRequested,
    this.onHelpRequested,
  });

  @override
  ConsumerState<GracefulDegradationStatusWidget> createState() => _GracefulDegradationStatusWidgetState();
}

class _GracefulDegradationStatusWidgetState extends ConsumerState<GracefulDegradationStatusWidget>
    with TickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<double> _fadeAnimation;
  late final Animation<double> _scaleAnimation;

  DegradationStatus? _currentStatus;
  bool _isLoading = true;
  String? _error;

  late final GracefulDegradationService _degradationService;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _animationController = AnimationController(duration: const Duration(milliseconds: 500), vsync: this);

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.elasticOut));

    // Initialize degradation service
    _degradationService = GracefulDegradationService(ref: ref);

    // Load current status
    _loadDegradationStatus();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _degradationService.dispose();
    super.dispose();
  }

  /// Load current degradation status
  Future<void> _loadDegradationStatus() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final status = await _degradationService.getCurrentDegradationStatus();

      setState(() {
        _currentStatus = status;
        _isLoading = false;
      });

      // Start animation
      _animationController.forward();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(padding: const EdgeInsets.all(16), child: _buildContent()),
    );
  }

  /// Build main content
  Widget _buildContent() {
    if (_isLoading) {
      return _buildLoadingState();
    } else if (_error != null) {
      return _buildErrorState();
    } else if (_currentStatus != null) {
      return _buildStatusContent();
    } else {
      return _buildEmptyState();
    }
  }

  /// Build loading state
  Widget _buildLoadingState() {
    return const Column(
      mainAxisSize: MainAxisSize.min,
      children: [CircularProgressIndicator(), SizedBox(height: 12), Text('Checking degradation status...')],
    );
  }

  /// Build error state
  Widget _buildErrorState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.error_outline, color: Colors.red, size: 48),
        const SizedBox(height: 12),
        const Text('Failed to load degradation status', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Text(
          _error!,
          style: const TextStyle(color: Colors.grey),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        ElevatedButton(onPressed: _loadDegradationStatus, child: const Text('Retry')),
      ],
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return const Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.check_circle, color: Colors.green, size: 48),
        SizedBox(height: 12),
        Text(
          'All permissions granted',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green),
        ),
        SizedBox(height: 8),
        Text(
          'Full notification functionality is available',
          style: TextStyle(color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Build status content
  Widget _buildStatusContent() {
    final status = _currentStatus!;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatusHeader(status),
            if (status.hasActiveDegradations) ...[
              const SizedBox(height: 16),
              _buildDegradationSummary(status),
              if (widget.showDetailedStatus) ...[const SizedBox(height: 16), _buildDetailedStatus(status)],
              const SizedBox(height: 16),
              _buildActionButtons(status),
            ],
          ],
        ),
      ),
    );
  }

  /// Build status header
  Widget _buildStatusHeader(DegradationStatus status) {
    final impactColor = _getImpactColor(status.overallImpact);
    final impactIcon = _getImpactIcon(status.overallImpact);

    return Row(
      children: [
        Icon(impactIcon, color: impactColor, size: 32),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Notification Status', style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: 4),
              Text(
                status.statusSummary,
                style: TextStyle(color: impactColor, fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
        if (widget.enableInteractiveActions)
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadDegradationStatus, tooltip: 'Refresh status'),
      ],
    );
  }

  /// Build degradation summary
  Widget _buildDegradationSummary(DegradationStatus status) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getImpactColor(status.overallImpact).withValues(alpha: (0.1 * 255).round().toDouble()),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getImpactColor(status.overallImpact).withValues(alpha: (0.3 * 255).round().toDouble()),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Impact Summary', style: Theme.of(context).textTheme.titleSmall),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildSummaryChip(
                icon: Icons.block,
                label: 'Degradations',
                value: '${status.degradationCount}',
                color: Colors.orange,
              ),
              const SizedBox(width: 8),
              if (status.hasPermanentDegradations)
                _buildSummaryChip(
                  icon: Icons.lock,
                  label: 'Permanent',
                  value: '${status.permanentDegradationCount}',
                  color: Colors.red,
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build summary chip
  Widget _buildSummaryChip({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Chip(
      avatar: Icon(icon, size: 16, color: color),
      label: Text('$label: $value'),
      labelStyle: TextStyle(fontSize: 12, color: color),
      backgroundColor: color.withValues(alpha: (0.1 * 255).round().toDouble()),
      side: BorderSide(color: color.withValues(alpha: (0.3 * 255).round().toDouble())),
    );
  }

  /// Build detailed status
  Widget _buildDetailedStatus(DegradationStatus status) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Affected Features', style: Theme.of(context).textTheme.titleSmall),
        const SizedBox(height: 8),
        ...status.activeDegradations.entries.map((entry) {
          final type = entry.key;
          final info = entry.value;
          return _buildDegradationTile(type, info);
        }),
      ],
    );
  }

  /// Build degradation tile
  Widget _buildDegradationTile(PermissionNotificationType type, DegradationInfo info) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Icon(_getPermissionIcon(type), color: _getImpactColor(info.impact)),
      title: Text(_getPermissionDisplayName(type)),
      subtitle: Text(info.impactDescription),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (info.alternativesAvailable) const Icon(Icons.alt_route, size: 16, color: Colors.blue),
          if (info.isPermanent) const Icon(Icons.lock, size: 16, color: Colors.red),
        ],
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(DegradationStatus status) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (widget.enableInteractiveActions) ...[
          ElevatedButton.icon(
            onPressed: _showImprovementOptions,
            icon: const Icon(Icons.trending_up),
            label: const Text('Improve Functionality'),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _showAlternativeMethods,
                  icon: const Icon(Icons.alt_route),
                  label: const Text('Alternatives'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _showHelpAndSupport,
                  icon: const Icon(Icons.help_outline),
                  label: const Text('Help'),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  // ============================================================================
  // ACTION METHODS
  // ============================================================================

  /// Show improvement options
  void _showImprovementOptions() {
    if (_currentStatus == null) return;

    final deniedTypes = _currentStatus!.activeDegradations.keys.toList();
    final recommendations = _degradationService.getDegradationRecommendations(deniedTypes);

    showModalBottomSheet(context: context, builder: (context) => _buildImprovementSheet(recommendations));
  }

  /// Build improvement sheet
  Widget _buildImprovementSheet(List<DegradationRecommendation> recommendations) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Improvement Recommendations', style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 16),
          ...recommendations.map(
            (rec) => ListTile(
              leading: Icon(
                rec.isHighPriority ? Icons.priority_high : Icons.lightbulb,
                color: rec.isHighPriority ? Colors.red : Colors.blue,
              ),
              title: Text(rec.title),
              subtitle: Text(rec.description),
              trailing: rec.hasAction ? TextButton(onPressed: rec.action, child: Text(rec.actionText!)) : null,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onImprovementRequested?.call();
            },
            child: const Text('Request Permissions'),
          ),
        ],
      ),
    );
  }

  /// Show alternative methods
  void _showAlternativeMethods() {
    if (_currentStatus == null) return;

    final deniedTypes = _currentStatus!.activeDegradations.keys.toList();
    final alternatives = _degradationService.getAlternativeMethods(deniedTypes);

    showDialog(context: context, builder: (context) => _buildAlternativesDialog(alternatives));
  }

  /// Build alternatives dialog
  Widget _buildAlternativesDialog(List<AlternativeMethod> alternatives) {
    return AlertDialog(
      title: const Text('Alternative Methods'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: alternatives
              .map(
                (method) => ExpansionTile(
                  leading: Icon(
                    method.isHighlyEffective ? Icons.star : Icons.alt_route,
                    color: method.isHighlyEffective ? Colors.amber : Colors.blue,
                  ),
                  title: Text(method.name),
                  subtitle: Text('${method.effectivenessPercentage}% effective'),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(method.description),
                          if (method.requirements.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            Text('Requirements:', style: Theme.of(context).textTheme.titleSmall),
                            ...method.requirements.map((req) => Text('• $req')),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              )
              .toList(),
        ),
      ),
      actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('Close'))],
    );
  }

  /// Show help and support
  void _showHelpAndSupport() {
    if (_currentStatus == null) return;

    final deniedTypes = _currentStatus!.activeDegradations.keys.toList();
    final userMessages = _degradationService.getUserGuidance(deniedTypes);

    showDialog(context: context, builder: (context) => _buildHelpDialog(userMessages));
  }

  /// Build help dialog
  Widget _buildHelpDialog(List<UserMessage> userMessages) {
    return AlertDialog(
      title: const Text('Help & Guidance'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Here are some tips to improve your notification experience:'),
            const SizedBox(height: 16),
            ...userMessages.map(
              (message) => Card(
                child: ListTile(
                  leading: Icon(_getMessageTypeIcon(message.type)),
                  title: Text(message.title),
                  subtitle: Text(message.message),
                  trailing: message.hasAction
                      ? TextButton(onPressed: message.action, child: Text(message.actionText!))
                      : null,
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(onPressed: () => Navigator.pop(context), child: const Text('Close')),
        if (widget.onHelpRequested != null)
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onHelpRequested!();
            },
            child: const Text('Get More Help'),
          ),
      ],
    );
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /// Get impact color
  Color _getImpactColor(DegradationImpact impact) {
    switch (impact) {
      case DegradationImpact.none:
        return Colors.green;
      case DegradationImpact.minor:
        return Colors.blue;
      case DegradationImpact.moderate:
        return Colors.orange;
      case DegradationImpact.severe:
        return Colors.red;
    }
  }

  /// Get impact icon
  IconData _getImpactIcon(DegradationImpact impact) {
    switch (impact) {
      case DegradationImpact.none:
        return Icons.check_circle;
      case DegradationImpact.minor:
        return Icons.info;
      case DegradationImpact.moderate:
        return Icons.warning;
      case DegradationImpact.severe:
        return Icons.error;
    }
  }

  /// Get permission icon
  IconData _getPermissionIcon(PermissionNotificationType type) {
    switch (type) {
      case PermissionNotificationType.local:
        return Icons.notifications;
      case PermissionNotificationType.push:
        return Icons.cloud_queue;
      case PermissionNotificationType.scheduled:
        return Icons.schedule;
      case PermissionNotificationType.background:
        return Icons.layers;
      case PermissionNotificationType.critical:
        return Icons.priority_high;
      case PermissionNotificationType.provisional:
        return Icons.notifications_paused;
    }
  }

  /// Get permission display name
  String _getPermissionDisplayName(PermissionNotificationType type) {
    switch (type) {
      case PermissionNotificationType.local:
        return 'Local Notifications';
      case PermissionNotificationType.push:
        return 'Push Notifications';
      case PermissionNotificationType.scheduled:
        return 'Scheduled Notifications';
      case PermissionNotificationType.background:
        return 'Background Notifications';
      case PermissionNotificationType.critical:
        return 'Critical Alerts';
      case PermissionNotificationType.provisional:
        return 'Provisional Notifications';
    }
  }

  /// Get message type icon
  IconData _getMessageTypeIcon(UserMessageType type) {
    switch (type) {
      case UserMessageType.information:
        return Icons.info;
      case UserMessageType.warning:
        return Icons.warning;
      case UserMessageType.guidance:
        return Icons.lightbulb;
      case UserMessageType.error:
        return Icons.error;
    }
  }
}
