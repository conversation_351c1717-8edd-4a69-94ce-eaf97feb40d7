import 'package:flutter/material.dart';
import 'package:flutter_islamic_icons/flutter_islamic_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/notifications/models/prayer_notification_settings.dart';
import '../../../../core/notifications/providers/unified_notification_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/l10n.dart';
import '../../../../core/utils/logger.dart';
import '../../../prayer_times/domain/providers/custom_calculation_method_provider.dart';
import 'prayer_toggle_item.dart';

/// Prayer notifications section widget
class PrayerNotificationsSection extends ConsumerWidget {
  /// Whether the current language is Arabic
  final bool isArabic;

  /// Whether dark mode is enabled
  final bool isDarkMode;

  /// Constructor
  const PrayerNotificationsSection({super.key, required this.isArabic, required this.isDarkMode});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get custom calculation method
    final customMethod = ref.watch(customCalculationMethodProvider);

    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue[50]!, Colors.blue[100]!], // Match sidebar gradient
        ),
        borderRadius: BorderRadius.circular(8), // Match sidebar border radius
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15), // Match sidebar shadow
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0), // Reduced from 16.0
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      FlutterIslamicIcons.mosque,
                      color: Colors.blue[600]!, // Match sidebar icon color
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      context.l10n?.prayerNotifications ?? 'Prayer Notifications',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimaryLight, // Match sidebar text color
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    // Enable all button
                    ElevatedButton.icon(
                      onPressed: () async {
                        final currentSettings = await ref.read(unifiedNotificationSettingsProvider.future);

                        // Create enabled prayer settings for all prayers
                        final updatedPrayerSettings = Map<String, PrayerSettings>.from(
                          currentSettings.prayerSettings.prayerSettings,
                        );

                        // Enable all prayers
                        for (final prayerName in ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha']) {
                          final currentPrayerSetting = updatedPrayerSettings[prayerName];
                          updatedPrayerSettings[prayerName] = PrayerSettings(
                            enabled: true,
                            reminderMinutes: currentPrayerSetting?.reminderMinutes ?? [15],
                            followUpMinutes: currentPrayerSetting?.followUpMinutes ?? [5],
                            enableSound: currentPrayerSetting?.enableSound ?? true,
                            enableVibration: currentPrayerSetting?.enableVibration ?? true,
                            customSoundPath: currentPrayerSetting?.customSoundPath,
                            customVibrationPattern: currentPrayerSetting?.customVibrationPattern,
                          );
                        }

                        final newPrayerSettings = currentSettings.prayerSettings.copyWith(
                          prayerSettings: updatedPrayerSettings,
                        );

                        await ref
                            .read(unifiedNotificationSettingsProvider.notifier)
                            .updatePrayerSettings(newPrayerSettings);

                        // Context7 MCP: Notification rescheduling handled automatically by unified provider
                        AppLogger.info(
                          'PrayerNotificationsSection: All prayers enabled, automatic rescheduling will occur',
                        );
                      },
                      icon: const Icon(
                        Icons.check_circle_outline,
                        size: 14, // Reduced from 16
                      ),
                      label: Text(
                        context.l10n?.all ?? 'All',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 11, // Reduced from 12
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[600]!, // Match sidebar color
                        foregroundColor: AppColors.textOnPrimary,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6, // Reduced from 8
                          vertical: 0,
                        ),
                        minimumSize: const Size(0, 28), // Reduced from 32
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14), // Reduced from 16
                        ),
                      ),
                    ),
                    const SizedBox(width: 6), // Reduced from 8
                    // Disable all button
                    OutlinedButton.icon(
                      onPressed: () async {
                        final currentSettings = await ref.read(unifiedNotificationSettingsProvider.future);

                        // Create disabled prayer settings for all prayers
                        final updatedPrayerSettings = Map<String, PrayerSettings>.from(
                          currentSettings.prayerSettings.prayerSettings,
                        );

                        // Disable all prayers
                        for (final prayerName in ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha']) {
                          final currentPrayerSetting = updatedPrayerSettings[prayerName];
                          updatedPrayerSettings[prayerName] = PrayerSettings(
                            enabled: false,
                            reminderMinutes: currentPrayerSetting?.reminderMinutes ?? [15],
                            followUpMinutes: currentPrayerSetting?.followUpMinutes ?? [5],
                            enableSound: currentPrayerSetting?.enableSound ?? true,
                            enableVibration: currentPrayerSetting?.enableVibration ?? true,
                            customSoundPath: currentPrayerSetting?.customSoundPath,
                            customVibrationPattern: currentPrayerSetting?.customVibrationPattern,
                          );
                        }

                        final newPrayerSettings = currentSettings.prayerSettings.copyWith(
                          prayerSettings: updatedPrayerSettings,
                        );

                        await ref
                            .read(unifiedNotificationSettingsProvider.notifier)
                            .updatePrayerSettings(newPrayerSettings);

                        // Context7 MCP: Notification rescheduling handled automatically by unified provider
                        AppLogger.info(
                          'PrayerNotificationsSection: All prayers disabled, automatic rescheduling will occur',
                        );
                      },
                      icon: const Icon(Icons.hide_source, size: 14), // Reduced from 16
                      label: Text(
                        context.l10n?.none ?? 'None',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 11, // Reduced from 12
                        ),
                      ),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.textSecondaryLight,
                        side: BorderSide(color: AppColors.grey(300)),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6, // Reduced from 8
                          vertical: 0,
                        ),
                        minimumSize: const Size(0, 28), // Reduced from 32
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14), // Reduced from 16
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12), // Reduced from 16
            const Divider(height: 1),

            // Prayer toggles
            PrayerToggleItem(prayerKey: 'Fajr', prayerName: isArabic ? 'الفجر' : 'Fajr', isDarkMode: isDarkMode),

            PrayerToggleItem(prayerKey: 'Dhuhr', prayerName: isArabic ? 'الظهر' : 'Dhuhr', isDarkMode: isDarkMode),

            // Only show Asr for Sunni method
            if (customMethod != CustomCalculationMethod.jafari)
              PrayerToggleItem(prayerKey: 'Asr', prayerName: isArabic ? 'العصر' : 'Asr', isDarkMode: isDarkMode),

            PrayerToggleItem(prayerKey: 'Maghrib', prayerName: isArabic ? 'المغرب' : 'Maghrib', isDarkMode: isDarkMode),

            // Only show Isha for Sunni method
            if (customMethod != CustomCalculationMethod.jafari)
              PrayerToggleItem(prayerKey: 'Isha', prayerName: isArabic ? 'العشاء' : 'Isha', isDarkMode: isDarkMode),
          ],
        ),
      ),
    );
  }
}
