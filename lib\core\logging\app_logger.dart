// ignore_for_file: invalid_annotation_target

import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

// ==================== Log Levels ====================

/// Log level enumeration for filtering
///
/// Context7 MCP: Defines logging levels with priority values for filtering
/// and categorizing log messages throughout the application.
enum LogLevel {
  /// Debug level - detailed information for debugging purposes
  debug(0, 'DEBUG'),

  /// Info level - general informational messages
  info(1, 'INFO'),

  /// Warning level - potentially harmful situations
  warning(2, 'WARNING'),

  /// Error level - error events that might still allow the application to continue
  error(3, 'ERROR'),

  /// Critical level - very severe error events that may cause the application to abort
  critical(4, 'CRITICAL');

  /// Creates a LogLevel with the specified priority value and name
  const LogLevel(this.value, this.name);

  /// The numeric priority value of this log level
  final int value;

  /// The string name of this log level
  final String name;
}

/// Context7 MCP: Centralized application logging service
///
/// **Context7 MCP Pattern:** Structured logging with multiple levels
/// - Implements Singleton Pattern for global access
/// - Provides structured logging with context
/// - Supports different log levels for filtering
/// - Integrates with Flutter's debugging tools
/// - Handles production vs development logging
/// - Follows Single Responsibility Principle
class AppLogger {
  AppLogger._(); // Private constructor to prevent instantiation

  // ==================== Configuration ====================

  static LogLevel _minimumLogLevel = kDebugMode ? LogLevel.debug : LogLevel.info;
  static bool _enableConsoleLogging = true;
  static bool _enableDeveloperLogging = true;
  static String _logPrefix = '[MasajidBH]';

  /// Configure the logger settings
  ///
  /// **Context7 MCP Pattern:** Configuration with sensible defaults
  static void configure({
    LogLevel? minimumLogLevel,
    bool? enableConsoleLogging,
    bool? enableDeveloperLogging,
    String? logPrefix,
  }) {
    _minimumLogLevel = minimumLogLevel ?? _minimumLogLevel;
    _enableConsoleLogging = enableConsoleLogging ?? _enableConsoleLogging;
    _enableDeveloperLogging = enableDeveloperLogging ?? _enableDeveloperLogging;
    _logPrefix = logPrefix ?? _logPrefix;
  }

  // ==================== Core Logging Methods ====================

  /// Log a debug message
  ///
  /// **Context7 MCP Pattern:** Debug logging for development
  static void debug(
    String message, {
    String? tag,
    Map<String, dynamic>? context,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.debug, message, tag: tag, context: context, error: error, stackTrace: stackTrace);
  }

  /// Log an info message
  ///
  /// **Context7 MCP Pattern:** Information logging for tracking
  static void info(
    String message, {
    String? tag,
    Map<String, dynamic>? context,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.info, message, tag: tag, context: context, error: error, stackTrace: stackTrace);
  }

  /// Log a warning message
  ///
  /// **Context7 MCP Pattern:** Warning logging for potential issues
  static void warning(
    String message, {
    String? tag,
    Map<String, dynamic>? context,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.warning, message, tag: tag, context: context, error: error, stackTrace: stackTrace);
  }

  /// Log an error message
  ///
  /// **Context7 MCP Pattern:** Error logging with context
  static void error(
    String message, {
    String? tag,
    Map<String, dynamic>? context,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.error, message, tag: tag, context: context, error: error, stackTrace: stackTrace);
  }

  /// Log a critical error message
  ///
  /// **Context7 MCP Pattern:** Critical error logging for system failures
  static void critical(
    String message, {
    String? tag,
    Map<String, dynamic>? context,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.critical, message, tag: tag, context: context, error: error, stackTrace: stackTrace);
  }

  // ==================== Internal Implementation ====================

  /// Internal logging implementation
  ///
  /// **Context7 MCP Pattern:** Centralized logging logic
  static void _log(
    LogLevel level,
    String message, {
    String? tag,
    Map<String, dynamic>? context,
    Object? error,
    StackTrace? stackTrace,
  }) {
    // Check if log level meets minimum threshold
    if (level.value < _minimumLogLevel.value) return;

    // Build the log message
    final timestamp = DateTime.now().toIso8601String();
    final tagString = tag != null ? '[$tag]' : '';
    final contextString = context != null ? ' Context: ${_formatContext(context)}' : '';
    final errorString = error != null ? ' Error: $error' : '';

    final fullMessage = '$_logPrefix $timestamp [${level.name}] $tagString $message$contextString$errorString';

    // Output to console if enabled
    if (_enableConsoleLogging) {
      _logToConsole(level, fullMessage, error, stackTrace);
    }

    // Output to developer log if enabled
    if (_enableDeveloperLogging) {
      _logToDeveloper(level, fullMessage, error, stackTrace);
    }
  }

  /// Log to console with appropriate method
  ///
  /// **Context7 MCP Pattern:** Console logging with level-appropriate output
  static void _logToConsole(LogLevel level, String message, Object? error, StackTrace? stackTrace) {
    switch (level) {
      case LogLevel.debug:
        debugPrint(message);
        break;
      case LogLevel.info:
        debugPrint(message);
        break;
      case LogLevel.warning:
        debugPrint('⚠️ $message');
        break;
      case LogLevel.error:
        debugPrint('❌ $message');
        if (error != null && stackTrace != null) {
          debugPrint('Stack trace: $stackTrace');
        }
        break;
      case LogLevel.critical:
        debugPrint('🚨 $message');
        if (error != null && stackTrace != null) {
          debugPrint('Stack trace: $stackTrace');
        }
        break;
    }
  }

  /// Log to developer log
  ///
  /// **Context7 MCP Pattern:** Developer tools integration
  static void _logToDeveloper(LogLevel level, String message, Object? error, StackTrace? stackTrace) {
    developer.log(message, name: _logPrefix, level: _getDeveloperLogLevel(level), error: error, stackTrace: stackTrace);
  }

  /// Get developer log level for Flutter's developer tools
  ///
  /// **Context7 MCP Pattern:** Level mapping for developer tools
  static int _getDeveloperLogLevel(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 500; // Fine
      case LogLevel.info:
        return 800; // Info
      case LogLevel.warning:
        return 900; // Warning
      case LogLevel.error:
        return 1000; // Severe
      case LogLevel.critical:
        return 1200; // Shout
    }
  }

  /// Format context map for logging
  ///
  /// **Context7 MCP Pattern:** Context formatting for readability
  static String _formatContext(Map<String, dynamic> context) {
    final entries = context.entries.map((entry) => '${entry.key}=${entry.value}').join(', ');
    return '{$entries}';
  }

  // ==================== Specialized Logging Methods ====================

  /// Log location-related events
  ///
  /// **Context7 MCP Pattern:** Domain-specific logging
  static void location(
    String message, {
    double? latitude,
    double? longitude,
    double? accuracy,
    LogLevel level = LogLevel.info,
  }) {
    final context = <String, dynamic>{};
    if (latitude != null) context['latitude'] = latitude;
    if (longitude != null) context['longitude'] = longitude;
    if (accuracy != null) context['accuracy'] = accuracy;

    _log(level, message, tag: 'LOCATION', context: context.isNotEmpty ? context : null);
  }

  /// Log performance-related events
  ///
  /// **Context7 MCP Pattern:** Performance monitoring logging
  static void performance(
    String message, {
    Duration? duration,
    int? memoryUsage,
    double? batteryUsage,
    LogLevel level = LogLevel.info,
  }) {
    final context = <String, dynamic>{};
    if (duration != null) context['duration_ms'] = duration.inMilliseconds;
    if (memoryUsage != null) context['memory_mb'] = memoryUsage;
    if (batteryUsage != null) context['battery_usage'] = batteryUsage;

    _log(level, message, tag: 'PERFORMANCE', context: context.isNotEmpty ? context : null);
  }

  /// Log network-related events
  ///
  /// **Context7 MCP Pattern:** Network operation logging
  static void network(
    String message, {
    String? url,
    int? statusCode,
    Duration? responseTime,
    LogLevel level = LogLevel.info,
  }) {
    final context = <String, dynamic>{};
    if (url != null) context['url'] = url;
    if (statusCode != null) context['status_code'] = statusCode;
    if (responseTime != null) context['response_time_ms'] = responseTime.inMilliseconds;

    _log(level, message, tag: 'NETWORK', context: context.isNotEmpty ? context : null);
  }

  /// Log user interaction events
  ///
  /// **Context7 MCP Pattern:** User behavior logging
  static void userAction(
    String action, {
    String? screen,
    Map<String, dynamic>? parameters,
    LogLevel level = LogLevel.info,
  }) {
    final context = <String, dynamic>{'action': action};
    if (screen != null) context['screen'] = screen;
    if (parameters != null) context.addAll(parameters);

    _log(level, 'User action: $action', tag: 'USER_ACTION', context: context);
  }
}
