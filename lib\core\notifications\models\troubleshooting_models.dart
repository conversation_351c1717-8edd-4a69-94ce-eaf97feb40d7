import 'permission_models.dart';

/// Troubleshooting Models
///
/// **Task 3.3.4: Create permission troubleshooting guides and helpers**
///
/// This file contains all data models for the permission troubleshooting system
/// following Context7 MCP patterns for data modeling and validation.

/// Troubleshooting Guide
///
/// Comprehensive guide for resolving permission issues.
class TroubleshootingGuide {
  final String id;
  final String title;
  final String description;
  final List<PermissionNotificationType> deniedTypes;
  final PermissionIssueAnalysis analysis;
  final List<TroubleshootingStep> steps;
  final List<TroubleshootingSolution> commonSolutions;
  final List<PlatformGuidance> platformGuidance;
  final SystemInformation? systemInfo;
  final TroubleshootingLevel level;
  final DateTime generatedAt;

  const TroubleshootingGuide({
    required this.id,
    required this.title,
    required this.description,
    required this.deniedTypes,
    required this.analysis,
    required this.steps,
    required this.commonSolutions,
    required this.platformGuidance,
    this.systemInfo,
    required this.level,
    required this.generatedAt,
  });

  /// Get estimated completion time
  Duration get estimatedCompletionTime {
    return steps.fold(Duration.zero, (total, step) => total + step.estimatedTime);
  }

  /// Get difficulty level
  TroubleshootingDifficulty get overallDifficulty {
    final difficulties = steps.map((step) => step.difficulty).toList();
    if (difficulties.contains(TroubleshootingDifficulty.hard)) {
      return TroubleshootingDifficulty.hard;
    } else if (difficulties.contains(TroubleshootingDifficulty.medium)) {
      return TroubleshootingDifficulty.medium;
    } else {
      return TroubleshootingDifficulty.easy;
    }
  }

  /// Get success probability
  double get successProbability {
    if (commonSolutions.isEmpty) return 0.5;

    final avgSuccessRate =
        commonSolutions.map((solution) => solution.successRate).reduce((a, b) => a + b) / commonSolutions.length;

    return avgSuccessRate;
  }
}

/// Troubleshooting Level
enum TroubleshootingLevel { basic, intermediate, comprehensive }

/// Troubleshooting Step
///
/// Individual step in the troubleshooting process.
class TroubleshootingStep {
  final String id;
  final String title;
  final String description;
  final List<String> instructions;
  final int priority;
  final Duration estimatedTime;
  final TroubleshootingDifficulty difficulty;
  final String? warning;
  final List<String> prerequisites;
  final String? successCriteria;

  const TroubleshootingStep({
    required this.id,
    required this.title,
    required this.description,
    required this.instructions,
    required this.priority,
    required this.estimatedTime,
    required this.difficulty,
    this.warning,
    this.prerequisites = const [],
    this.successCriteria,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is TroubleshootingStep && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Troubleshooting Difficulty
enum TroubleshootingDifficulty { easy, medium, hard }

/// Troubleshooting Solution
///
/// Common solution for permission issues.
class TroubleshootingSolution {
  final String id;
  final String title;
  final String description;
  final List<String> steps;
  final double successRate;
  final TroubleshootingDifficulty difficulty;
  final Duration? estimatedTime;
  final List<String> requirements;

  const TroubleshootingSolution({
    required this.id,
    required this.title,
    required this.description,
    required this.steps,
    required this.successRate,
    required this.difficulty,
    this.estimatedTime,
    this.requirements = const [],
  });

  /// Check if solution is highly effective
  bool get isHighlyEffective => successRate >= 0.8;

  /// Check if solution is quick
  bool get isQuickFix => estimatedTime != null && estimatedTime!.inMinutes <= 2;
}

/// Platform Guidance
///
/// Platform-specific troubleshooting guidance.
class PlatformGuidance {
  final String platform;
  final String version;
  final String guidance;
  final List<String> specificSteps;
  final List<String> commonIssues;
  final List<String> limitations;

  const PlatformGuidance({
    required this.platform,
    required this.version,
    required this.guidance,
    required this.specificSteps,
    required this.commonIssues,
    this.limitations = const [],
  });
}

/// System Information
///
/// System information for troubleshooting context.
class SystemInformation {
  final String platform;
  final String platformVersion;
  final bool isPhysicalDevice;
  final String locale;
  final String timezone;
  final String dartVersion;
  final DateTime gatherTime;

  const SystemInformation({
    required this.platform,
    required this.platformVersion,
    required this.isPhysicalDevice,
    required this.locale,
    required this.timezone,
    required this.dartVersion,
    required this.gatherTime,
  });

  /// Get platform display name
  String get platformDisplayName {
    switch (platform.toLowerCase()) {
      case 'android':
        return 'Android $platformVersion';
      case 'ios':
        return 'iOS $platformVersion';
      case 'web':
        return 'Web Browser';
      default:
        return '$platform $platformVersion';
    }
  }

  /// Check if system is recent
  bool get isRecentSystem {
    final now = DateTime.now();
    return now.difference(gatherTime).inHours < 1;
  }
}

/// Permission Issue Analysis
///
/// Analysis of permission-related issues.
class PermissionIssueAnalysis {
  final List<PermissionNotificationType> deniedTypes;
  final List<PermissionIssueType> issues;
  final List<String> causes;
  final List<String> impacts;
  final TroubleshootingSeverity severity;
  final DateTime analysisTime;

  const PermissionIssueAnalysis({
    required this.deniedTypes,
    required this.issues,
    required this.causes,
    required this.impacts,
    required this.severity,
    required this.analysisTime,
  });

  /// Get primary issue
  PermissionIssueType? get primaryIssue => issues.isNotEmpty ? issues.first : null;

  /// Get most likely cause
  String? get mostLikelyCause => causes.isNotEmpty ? causes.first : null;

  /// Get primary impact
  String? get primaryImpact => impacts.isNotEmpty ? impacts.first : null;

  /// Check if analysis is recent
  bool get isRecent => DateTime.now().difference(analysisTime).inMinutes < 30;
}

/// Permission Issue Type
enum PermissionIssueType {
  localNotificationsDenied,
  pushNotificationsDenied,
  backgroundNotificationsDenied,
  criticalAlertsDenied,
  scheduledNotificationsDenied,
  provisionalNotificationsDenied,
  systemRestrictions,
  batteryOptimization,
  networkConnectivity,
  appConfiguration,
}

/// Permission Type Analysis
///
/// Analysis for specific permission type.
class PermissionTypeAnalysis {
  final List<PermissionIssueType> issues;
  final List<String> causes;
  final List<String> impacts;

  const PermissionTypeAnalysis({required this.issues, required this.causes, required this.impacts});
}

/// Troubleshooting Diagnosis
///
/// Result of automated troubleshooting diagnosis.
class TroubleshootingDiagnosis {
  final String id;
  final List<TroubleshootingIssue> issues;
  final List<String> recommendations;
  final TroubleshootingSeverity severity;
  final PermissionStatusReport permissionStatus;
  final SystemInformation systemInfo;
  final DateTime diagnosisTime;

  const TroubleshootingDiagnosis({
    required this.id,
    required this.issues,
    required this.recommendations,
    required this.severity,
    required this.permissionStatus,
    required this.systemInfo,
    required this.diagnosisTime,
  });

  /// Get critical issues
  List<TroubleshootingIssue> get criticalIssues =>
      issues.where((issue) => issue.severity == TroubleshootingSeverity.critical).toList();

  /// Get auto-fixable issues
  List<TroubleshootingIssue> get autoFixableIssues => issues.where((issue) => issue.autoFixable).toList();

  /// Check if diagnosis requires immediate attention
  bool get requiresImmediateAttention => severity == TroubleshootingSeverity.critical;

  /// Get diagnosis summary
  String get summary {
    if (issues.isEmpty) {
      return 'No issues detected';
    } else if (criticalIssues.isNotEmpty) {
      return '${criticalIssues.length} critical issues found';
    } else {
      return '${issues.length} issues detected';
    }
  }
}

/// Troubleshooting Issue
///
/// Individual issue found during diagnosis.
class TroubleshootingIssue {
  final String id;
  final String title;
  final String description;
  final TroubleshootingSeverity severity;
  final TroubleshootingCategory category;
  final bool autoFixable;
  final String? resolution;
  final List<String> affectedFeatures;

  const TroubleshootingIssue({
    required this.id,
    required this.title,
    required this.description,
    required this.severity,
    required this.category,
    required this.autoFixable,
    this.resolution,
    this.affectedFeatures = const [],
  });

  /// Get severity color for UI
  String get severityColor {
    switch (severity) {
      case TroubleshootingSeverity.critical:
        return '#F44336'; // Red
      case TroubleshootingSeverity.high:
        return '#FF9800'; // Orange
      case TroubleshootingSeverity.medium:
        return '#FFC107'; // Amber
      case TroubleshootingSeverity.low:
        return '#4CAF50'; // Green
    }
  }

  /// Get severity icon
  String get severityIcon {
    switch (severity) {
      case TroubleshootingSeverity.critical:
        return '🚨';
      case TroubleshootingSeverity.high:
        return '⚠️';
      case TroubleshootingSeverity.medium:
        return '⚡';
      case TroubleshootingSeverity.low:
        return 'ℹ️';
    }
  }
}

/// Troubleshooting Severity
enum TroubleshootingSeverity { critical, high, medium, low }

/// Troubleshooting Category
enum TroubleshootingCategory {
  permissions,
  systemSettings,
  appSettings,
  networkConnectivity,
  compatibility,
  configuration,
}

/// Quick Fix
///
/// Quick solution for common issues.
class QuickFix {
  final String id;
  final String title;
  final String description;
  final String action;
  final int priority;
  final Duration estimatedTime;

  const QuickFix({
    required this.id,
    required this.title,
    required this.description,
    required this.action,
    required this.priority,
    required this.estimatedTime,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is QuickFix && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  /// Check if this is a very quick fix
  bool get isVeryQuick => estimatedTime.inMinutes <= 1;

  /// Check if this is high priority
  bool get isHighPriority => priority <= 3;
}

/// Auto Resolution Result
///
/// Result of automatic issue resolution attempt.
class AutoResolutionResult {
  final int totalAttempted;
  final int resolvedCount;
  final Map<PermissionNotificationType, bool> results;
  final List<String> errors;
  final bool isFullyResolved;
  final bool requiresManualIntervention;

  const AutoResolutionResult({
    required this.totalAttempted,
    required this.resolvedCount,
    required this.results,
    required this.errors,
    required this.isFullyResolved,
    required this.requiresManualIntervention,
  });

  /// Get resolution percentage
  double get resolutionPercentage {
    if (totalAttempted == 0) return 0.0;
    return resolvedCount / totalAttempted;
  }

  /// Get failed permissions
  List<PermissionNotificationType> get failedPermissions {
    return results.entries.where((entry) => !entry.value).map((entry) => entry.key).toList();
  }

  /// Get resolved permissions
  List<PermissionNotificationType> get resolvedPermissions {
    return results.entries.where((entry) => entry.value).map((entry) => entry.key).toList();
  }

  /// Check if resolution was successful
  bool get wasSuccessful => resolvedCount > 0;

  /// Get result summary
  String get summary {
    if (isFullyResolved) {
      return 'All permissions resolved automatically';
    } else if (resolvedCount > 0) {
      return '$resolvedCount of $totalAttempted permissions resolved';
    } else {
      return 'No permissions could be resolved automatically';
    }
  }
}

/// Support Information
///
/// Information package for support requests.
class SupportInformation {
  final String id;
  final DateTime generatedAt;
  final String appVersion;
  final Map<String, String> platformInfo;
  final Map<String, dynamic> supportData;

  const SupportInformation({
    required this.id,
    required this.generatedAt,
    required this.appVersion,
    required this.platformInfo,
    required this.supportData,
  });

  /// Get formatted support data
  String get formattedSupportData {
    final buffer = StringBuffer();
    buffer.writeln('Support Information');
    buffer.writeln('==================');
    buffer.writeln('Generated: $generatedAt');
    buffer.writeln('App Version: $appVersion');
    buffer.writeln();

    buffer.writeln('Platform Information:');
    platformInfo.forEach((key, value) {
      buffer.writeln('  $key: $value');
    });
    buffer.writeln();

    buffer.writeln('Support Data:');
    supportData.forEach((key, value) {
      buffer.writeln('  $key: $value');
    });

    return buffer.toString();
  }

  /// Check if information is recent
  bool get isRecent => DateTime.now().difference(generatedAt).inHours < 24;

  /// Get data size estimate
  int get estimatedDataSize => formattedSupportData.length;
}

/// Troubleshooting Exception
///
/// Exception thrown during troubleshooting operations.
class TroubleshootingException implements Exception {
  final String message;
  final StackTrace? stackTrace;
  final String? code;

  const TroubleshootingException(this.message, {this.stackTrace, this.code});

  @override
  String toString() {
    if (code != null) {
      return 'TroubleshootingException [$code]: $message';
    } else {
      return 'TroubleshootingException: $message';
    }
  }
}
