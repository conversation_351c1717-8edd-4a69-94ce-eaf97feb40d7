import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:path_provider/path_provider.dart';

/// A utility class for monitoring and recording performance metrics in the app
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  // Performance metrics
  final Map<String, dynamic> _metrics = {
    'startupTime': null,
    'navigationTimes': <String, int>{},
    'frameMetrics': <String, dynamic>{
      'totalFrames': 0,
      'jankFrames': 0,
      'averageFrameTime': 0.0,
      'worstFrameTime': 0.0,
    },
    'apiCallTimes': <String, int>{},
    'renderTimes': <String, int>{},
  };

  // Stopwatches for timing
  final Map<String, Stopwatch> _stopwatches = {};

  // Frame timing
  Ticker? _ticker;
  List<Duration> _frameTimes = [];
  Duration? _lastFrameTime;
  bool _isMonitoringFrames = false;

  /// Start measuring app startup time
  void startMeasuringStartupTime() {
    _stopwatches['startup'] = Stopwatch()..start();
  }

  /// Stop measuring app startup time
  void stopMeasuringStartupTime() {
    if (_stopwatches.containsKey('startup') && _stopwatches['startup']!.isRunning) {
      _stopwatches['startup']!.stop();
      _metrics['startupTime'] = _stopwatches['startup']!.elapsedMilliseconds;
      debugPrint('App startup time: ${_metrics['startupTime']}ms');
    }
  }

  /// Start measuring navigation time to a specific route
  void startMeasuringNavigation(String routeName) {
    _stopwatches['navigation_$routeName'] = Stopwatch()..start();
  }

  /// Stop measuring navigation time to a specific route
  void stopMeasuringNavigation(String routeName) {
    final key = 'navigation_$routeName';
    if (_stopwatches.containsKey(key) && _stopwatches[key]!.isRunning) {
      _stopwatches[key]!.stop();
      final time = _stopwatches[key]!.elapsedMilliseconds;
      (_metrics['navigationTimes'] as Map<String, int>)[routeName] = time;
      debugPrint('Navigation to $routeName: ${time}ms');
    }
  }

  /// Start measuring API call time
  void startMeasuringApiCall(String apiName) {
    _stopwatches['api_$apiName'] = Stopwatch()..start();
  }

  /// Stop measuring API call time
  void stopMeasuringApiCall(String apiName) {
    final key = 'api_$apiName';
    if (_stopwatches.containsKey(key) && _stopwatches[key]!.isRunning) {
      _stopwatches[key]!.stop();
      final time = _stopwatches[key]!.elapsedMilliseconds;
      (_metrics['apiCallTimes'] as Map<String, int>)[apiName] = time;
      debugPrint('API call to $apiName: ${time}ms');
    }
  }

  /// Start measuring render time for a specific widget
  void startMeasuringRender(String widgetName) {
    _stopwatches['render_$widgetName'] = Stopwatch()..start();
  }

  /// Stop measuring render time for a specific widget
  void stopMeasuringRender(String widgetName) {
    final key = 'render_$widgetName';
    if (_stopwatches.containsKey(key) && _stopwatches[key]!.isRunning) {
      _stopwatches[key]!.stop();
      final time = _stopwatches[key]!.elapsedMilliseconds;
      (_metrics['renderTimes'] as Map<String, int>)[widgetName] = time;
      debugPrint('Render time for $widgetName: ${time}ms');
    }
  }

  /// Start monitoring frame times
  void startMonitoringFrames() {
    if (_isMonitoringFrames) return;

    _isMonitoringFrames = true;
    _frameTimes = [];
    _lastFrameTime = null;

    _ticker = Ticker((elapsed) {
      if (_lastFrameTime != null) {
        final frameTime = elapsed - _lastFrameTime!;
        _frameTimes.add(frameTime);

        // Update metrics
        _metrics['frameMetrics']['totalFrames'] = _frameTimes.length;

        // Count jank frames (frames that take longer than 16.67ms - 60fps)
        if (frameTime.inMicroseconds > 16670) {
          _metrics['frameMetrics']['jankFrames'] = (_metrics['frameMetrics']['jankFrames'] as int) + 1;
        }

        // Calculate average frame time
        final totalTime = _frameTimes.fold<Duration>(Duration.zero, (prev, curr) => prev + curr);
        _metrics['frameMetrics']['averageFrameTime'] = totalTime.inMicroseconds / _frameTimes.length / 1000;

        // Track worst frame time
        final worstFrame = _frameTimes.fold<Duration>(
          Duration.zero,
          (prev, curr) => prev.inMicroseconds > curr.inMicroseconds ? prev : curr,
        );
        _metrics['frameMetrics']['worstFrameTime'] = worstFrame.inMicroseconds / 1000;
      }

      _lastFrameTime = elapsed;
    });

    _ticker!.start();
  }

  /// Stop monitoring frame times
  void stopMonitoringFrames() {
    if (!_isMonitoringFrames) return;

    _ticker?.stop();
    _ticker?.dispose();
    _ticker = null;
    _isMonitoringFrames = false;

    // Log frame metrics
    debugPrint('Frame metrics:');
    debugPrint('  Total frames: ${_metrics['frameMetrics']['totalFrames']}');
    debugPrint('  Jank frames: ${_metrics['frameMetrics']['jankFrames']}');
    debugPrint(
      '  Jank percentage: ${(_metrics['frameMetrics']['jankFrames'] / _metrics['frameMetrics']['totalFrames'] * 100).toStringAsFixed(2)}%',
    );
    debugPrint('  Average frame time: ${_metrics['frameMetrics']['averageFrameTime'].toStringAsFixed(2)}ms');
    debugPrint('  Worst frame time: ${_metrics['frameMetrics']['worstFrameTime'].toStringAsFixed(2)}ms');
  }

  /// Save performance metrics to a file
  Future<String> saveMetricsToFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/performance_metrics.json');

      // Add timestamp
      _metrics['timestamp'] = DateTime.now().toIso8601String();

      // Write to file
      await file.writeAsString(jsonEncode(_metrics));

      debugPrint('Performance metrics saved to: ${file.path}');
      return file.path;
    } on Exception catch (e) {
      debugPrint('Error saving performance metrics: $e');
      return '';
    }
  }

  /// Get the current metrics
  Map<String, dynamic> getMetrics() {
    return Map.from(_metrics);
  }

  /// Reset all metrics
  void resetMetrics() {
    _metrics['startupTime'] = null;
    (_metrics['navigationTimes'] as Map).clear();
    _metrics['frameMetrics']['totalFrames'] = 0;
    _metrics['frameMetrics']['jankFrames'] = 0;
    _metrics['frameMetrics']['averageFrameTime'] = 0.0;
    _metrics['frameMetrics']['worstFrameTime'] = 0.0;
    (_metrics['apiCallTimes'] as Map).clear();
    (_metrics['renderTimes'] as Map).clear();

    _stopwatches.clear();
    _frameTimes = [];
  }
}
