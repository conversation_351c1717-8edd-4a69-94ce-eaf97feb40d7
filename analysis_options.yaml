# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

analyzer:
  plugins:
    - custom_lint
  # Stricter type checking for better type safety
  strong-mode:
    # Disable implicit casts for stricter type checking
    implicit-casts: false
    # Disable implicit dynamic for better type annotations
    implicit-dynamic: false
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.mocks.dart"
    - "scripts/**"  # Exclude scripts from analysis
    - "test/**"     # Exclude test files from strict documentation requirements
    - "integration_test/**"  # Exclude integration tests
    - "lib/core/sync/**"  # Exclude sync system (internal implementation)
    - "lib/core/services/**"  # Exclude internal services
    - "lib/core/utils/**"  # Exclude utility classes
    - "lib/core/widgets/**"  # Exclude internal widgets
    - "backup_deprecated_providers/**"  # Exclude deprecated backup providers from analysis
    - "lib/shared/widgets/**"  # Exclude shared widgets (well documented in code)
    - "lib/features/**/data/**"  # Exclude data layer (implementation details)
    - "lib/features/**/domain/usecases/**"  # Exclude use cases (self-explanatory)
    - "lib/test_*.dart"  # Exclude test files in lib
    - "lib/performance/**"  # Exclude performance monitoring
  errors:
    # Temporarily ignore Freezed mixin issues
    non_abstract_class_inherits_abstract_member: ignore
    # Ignore invalid annotation target warnings (Freezed + json_serializable issue)
    invalid_annotation_target: ignore
    # Allow print statements in scripts and tests
    avoid_print: ignore
    # Treat missing documentation as warnings instead of info
    public_member_api_docs: warning
    # Treat unnecessary underscores as warnings to be fixed
    unnecessary_underscores: warning
    # Treat unused imports as errors for cleaner code
    unused_import: error
    # Treat deprecated member usage as errors to prevent usage
    deprecated_member_use: error
    deprecated_member_use_from_same_package: error
    # Handle unused elements as warnings to be cleaned up
    unused_element: warning
    unused_field: warning
    unused_catch_clause: warning
    # Handle dead code as warnings
    dead_null_aware_expression: warning

linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at https://dart.dev/lints.
  rules:
    # Code style and formatting
    prefer_single_quotes: true
    prefer_const_constructors: true
    prefer_const_literals_to_create_immutables: true
    prefer_const_declarations: true
    prefer_final_locals: true
    prefer_final_fields: true

    # Import organization and cleanliness
    directives_ordering: true
    sort_pub_dependencies: true
    avoid_relative_lib_imports: true

    # Performance and best practices
    avoid_print: false  # Allow print in development, will be excluded for scripts/tests
    use_build_context_synchronously: true
    avoid_unnecessary_containers: true
    sized_box_for_whitespace: true
    use_key_in_widget_constructors: true
    prefer_is_empty: true
    prefer_is_not_empty: true

    # Widget and UI best practices
    use_colored_box: true
    use_decorated_box: true
    avoid_web_libraries_in_flutter: true

    # Enhanced const optimization rules
    prefer_const_constructors_in_immutables: true
    unnecessary_const: true
    unnecessary_new: true
    prefer_collection_literals: true

    # Memory and resource management
    avoid_function_literals_in_foreach_calls: true
    prefer_foreach: true

    # Error prevention and safety
    avoid_returning_null_for_void: true
    avoid_slow_async_io: false  # Allow for development scripts
    cancel_subscriptions: true
    close_sinks: true
    avoid_catches_without_on_clauses: true
    avoid_catching_errors: true

    # Type safety and null safety
    prefer_typing_uninitialized_variables: true
    avoid_types_on_closure_parameters: true
    omit_local_variable_types: true

    # Enhanced type safety rules for strict type annotations
    # Note: always_specify_types conflicts with avoid_types_on_closure_parameters
    # Using more targeted type safety rules instead
    avoid_dynamic_calls: true  # Avoid calls on dynamic types
    prefer_generic_function_type_aliases: true  # Use typedef for function types

    # Documentation - Re-enabled with exclusions for generated files
    public_member_api_docs: true  # Re-enabled for better code documentation

    # Import and dependency rules
    depend_on_referenced_packages: true

    # Code organization and readability
    prefer_interpolation_to_compose_strings: true
    prefer_adjacent_string_concatenation: true
    prefer_if_null_operators: true
    prefer_null_aware_operators: true

    # Flutter specific best practices
    use_full_hex_values_for_flutter_colors: true
    prefer_relative_imports: true

    # Performance optimizations
    # avoid_unnecessary_setstate: true  # Not a recognized lint rule

    # Security and best practices
    secure_pubspec_urls: true

    # Additional strict rules for enterprise-grade code quality (selective)
    # Focus on the most important rules that improve code quality without being overly verbose

    # Critical safety and correctness rules
    annotate_overrides: true
    avoid_empty_else: true
    avoid_equals_and_hash_code_on_mutable_classes: true
    avoid_init_to_null: true
    avoid_null_checks_in_equality_operators: true
    avoid_returning_null_for_future: true
    avoid_shadowing_type_parameters: true
    avoid_types_as_parameter_names: true
    avoid_unused_constructor_parameters: true
    cast_nullable_to_non_nullable: true
    exhaustive_cases: true
    hash_and_equals: true
    implementation_imports: true
    iterable_contains_unrelated_type: true
    no_duplicate_case_values: true
    no_logic_in_create_state: true
    only_throw_errors: true
    overridden_fields: true
    parameter_assignments: true
    recursive_getters: true
    test_types_in_equals: true
    throw_in_finally: true
    type_annotate_public_apis: true
    unawaited_futures: true
    unrelated_type_equality_checks: true
    void_checks: true

    # Additional type safety rules for strict type annotations
    # Note: always_declare_return_types is not a recognized lint rule
    # Using type_annotate_public_apis instead which is already enabled above
    avoid_returning_this: true  # Avoid returning 'this' for better type safety
    prefer_void_to_null: true  # Use void instead of returning null

    # Performance and best practices (moderate)
    prefer_for_elements_to_map_fromIterable: true
    prefer_spread_collections: true
    unnecessary_await_in_return: true
    unnecessary_getters_setters: true
    unnecessary_null_aware_assignments: true
    unnecessary_null_in_if_null_operators: true
    unnecessary_overrides: true
    unnecessary_statements: true
    unnecessary_string_interpolations: true
    unnecessary_this: true

    # Code organization (selective)
    file_names: true
    library_names: true
    package_names: true

    # Disabled overly strict rules that create noise:
    # - prefer_final_parameters: Creates too many warnings
    # - prefer_expression_function_bodies: Not always more readable
    # - sort_constructors_first: Can conflict with logical organization
    # - always_put_control_body_on_new_line: Can make simple code verbose
    # - prefer_int_literals: Double literals are often intentional
    # - avoid_redundant_argument_values: Sometimes explicit is better
    # - require_trailing_commas: Formatting tool handles this

    # Temporarily disable this rule due to Freezed mixin issues
    # TODO: Re-enable once Freezed generation issues are resolved
    # non_abstract_class_inherits_abstract_member: false

    # Riverpod specific (handled by custom_lint)
    # These will be enforced by riverpod_lint plugin

# Custom lint configuration for Riverpod
custom_lint:
  rules:
    # Enable all riverpod_lint rules
    - riverpod_lint

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
