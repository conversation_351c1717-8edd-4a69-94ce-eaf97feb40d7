import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../core/logging/global_talker_provider.dart';
import '../../../../core/logging/talker_extensions.dart';

import '../../../../core/utils/memory_leak_prevention_utility.dart';
import '../../domain/entities/debug_tool.dart';
import '../../domain/usecases/manage_debug_tools_usecase.dart';
import 'debug_providers.dart';

part 'debug_tools_provider.g.dart';

/// Provider for managing debug tools with modern AsyncNotifier pattern
///
/// This provider handles the management of debug tools including registration,
/// configuration, status updates, and usage tracking. It provides reactive
/// state management with automatic loading states and error handling.
@riverpod
class DebugToolsNotifier extends _$DebugToolsNotifier {
  // Context7 MCP: Resource management for memory leak prevention
  final List<Disposable> _managedResources = [];
  late final String _providerIdentifier;

  @override
  Future<List<DebugTool>> build() async {
    // Context7 MCP: Initialize resource tracking
    _providerIdentifier = 'DebugToolsNotifier_$hashCode';

    // Context7 MCP: Register provider disposal
    ref.onDispose(() {
      _disposeResources();
    });

    // Context7 MCP: Use structured logging with Talker
    final talker = ref.read(appTalkerProvider);
    talker.logDebugSession(
      'Initializing debug tools provider',
      operation: 'provider_build',
      metadata: {'provider': 'DebugToolsNotifier'},
    );

    try {
      final useCase = ref.read(manageDebugToolsUseCaseProvider);
      final result = await useCase(const ManageDebugToolsParams(action: DebugToolAction.getAll));

      return result.fold(
        (failure) {
          final message = failure.toString();
          final talker = ref.read(appTalkerProvider);

          // Context7 MCP: Structured error logging with Talker
          talker.logDebugTool(
            'Failed to load debug tools',
            action: 'getAll',
            operation: 'provider_build',
            metadata: {
              'failure_message': message,
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          throw Exception(message);
        },
        (tools) {
          final talker = ref.read(appTalkerProvider);

          // Context7 MCP: Structured success logging with Talker
          talker.logDebugTool(
            'Successfully loaded debug tools',
            action: 'getAll',
            operation: 'provider_build',
            metadata: {
              'tools_count': tools.length,
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          return tools;
        },
      );
    } on Exception catch (e, stackTrace) {
      final talker = ref.read(appTalkerProvider);

      // Context7 MCP: Sentry error capture with structured context
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('operation', 'debug_tools_provider_build');
          scope.setTag('provider', 'DebugToolsNotifier');
          scope.contexts['debug_tools'] = {
            'action': 'getAll',
            'provider_type': 'AsyncNotifier',
            'timestamp': DateTime.now().toIso8601String(),
          };
        },
      );

      // Context7 MCP: Structured error logging with Talker
      talker.logStructuredError(
        'Error in debug tools provider build',
        error: e,
        operation: 'provider_build',
        metadata: {
          'provider': 'DebugToolsNotifier',
          'action': 'getAll',
          'stack_trace': stackTrace.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      rethrow;
    }
  }

  /// Registers a new debug tool
  Future<void> registerTool(DebugTool tool) async {
    final talker = ref.read(appTalkerProvider);

    // Context7 MCP: Structured operation start logging
    talker.logDebugTool(
      'Starting debug tool registration',
      toolId: tool.id,
      action: 'register',
      operation: 'registerTool',
      metadata: {
        'tool_name': tool.name,
        'tool_type': tool.type.toString(),
        'provider': 'DebugToolsNotifier',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    try {
      final useCase = ref.read(manageDebugToolsUseCaseProvider);
      final result = await useCase(ManageDebugToolsParams(action: DebugToolAction.register, tool: tool));

      result.fold(
        (failure) {
          final message = failure.toString();

          // Context7 MCP: Structured failure logging with Talker
          talker.logDebugTool(
            'Failed to register debug tool',
            toolId: tool.id,
            action: 'register',
            operation: 'registerTool',
            metadata: {
              'failure_message': message,
              'tool_name': tool.name,
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          throw Exception(message);
        },
        (tools) {
          // Context7 MCP: Structured success logging with Talker
          talker.logDebugTool(
            'Successfully registered debug tool',
            toolId: tool.id,
            action: 'register',
            operation: 'registerTool',
            metadata: {
              'tool_name': tool.name,
              'tool_type': tool.type.toString(),
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          ref.invalidateSelf();
        },
      );
    } catch (e, stackTrace) {
      // Context7 MCP: Sentry error capture with structured context
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('operation', 'debug_tool_register');
          scope.setTag('provider', 'DebugToolsNotifier');
          scope.setTag('tool_id', tool.id);
          scope.contexts['debug_tool_register'] = {
            'tool_id': tool.id,
            'tool_name': tool.name,
            'tool_type': tool.type.toString(),
            'action': 'register',
            'timestamp': DateTime.now().toIso8601String(),
          };
        },
      );

      // Context7 MCP: Structured error logging with Talker
      talker.logStructuredError(
        'Error registering debug tool',
        error: e,
        operation: 'registerTool',
        metadata: {
          'tool_id': tool.id,
          'tool_name': tool.name,
          'provider': 'DebugToolsNotifier',
          'action': 'register',
          'stack_trace': stackTrace.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      rethrow;
    }
  }

  /// Updates an existing debug tool
  Future<void> updateTool(String toolId, DebugTool tool) async {
    final talker = ref.read(appTalkerProvider);

    // Context7 MCP: Structured operation start logging
    talker.logDebugTool(
      'Starting debug tool update',
      toolId: toolId,
      action: 'update',
      operation: 'updateTool',
      metadata: {
        'tool_name': tool.name,
        'tool_type': tool.type.toString(),
        'provider': 'DebugToolsNotifier',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    try {
      final useCase = ref.read(manageDebugToolsUseCaseProvider);
      final result = await useCase(ManageDebugToolsParams(action: DebugToolAction.update, toolId: toolId, tool: tool));

      result.fold(
        (failure) {
          final message = failure.toString();

          // Context7 MCP: Structured failure logging with Talker
          talker.logDebugTool(
            'Failed to update debug tool',
            toolId: toolId,
            action: 'update',
            operation: 'updateTool',
            metadata: {
              'failure_message': message,
              'tool_name': tool.name,
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          throw Exception(message);
        },
        (tools) {
          // Context7 MCP: Structured success logging with Talker
          talker.logDebugTool(
            'Successfully updated debug tool',
            toolId: toolId,
            action: 'update',
            operation: 'updateTool',
            metadata: {
              'tool_name': tool.name,
              'tool_type': tool.type.toString(),
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          ref.invalidateSelf();
        },
      );
    } catch (e, stackTrace) {
      // Context7 MCP: Sentry error capture with structured context
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('operation', 'debug_tool_update');
          scope.setTag('provider', 'DebugToolsNotifier');
          scope.setTag('tool_id', toolId);
          scope.contexts['debug_tool_update'] = {
            'tool_id': toolId,
            'tool_name': tool.name,
            'tool_type': tool.type.toString(),
            'action': 'update',
            'timestamp': DateTime.now().toIso8601String(),
          };
        },
      );

      // Context7 MCP: Structured error logging with Talker
      talker.logStructuredError(
        'Error updating debug tool',
        error: e,
        operation: 'updateTool',
        metadata: {
          'tool_id': toolId,
          'tool_name': tool.name,
          'provider': 'DebugToolsNotifier',
          'action': 'update',
          'stack_trace': stackTrace.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      rethrow;
    }
  }

  /// Enables a debug tool
  Future<void> enableTool(String toolId) async {
    final talker = ref.read(appTalkerProvider);

    // Context7 MCP: Structured operation start logging
    talker.logDebugTool(
      'Starting debug tool enable',
      toolId: toolId,
      action: 'enable',
      operation: 'enableTool',
      metadata: {'provider': 'DebugToolsNotifier', 'timestamp': DateTime.now().toIso8601String()},
    );

    try {
      final useCase = ref.read(manageDebugToolsUseCaseProvider);
      final result = await useCase(ManageDebugToolsParams(action: DebugToolAction.enable, toolId: toolId));

      result.fold(
        (failure) {
          final message = failure.toString();

          // Context7 MCP: Structured failure logging with Talker
          talker.logDebugTool(
            'Failed to enable debug tool',
            toolId: toolId,
            action: 'enable',
            operation: 'enableTool',
            metadata: {
              'failure_message': message,
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          throw Exception(message);
        },
        (tools) {
          // Context7 MCP: Structured success logging with Talker
          talker.logDebugTool(
            'Successfully enabled debug tool',
            toolId: toolId,
            action: 'enable',
            operation: 'enableTool',
            metadata: {'provider': 'DebugToolsNotifier', 'timestamp': DateTime.now().toIso8601String()},
          );

          ref.invalidateSelf();
        },
      );
    } catch (e, stackTrace) {
      // Context7 MCP: Sentry error capture with structured context
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('operation', 'debug_tool_enable');
          scope.setTag('provider', 'DebugToolsNotifier');
          scope.setTag('tool_id', toolId);
          scope.contexts['debug_tool_enable'] = {
            'tool_id': toolId,
            'action': 'enable',
            'timestamp': DateTime.now().toIso8601String(),
          };
        },
      );

      // Context7 MCP: Structured error logging with Talker
      talker.logStructuredError(
        'Error enabling debug tool',
        error: e,
        operation: 'enableTool',
        metadata: {
          'tool_id': toolId,
          'provider': 'DebugToolsNotifier',
          'action': 'enable',
          'stack_trace': stackTrace.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      rethrow;
    }
  }

  /// Disables a debug tool
  Future<void> disableTool(String toolId) async {
    final talker = ref.read(appTalkerProvider);

    // Context7 MCP: Structured operation start logging
    talker.logDebugTool(
      'Starting debug tool disable',
      toolId: toolId,
      action: 'disable',
      operation: 'disableTool',
      metadata: {'provider': 'DebugToolsNotifier', 'timestamp': DateTime.now().toIso8601String()},
    );

    try {
      final useCase = ref.read(manageDebugToolsUseCaseProvider);
      final result = await useCase(ManageDebugToolsParams(action: DebugToolAction.disable, toolId: toolId));

      result.fold(
        (failure) {
          final message = failure.toString();

          // Context7 MCP: Structured failure logging with Talker
          talker.logDebugTool(
            'Failed to disable debug tool',
            toolId: toolId,
            action: 'disable',
            operation: 'disableTool',
            metadata: {
              'failure_message': message,
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          throw Exception(message);
        },
        (tools) {
          // Context7 MCP: Structured success logging with Talker
          talker.logDebugTool(
            'Successfully disabled debug tool',
            toolId: toolId,
            action: 'disable',
            operation: 'disableTool',
            metadata: {'provider': 'DebugToolsNotifier', 'timestamp': DateTime.now().toIso8601String()},
          );

          ref.invalidateSelf();
        },
      );
    } catch (e, stackTrace) {
      // Context7 MCP: Sentry error capture with structured context
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('operation', 'debug_tool_disable');
          scope.setTag('provider', 'DebugToolsNotifier');
          scope.setTag('tool_id', toolId);
          scope.contexts['debug_tool_disable'] = {
            'tool_id': toolId,
            'action': 'disable',
            'timestamp': DateTime.now().toIso8601String(),
          };
        },
      );

      // Context7 MCP: Structured error logging with Talker
      talker.logStructuredError(
        'Error disabling debug tool',
        error: e,
        operation: 'disableTool',
        metadata: {
          'tool_id': toolId,
          'provider': 'DebugToolsNotifier',
          'action': 'disable',
          'stack_trace': stackTrace.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      rethrow;
    }
  }

  /// Updates tool usage statistics
  Future<void> updateToolUsage({
    required String toolId,
    bool? wasSuccessful,
    int? dataEntriesCount,
    int? reportsCount,
  }) async {
    final talker = ref.read(appTalkerProvider);

    // Context7 MCP: Structured operation start logging
    talker.logDebugTool(
      'Starting debug tool usage update',
      toolId: toolId,
      action: 'updateUsage',
      operation: 'updateToolUsage',
      metadata: {
        'was_successful': wasSuccessful,
        'data_entries_count': dataEntriesCount,
        'reports_count': reportsCount,
        'provider': 'DebugToolsNotifier',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    try {
      final useCase = ref.read(manageDebugToolsUseCaseProvider);
      final result = await useCase(
        ManageDebugToolsParams(
          action: DebugToolAction.updateUsage,
          toolId: toolId,
          wasSuccessful: wasSuccessful,
          dataEntriesCount: dataEntriesCount,
          reportsCount: reportsCount,
        ),
      );

      result.fold(
        (failure) {
          final message = failure.toString();

          // Context7 MCP: Structured failure logging with Talker
          talker.logDebugTool(
            'Failed to update tool usage',
            toolId: toolId,
            action: 'updateUsage',
            operation: 'updateToolUsage',
            metadata: {
              'failure_message': message,
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          throw Exception(message);
        },
        (tools) {
          // Context7 MCP: Structured success logging with Talker
          talker.logDebugTool(
            'Successfully updated tool usage',
            toolId: toolId,
            action: 'updateUsage',
            operation: 'updateToolUsage',
            metadata: {
              'was_successful': wasSuccessful,
              'data_entries_count': dataEntriesCount,
              'reports_count': reportsCount,
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          ref.invalidateSelf();
        },
      );
    } catch (e, stackTrace) {
      // Context7 MCP: Sentry error capture with structured context
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('operation', 'debug_tool_update_usage');
          scope.setTag('provider', 'DebugToolsNotifier');
          scope.setTag('tool_id', toolId);
          scope.contexts['debug_tool_update_usage'] = {
            'tool_id': toolId,
            'action': 'updateUsage',
            'was_successful': wasSuccessful,
            'data_entries_count': dataEntriesCount,
            'reports_count': reportsCount,
            'timestamp': DateTime.now().toIso8601String(),
          };
        },
      );

      // Context7 MCP: Structured error logging with Talker
      talker.logStructuredError(
        'Error updating tool usage',
        error: e,
        operation: 'updateToolUsage',
        metadata: {
          'tool_id': toolId,
          'provider': 'DebugToolsNotifier',
          'action': 'updateUsage',
          'stack_trace': stackTrace.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      rethrow;
    }
  }

  /// Resets tool usage statistics
  Future<void> resetToolUsage(String toolId) async {
    final talker = ref.read(appTalkerProvider);

    // Context7 MCP: Structured operation start logging
    talker.logDebugTool(
      'Starting debug tool usage reset',
      toolId: toolId,
      action: 'resetUsage',
      operation: 'resetToolUsage',
      metadata: {'provider': 'DebugToolsNotifier', 'timestamp': DateTime.now().toIso8601String()},
    );

    try {
      final useCase = ref.read(manageDebugToolsUseCaseProvider);
      final result = await useCase(ManageDebugToolsParams(action: DebugToolAction.resetUsage, toolId: toolId));

      result.fold(
        (failure) {
          final message = failure.toString();

          // Context7 MCP: Structured failure logging with Talker
          talker.logDebugTool(
            'Failed to reset tool usage',
            toolId: toolId,
            action: 'resetUsage',
            operation: 'resetToolUsage',
            metadata: {
              'failure_message': message,
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          throw Exception(message);
        },
        (tools) {
          // Context7 MCP: Structured success logging with Talker
          talker.logDebugTool(
            'Successfully reset tool usage',
            toolId: toolId,
            action: 'resetUsage',
            operation: 'resetToolUsage',
            metadata: {'provider': 'DebugToolsNotifier', 'timestamp': DateTime.now().toIso8601String()},
          );

          ref.invalidateSelf();
        },
      );
    } catch (e, stackTrace) {
      // Context7 MCP: Sentry error capture with structured context
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('operation', 'debug_tool_reset_usage');
          scope.setTag('provider', 'DebugToolsNotifier');
          scope.setTag('tool_id', toolId);
          scope.contexts['debug_tool_reset_usage'] = {
            'tool_id': toolId,
            'action': 'resetUsage',
            'timestamp': DateTime.now().toIso8601String(),
          };
        },
      );

      // Context7 MCP: Structured error logging with Talker
      talker.logStructuredError(
        'Error resetting tool usage',
        error: e,
        operation: 'resetToolUsage',
        metadata: {
          'tool_id': toolId,
          'provider': 'DebugToolsNotifier',
          'action': 'resetUsage',
          'stack_trace': stackTrace.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      rethrow;
    }
  }

  /// Removes a debug tool
  Future<void> removeTool(String toolId) async {
    final talker = ref.read(appTalkerProvider);

    // Context7 MCP: Structured operation start logging
    talker.logDebugTool(
      'Starting debug tool removal',
      toolId: toolId,
      action: 'remove',
      operation: 'removeTool',
      metadata: {'provider': 'DebugToolsNotifier', 'timestamp': DateTime.now().toIso8601String()},
    );

    try {
      final useCase = ref.read(manageDebugToolsUseCaseProvider);
      final result = await useCase(ManageDebugToolsParams(action: DebugToolAction.remove, toolId: toolId));

      result.fold(
        (failure) {
          final message = failure.toString();

          // Context7 MCP: Structured failure logging with Talker
          talker.logDebugTool(
            'Failed to remove debug tool',
            toolId: toolId,
            action: 'remove',
            operation: 'removeTool',
            metadata: {
              'failure_message': message,
              'provider': 'DebugToolsNotifier',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );

          throw Exception(message);
        },
        (tools) {
          // Context7 MCP: Structured success logging with Talker
          talker.logDebugTool(
            'Successfully removed debug tool',
            toolId: toolId,
            action: 'remove',
            operation: 'removeTool',
            metadata: {'provider': 'DebugToolsNotifier', 'timestamp': DateTime.now().toIso8601String()},
          );

          ref.invalidateSelf();
        },
      );
    } catch (e, stackTrace) {
      // Context7 MCP: Sentry error capture with structured context
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('operation', 'debug_tool_remove');
          scope.setTag('provider', 'DebugToolsNotifier');
          scope.setTag('tool_id', toolId);
          scope.contexts['debug_tool_remove'] = {
            'tool_id': toolId,
            'action': 'remove',
            'timestamp': DateTime.now().toIso8601String(),
          };
        },
      );

      // Context7 MCP: Structured error logging with Talker
      talker.logStructuredError(
        'Error removing debug tool',
        error: e,
        operation: 'removeTool',
        metadata: {
          'tool_id': toolId,
          'provider': 'DebugToolsNotifier',
          'action': 'remove',
          'stack_trace': stackTrace.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      rethrow;
    }
  }

  /// Gets enabled debug tools
  List<DebugTool> getEnabledTools() {
    return state.when(
      data: (tools) => tools.where((tool) => tool.isEnabled).toList(),
      loading: () => [],
      error: (error, stackTrace) => [],
    );
  }

  /// Gets debug tools by type
  List<DebugTool> getToolsByType(DebugToolType type) {
    return state.when(
      data: (tools) => tools.where((tool) => tool.type == type).toList(),
      loading: () => [],
      error: (error, stackTrace) => [],
    );
  }

  /// Gets debug tool by ID
  DebugTool? getToolById(String toolId) {
    return state.when(
      data: (tools) {
        final matchingTools = tools.where((tool) => tool.id == toolId);
        return matchingTools.isNotEmpty ? matchingTools.first : null;
      },
      loading: () => null,
      error: (error, stackTrace) => null,
    );
  }

  /// Gets available debug tools (enabled and available on platform)
  List<DebugTool> getAvailableTools() {
    return state.when(
      data: (tools) => tools.where((tool) => tool.isEnabled && tool.isAvailable).toList(),
      loading: () => [],
      error: (error, stackTrace) => [],
    );
  }

  /// Context7 MCP: Dispose all managed resources
  void _disposeResources() {
    final talker = ref.read(appTalkerProvider);

    talker.logResourceManagement(
      'Disposing debug tools provider resources',
      resourceType: 'Provider',
      action: 'dispose',
      resourceId: _providerIdentifier,
      metadata: {'managed_resources_count': _managedResources.length, 'provider': 'DebugToolsNotifier'},
    );

    // Dispose all managed resources
    for (final resource in _managedResources) {
      try {
        resource.dispose();
      } on Exception catch (e) {
        talker.logStructuredError(
          'Error disposing resource in debug tools provider',
          error: e,
          operation: 'dispose_resource',
          metadata: {'resource_type': resource.runtimeType.toString(), 'provider': 'DebugToolsNotifier'},
        );
      }
    }

    _managedResources.clear();
    // Note: MemoryLeakPreventionUtility will automatically clean up when resources are disposed
  }
}

/// Provider for getting enabled debug tools
@riverpod
List<DebugTool> enabledDebugTools(Ref ref) {
  final toolsAsync = ref.watch(debugToolsNotifierProvider);
  return toolsAsync.when(
    data: (tools) => tools.where((tool) => tool.isEnabled).toList(),
    loading: () => [],
    error: (_, _) => [],
  );
}

/// Provider for getting debug tools by type
@riverpod
List<DebugTool> debugToolsByType(Ref ref, DebugToolType type) {
  final toolsAsync = ref.watch(debugToolsNotifierProvider);
  return toolsAsync.when(
    data: (tools) => tools.where((tool) => tool.type == type).toList(),
    loading: () => [],
    error: (_, _) => [],
  );
}

/// Provider for getting available debug tools
@riverpod
List<DebugTool> availableDebugTools(Ref ref) {
  final toolsAsync = ref.watch(debugToolsNotifierProvider);
  return toolsAsync.when(
    data: (tools) => tools.where((tool) => tool.isEnabled && tool.isAvailable).toList(),
    loading: () => [],
    error: (_, _) => [],
  );
}
