import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../models/sync_conflict.dart';
import '../models/sync_result.dart';

/// Conflict Resolution Dialog
///
/// A comprehensive dialog widget for handling data synchronization conflicts
/// following Context7 MCP best practices. This widget provides users with
/// clear information about conflicts and options for resolution.
///
/// Key Features:
/// - Clear conflict visualization with side-by-side comparison
/// - Multiple resolution options with explanations
/// - User-friendly interface with proper accessibility
/// - Context7 MCP Material Design compliance
/// - Internationalization support
///
/// Usage:
/// ```dart
/// final result = await showDialog<ConflictResolution>(
///   context: context,
///   builder: (context) => ConflictResolutionDialog(
///     conflict: syncConflict,
///   ),
/// );
/// ```
class ConflictResolutionDialog extends StatefulWidget {
  /// The conflict to resolve
  final SyncConflict conflict;

  /// Optional callback for custom resolution
  final Future<ConflictResolution> Function(ConflictResolutionStrategy)? onResolve;

  const ConflictResolutionDialog({super.key, required this.conflict, this.onResolve});

  @override
  State<ConflictResolutionDialog> createState() => _ConflictResolutionDialogState();
}

class _ConflictResolutionDialogState extends State<ConflictResolutionDialog> {
  ConflictResolutionStrategy? _selectedStrategy;
  bool _isResolving = false;

  @override
  void initState() {
    super.initState();
    _selectedStrategy = widget.conflict.strategy;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.warning_amber_rounded, color: theme.colorScheme.error, size: 28),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              l10n.conflictResolutionTitle,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Conflict description
              _buildConflictDescription(context, l10n, theme),

              const SizedBox(height: 24),

              // Data comparison
              _buildDataComparison(context, l10n, theme),

              const SizedBox(height: 24),

              // Resolution options
              _buildResolutionOptions(context, l10n, theme),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(onPressed: _isResolving ? null : () => Navigator.of(context).pop(), child: Text(l10n.cancel)),
        ElevatedButton(
          onPressed: _isResolving || _selectedStrategy == null ? null : _handleResolve,
          child: _isResolving
              ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
              : Text(l10n.resolve),
        ),
      ],
    );
  }

  Widget _buildConflictDescription(BuildContext context, AppLocalizations l10n, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.conflictDetails, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            _buildDetailRow(l10n.entityType, widget.conflict.entityType),
            _buildDetailRow(l10n.entityId, widget.conflict.entityId),
            _buildDetailRow(l10n.conflictType, _getConflictTypeText(l10n)),
            _buildDetailRow(l10n.detectedAt, _formatDateTime(widget.conflict.detectedAt)),
            if (widget.conflict.fieldConflicts?.isNotEmpty == true)
              _buildDetailRow(
                l10n.conflictingFields,
                widget.conflict.fieldConflicts!.map((f) => f.fieldName).join(', '),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text('$label:', style: const TextStyle(fontWeight: FontWeight.w500)),
          ),
          Expanded(
            child: Text(value, style: const TextStyle(fontFamily: 'monospace')),
          ),
        ],
      ),
    );
  }

  Widget _buildDataComparison(BuildContext context, AppLocalizations l10n, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.dataComparison, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(child: _buildDataColumn(l10n.localData, widget.conflict.localData, theme.colorScheme.primary)),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDataColumn(l10n.remoteData, widget.conflict.remoteData, theme.colorScheme.secondary),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataColumn(String title, Map<String, dynamic> data, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: (0.1 * 255).round().toDouble()),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color.withValues(alpha: (0.3 * 255).round().toDouble())),
          ),
          child: Text(
            title,
            style: TextStyle(fontWeight: FontWeight.bold, color: color),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: (0.05 * 255).round().toDouble()),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.withValues(alpha: (0.2 * 255).round().toDouble())),
          ),
          child: Text(_formatDataForDisplay(data), style: const TextStyle(fontFamily: 'monospace', fontSize: 12)),
        ),
      ],
    );
  }

  Widget _buildResolutionOptions(BuildContext context, AppLocalizations l10n, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.resolutionOptions, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            ...ConflictResolutionStrategy.values.map((strategy) {
              return _buildStrategyOption(strategy, l10n, theme);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildStrategyOption(ConflictResolutionStrategy strategy, AppLocalizations l10n, ThemeData theme) {
    final isSelected = _selectedStrategy == strategy;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected ? theme.colorScheme.primary : Colors.grey.withValues(alpha: (0.3 * 255).round().toDouble()),
          width: isSelected ? 2 : 1,
        ),
        color: isSelected ? theme.colorScheme.primary.withValues(alpha: (0.05 * 255).round().toDouble()) : null,
      ),
      child: RadioListTile<ConflictResolutionStrategy>(
        value: strategy,
        groupValue: _selectedStrategy,
        onChanged: (value) {
          setState(() {
            _selectedStrategy = value;
          });
        },
        title: Text(
          _getStrategyTitle(strategy, l10n),
          style: TextStyle(fontWeight: isSelected ? FontWeight.bold : FontWeight.normal),
        ),
        subtitle: Text(
          _getStrategyDescription(strategy, l10n),
          style: TextStyle(color: theme.textTheme.bodySmall?.color),
        ),
        dense: true,
      ),
    );
  }

  String _getConflictTypeText(AppLocalizations l10n) {
    switch (widget.conflict.conflictType) {
      case ConflictType.dataConflict:
        return l10n.dataConflict;
      case ConflictType.deletionConflict:
        return l10n.deletionConflict;
      case ConflictType.temporalConflict:
        return l10n.temporalConflict;
      case ConflictType.structuralConflict:
        return l10n.structuralConflict;
      case ConflictType.permissionConflict:
        return l10n.permissionConflict;
    }
  }

  String _getStrategyTitle(ConflictResolutionStrategy strategy, AppLocalizations l10n) {
    switch (strategy) {
      case ConflictResolutionStrategy.serverWins:
        return l10n.useRemoteData;
      case ConflictResolutionStrategy.clientWins:
        return l10n.useLocalData;
      case ConflictResolutionStrategy.latestTimestamp:
        return l10n.useLatestData;
      case ConflictResolutionStrategy.merge:
        return l10n.mergeData;
      case ConflictResolutionStrategy.manual:
        return l10n.manualResolution;
    }
  }

  String _getStrategyDescription(ConflictResolutionStrategy strategy, AppLocalizations l10n) {
    switch (strategy) {
      case ConflictResolutionStrategy.serverWins:
        return l10n.useRemoteDataDescription;
      case ConflictResolutionStrategy.clientWins:
        return l10n.useLocalDataDescription;
      case ConflictResolutionStrategy.latestTimestamp:
        return l10n.useLatestDataDescription;
      case ConflictResolutionStrategy.merge:
        return l10n.mergeDataDescription;
      case ConflictResolutionStrategy.manual:
        return l10n.manualResolutionDescription;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDataForDisplay(Map<String, dynamic> data) {
    final buffer = StringBuffer();
    for (final entry in data.entries) {
      buffer.writeln('${entry.key}: ${entry.value}');
    }
    return buffer.toString().trim();
  }

  Future<void> _handleResolve() async {
    if (_selectedStrategy == null) return;

    setState(() {
      _isResolving = true;
    });

    try {
      ConflictResolution resolution;

      if (widget.onResolve != null) {
        resolution = await widget.onResolve!(_selectedStrategy!);
      } else {
        // Create a basic resolution
        resolution = ConflictResolution(
          strategy: _selectedStrategy!,
          resolvedData: _selectedStrategy == ConflictResolutionStrategy.serverWins
              ? widget.conflict.remoteData
              : widget.conflict.localData,
          resolvedAt: DateTime.now(),
          confidence: 1.0,
        );
      }

      if (mounted) {
        Navigator.of(context).pop(resolution);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to resolve conflict: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isResolving = false;
        });
      }
    }
  }
}

/// Show conflict resolution dialog
Future<ConflictResolution?> showConflictResolutionDialog({
  required BuildContext context,
  required SyncConflict conflict,
  Future<ConflictResolution> Function(ConflictResolutionStrategy)? onResolve,
}) {
  return showDialog<ConflictResolution>(
    context: context,
    barrierDismissible: false,
    builder: (context) => ConflictResolutionDialog(conflict: conflict, onResolve: onResolve),
  );
}
