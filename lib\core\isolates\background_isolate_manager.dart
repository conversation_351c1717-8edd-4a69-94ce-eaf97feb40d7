import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../logging/app_talker_provider.dart';
import '../logging/talker_extensions.dart';

import '../utils/memory_leak_prevention_utility.dart';
import 'isolate_task_types.dart';

/// Context7 MCP: Background isolate manager for heavy operations
///
/// This manager provides a pool of isolates for executing CPU-intensive tasks
/// without blocking the main UI thread. It implements proper resource management,
/// error handling, and progress monitoring according to Context7 MCP best practices.
class BackgroundIsolateManager implements Disposable {
  static BackgroundIsolateManager? _instance;

  /// Gets the singleton instance of BackgroundIsolateManager.
  ///
  /// This provides a global access point to the background isolate management
  /// system following the singleton pattern. The instance is lazily initialized
  /// on first access.
  ///
  /// **Context7 MCP:** Singleton pattern ensures consistent isolate management
  /// across the application lifecycle.
  ///
  /// **Returns:** The singleton BackgroundIsolateManager instance.
  static BackgroundIsolateManager get instance => _instance ??= BackgroundIsolateManager._();

  BackgroundIsolateManager._();

  /// Context7 MCP: Reset singleton for testing purposes
  @visibleForTesting
  static void resetInstance() {
    _instance?.dispose();
    _instance = null;
  }

  /// Context7 MCP: Get manager ID safely
  String get managerId {
    if (!_isInitialized || _managerId == null) {
      throw StateError('BackgroundIsolateManager must be initialized before accessing managerId');
    }
    return _managerId!;
  }

  /// Context7 MCP: Check if manager is initialized
  bool get isInitialized => _isInitialized;

  // Context7 MCP: Resource management for isolate pool
  final Map<String, _IsolateWorker> _isolatePool = {};
  final Map<String, IsolateTask> _activeTasks = {};
  final Map<String, Completer<dynamic>> _taskCompleters = {};
  final List<Disposable> _managedResources = [];

  // Context7 MCP: Configuration
  static const int _maxIsolates = 4;
  static const Duration _isolateTimeout = Duration(minutes: 5);
  static const Duration _taskTimeout = Duration(minutes: 2);

  bool _isDisposed = false;
  bool _isInitialized = false;
  String? _managerId;

  /// Context7 MCP: Initialize the isolate manager
  Future<void> initialize() async {
    if (_isDisposed) {
      throw StateError('BackgroundIsolateManager has been disposed');
    }

    // Context7 MCP: Prevent multiple initialization
    if (_isInitialized) {
      return; // Already initialized, skip
    }

    _managerId = 'BackgroundIsolateManager_$hashCode';
    _isInitialized = true;

    // Context7 MCP: Register with memory leak prevention
    MemoryLeakPreventionUtility().registerResource(_managerId!, this);

    // Context7 MCP: Structured logging for initialization
    final talker = AppTalkerProvider.instance.talker;
    talker.logPerformance(
      'Initializing background isolate manager',
      operation: 'isolate_manager_init',
      metadata: {
        'max_isolates': _maxIsolates,
        'isolate_timeout_minutes': _isolateTimeout.inMinutes,
        'task_timeout_minutes': _taskTimeout.inMinutes,
        'manager_id': _managerId!,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Context7 MCP: Execute a task in a background isolate
  Future<T> executeTask<T>(IsolateTask<T> task) async {
    if (_isDisposed) {
      throw StateError('BackgroundIsolateManager has been disposed');
    }

    final taskId = 'task_${DateTime.now().millisecondsSinceEpoch}_${task.hashCode}';
    final talker = AppTalkerProvider.instance.talker;

    // Context7 MCP: Structured logging for task start
    talker.logPerformance(
      'Starting background task execution',
      operation: 'isolate_task_start',
      metadata: {
        'task_id': taskId,
        'task_type': task.runtimeType.toString(),
        'task_priority': task.priority.toString(),
        'manager_id': _managerId!,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    try {
      // Context7 MCP: Get or create isolate worker
      final worker = await _getAvailableWorker();

      // Context7 MCP: Register task for monitoring
      _activeTasks[taskId] = task;
      final completer = Completer<T>();
      _taskCompleters[taskId] = completer;

      // Context7 MCP: Execute task with timeout
      final timeoutTimer = Timer(_taskTimeout, () {
        if (!completer.isCompleted) {
          final error = TimeoutException('Task execution timeout', _taskTimeout);
          _handleTaskError(taskId, error, StackTrace.current);
        }
      });

      // Context7 MCP: Send task to isolate
      worker.sendPort.send({'taskId': taskId, 'task': task, 'timestamp': DateTime.now().toIso8601String()});

      // Context7 MCP: Wait for result with proper cleanup
      final result = await completer.future;
      timeoutTimer.cancel();

      // Context7 MCP: Cleanup task tracking
      _activeTasks.remove(taskId);
      _taskCompleters.remove(taskId);

      // Context7 MCP: Structured logging for task completion
      talker.logPerformance(
        'Background task completed successfully',
        operation: 'isolate_task_complete',
        metadata: {
          'task_id': taskId,
          'task_type': task.runtimeType.toString(),
          'execution_time_ms': DateTime.now().millisecondsSinceEpoch - int.parse(taskId.split('_')[1]),
          'manager_id': _managerId!,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return result;
    } catch (e, stackTrace) {
      // Context7 MCP: Comprehensive error handling
      await _handleTaskError(taskId, e, stackTrace);
      rethrow;
    }
  }

  /// Context7 MCP: Get available worker or create new one
  Future<_IsolateWorker> _getAvailableWorker() async {
    // Context7 MCP: Find available worker
    for (final worker in _isolatePool.values) {
      if (!worker.isBusy) {
        worker.isBusy = true;
        return worker;
      }
    }

    // Context7 MCP: Create new worker if under limit
    if (_isolatePool.length < _maxIsolates) {
      final workerId = 'worker_${_isolatePool.length}';
      final worker = await _createWorker(workerId);
      _isolatePool[workerId] = worker;
      worker.isBusy = true;
      return worker;
    }

    // Context7 MCP: Wait for available worker
    return _waitForAvailableWorker();
  }

  /// Context7 MCP: Create new isolate worker
  Future<_IsolateWorker> _createWorker(String workerId) async {
    final talker = AppTalkerProvider.instance.talker;

    talker.logPerformance(
      'Creating new isolate worker',
      operation: 'isolate_worker_create',
      metadata: {'worker_id': workerId, 'manager_id': _managerId!, 'timestamp': DateTime.now().toIso8601String()},
    );

    try {
      final receivePort = ReceivePort();
      final isolate = await Isolate.spawn(
        _isolateEntryPoint,
        receivePort.sendPort,
        debugName: 'BackgroundWorker_$workerId',
      );

      // Context7 MCP: Set up variables for worker communication
      late SendPort sendPort;
      var sendPortReceived = false;

      // Context7 MCP: Listen for worker messages and extract send port
      receivePort.listen((message) {
        if (!sendPortReceived && message is SendPort) {
          sendPort = message;
          sendPortReceived = true;
        } else {
          _handleWorkerMessage(workerId, message);
        }
      });

      // Context7 MCP: Wait for send port
      while (!sendPortReceived) {
        await Future.delayed(const Duration(milliseconds: 10));
      }

      final worker = _IsolateWorker(id: workerId, isolate: isolate, sendPort: sendPort, receivePort: receivePort);

      // Context7 MCP: Register worker for disposal
      _managedResources.add(worker);

      return worker;
    } on Exception catch (e, stackTrace) {
      // Context7 MCP: Sentry error capture for worker creation failure
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('operation', 'isolate_worker_creation');
          scope.setTag('worker_id', workerId);
          scope.contexts['isolate_manager'] = {
            'manager_id': _managerId!,
            'worker_id': workerId,
            'pool_size': _isolatePool.length,
            'timestamp': DateTime.now().toIso8601String(),
          };
        },
      );

      talker.logStructuredError(
        'Failed to create isolate worker',
        error: e,
        operation: 'isolate_worker_create',
        metadata: {
          'worker_id': workerId,
          'manager_id': _managerId!,
          'stack_trace': stackTrace.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      rethrow;
    }
  }

  /// Context7 MCP: Wait for available worker
  Future<_IsolateWorker> _waitForAvailableWorker() async {
    final completer = Completer<_IsolateWorker>();

    // Context7 MCP: Poll for available worker
    Timer.periodic(const Duration(milliseconds: 100), (timer) {
      for (final worker in _isolatePool.values) {
        if (!worker.isBusy) {
          worker.isBusy = true;
          timer.cancel();
          completer.complete(worker);
          return;
        }
      }
    });

    return completer.future;
  }

  /// Context7 MCP: Handle messages from worker isolates
  void _handleWorkerMessage(String workerId, dynamic message) {
    if (message is Map<String, dynamic>) {
      final taskId = message['taskId'] as String?;
      final result = message['result'];
      final error = message['error'];

      if (taskId != null && _taskCompleters.containsKey(taskId)) {
        final completer = _taskCompleters[taskId]!;

        if (error != null) {
          completer.completeError(Exception(error));
        } else {
          completer.complete(result);
        }

        // Context7 MCP: Mark worker as available
        final worker = _isolatePool[workerId];
        if (worker != null) {
          worker.isBusy = false;
        }
      }
    }
  }

  /// Context7 MCP: Handle task execution errors
  Future<void> _handleTaskError(String taskId, Object error, StackTrace stackTrace) async {
    final talker = AppTalkerProvider.instance.talker;

    // Context7 MCP: Sentry error capture
    await Sentry.captureException(
      error,
      stackTrace: stackTrace,
      withScope: (scope) {
        scope.setTag('operation', 'isolate_task_execution');
        scope.setTag('task_id', taskId);
        scope.contexts['isolate_task_error'] = {
          'task_id': taskId,
          'manager_id': _managerId!,
          'active_tasks_count': _activeTasks.length,
          'isolate_pool_size': _isolatePool.length,
          'timestamp': DateTime.now().toIso8601String(),
        };
      },
    );

    // Context7 MCP: Structured error logging
    talker.logStructuredError(
      'Background task execution failed',
      error: error,
      operation: 'isolate_task_execution',
      metadata: {
        'task_id': taskId,
        'manager_id': _managerId!,
        'stack_trace': stackTrace.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    // Context7 MCP: Cleanup failed task
    _activeTasks.remove(taskId);
    final completer = _taskCompleters.remove(taskId);
    if (completer != null && !completer.isCompleted) {
      completer.completeError(error, stackTrace);
    }
  }

  /// Context7 MCP: Get manager status for monitoring
  Map<String, dynamic> getStatus() {
    return {
      'manager_id': _managerId!,
      'is_disposed': _isDisposed,
      'isolate_pool_size': _isolatePool.length,
      'active_tasks_count': _activeTasks.length,
      'max_isolates': _maxIsolates,
      'available_workers': _isolatePool.values.where((w) => !w.isBusy).length,
      'busy_workers': _isolatePool.values.where((w) => w.isBusy).length,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Context7 MCP: Dispose all resources
  @override
  void dispose() {
    if (_isDisposed) return;

    final talker = AppTalkerProvider.instance.talker;
    talker.logResourceManagement(
      'Disposing background isolate manager',
      resourceType: 'IsolateManager',
      action: 'dispose',
      resourceId: _managerId!,
      metadata: {
        'isolate_pool_size': _isolatePool.length,
        'active_tasks_count': _activeTasks.length,
        'managed_resources_count': _managedResources.length,
      },
    );

    // Context7 MCP: Cancel all active tasks
    for (final completer in _taskCompleters.values) {
      if (!completer.isCompleted) {
        completer.completeError(StateError('IsolateManager disposed'));
      }
    }
    _taskCompleters.clear();
    _activeTasks.clear();

    // Context7 MCP: Dispose all workers
    for (final worker in _isolatePool.values) {
      worker.dispose();
    }
    _isolatePool.clear();

    // Context7 MCP: Dispose managed resources
    for (final resource in _managedResources) {
      try {
        resource.dispose();
      } on Exception catch (e) {
        talker.logStructuredError(
          'Error disposing managed resource',
          error: e,
          operation: 'resource_disposal',
          metadata: {'resource_type': resource.runtimeType.toString()},
        );
      }
    }
    _managedResources.clear();

    // Context7 MCP: Reset initialization state
    _isInitialized = false;
    _managerId = null;
    _isDisposed = true;
  }

  /// Context7 MCP: Isolate entry point for worker isolates
  static void _isolateEntryPoint(SendPort mainSendPort) {
    final receivePort = ReceivePort();
    mainSendPort.send(receivePort.sendPort);

    receivePort.listen((message) async {
      if (message is Map<String, dynamic>) {
        final taskId = message['taskId'] as String;
        final task = message['task'] as IsolateTask;

        try {
          // Context7 MCP: Execute task in isolate
          final result = await task.execute();

          // Context7 MCP: Send result back to main isolate
          mainSendPort.send({'taskId': taskId, 'result': result, 'timestamp': DateTime.now().toIso8601String()});
        } on Exception catch (e, stackTrace) {
          // Context7 MCP: Send error back to main isolate
          mainSendPort.send({
            'taskId': taskId,
            'error': e.toString(),
            'stackTrace': stackTrace.toString(),
            'timestamp': DateTime.now().toIso8601String(),
          });
        }
      }
    });
  }
}

/// Context7 MCP: Internal isolate worker representation
class _IsolateWorker implements Disposable {
  final String id;
  final Isolate isolate;
  final SendPort sendPort;
  final ReceivePort receivePort;
  bool isBusy = false;

  _IsolateWorker({required this.id, required this.isolate, required this.sendPort, required this.receivePort});

  @override
  void dispose() {
    receivePort.close();
    isolate.kill(priority: Isolate.immediate);
  }
}
