import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../../core/config/clustering_config.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/controllers/location_centering_controller.dart';
import '../../../../core/controllers/optimized_camera_controller.dart';
import '../../../../core/interfaces/unified_map_service_interface.dart';
import '../../../../core/interfaces/unified_marker_service_interface.dart';
import '../../../../core/models/location_data.dart';
import '../../../../core/models/performance_metrics.dart';
import '../../../../core/providers/app_settings_selectors.dart';
import '../../../../core/providers/enhanced_map_retry_system.dart';
import '../../../../core/providers/first_launch_location_provider.dart';
import '../../../../core/providers/master_location_provider.dart';
import '../../../../core/providers/unified_service_providers.dart';
import '../../../../core/services/unified_clustering_service.dart';
import '../../../../core/utils/result.dart';

import '../../../../core/widgets/resilient_map_widget.dart';

import '../../../maps/presentation/widgets/map_masjid_card.dart';
import '../../../masjids/data/models/masjid_model.dart';
import '../../../masjids/presentation/providers/unified_masjids_filter_manager.dart';

import '../../../tutorial/domain/models/tutorial_target.dart';
import '../../../tutorial/domain/providers/tutorial_keys_provider.dart';
// MapStateMixin removed - functionality implemented directly
import '../providers/home_search_provider.dart';
import 'current_location_button.dart';
import 'map_ui_components.dart';

import 'nearby_masjids_button.dart';

/// Optimized Google Maps widget for the home page
/// Uses lightweight marker data for better performance while maintaining all original features
class OptimizedMapWidget extends ConsumerStatefulWidget {
  /// Optional callback to report masjid counts
  final Function(int active, int inactive)? onMasjidCountsUpdated;

  /// ValueNotifier for masjid counts to display in overlay
  final ValueNotifier<Map<String, int>>? masjidCounts;

  /// Callback when a masjid is tapped on the map
  final Function(MasjidModel)? onMasjidTapped;

  /// The currently selected masjid to show in overlay
  final MasjidModel? selectedMasjid;

  /// Whether the selected masjid is the nearest masjid
  final bool isNearestMasjid;

  /// Callback when the overlay close button is pressed
  final VoidCallback? onOverlayClose;

  /// Constructor with const for performance optimization
  const OptimizedMapWidget({
    super.key,
    this.onMasjidCountsUpdated,
    this.masjidCounts,
    this.onMasjidTapped,
    this.selectedMasjid,
    this.isNearestMasjid = false,
    this.onOverlayClose,
  });

  @override
  ConsumerState<OptimizedMapWidget> createState() => _OptimizedMapWidgetState();
}

class _OptimizedMapWidgetState extends ConsumerState<OptimizedMapWidget> {
  final Set<Marker> _markers = {};
  Set<Marker> _clusteredMarkers = {};

  /// Primary clustering service - using unified clustering for best performance
  final UnifiedClusteringService _unifiedClustering = UnifiedClusteringService();

  /// Unified map service (replaces MapMemoryOptimizationService and MapOfflineManager)
  UnifiedMapServiceInterface? _unifiedMapService;

  /// Unified marker service (replaces ProgressiveMarkerLoadingService and SmartMarkerFilteringService)
  UnifiedMarkerServiceInterface? _unifiedMarkerService;

  /// Optimized camera controller for smooth operations
  final OptimizedCameraController _cameraController = OptimizedCameraController();

  /// Context7 MCP: Location centering controller to prevent infinite loops
  final LocationCenteringController _locationCenteringController = LocationCenteringController();

  /// Track if hierarchical clustering is initialized
  bool _isHierarchicalClusteringInitialized = false;

  // Map state management fields (previously from MapStateMixin)
  GoogleMapController? mapController;
  double currentZoom = ClusteringConfig.defaultZoom;
  Timer? _debounceTimer;

  /// Performance metrics from clustering operations
  PerformanceMetrics _lastPerformanceMetrics = const PerformanceMetrics();

  /// Track clustering state
  bool _isClusteringInProgress = false;

  /// Track if markers have been built to prevent rebuilding on every frame
  bool _markersBuilt = false;
  List<MasjidModel>? _lastMasjidData;

  /// Default camera position (Bahrain) - now using unified configuration
  static CameraPosition get _defaultCameraPosition => const CameraPosition(
    target: LatLng(ClusteringConfig.defaultLatitude, ClusteringConfig.defaultLongitude),
    zoom: ClusteringConfig.defaultZoom,
    tilt: AppConstants.defaultMapTilt,
    bearing: AppConstants.defaultMapBearing,
  );

  @override
  void initState() {
    super.initState();
    initializeMapState(onClusteringRefreshNeeded: _clusterMarkers);

    // Initialize camera controller
    _cameraController.initialize(
      onCameraMove: _onOptimizedCameraMove,
      onCameraIdle: _onOptimizedCameraIdle,
      onPerformanceUpdate: _onCameraPerformanceUpdate,
    );

    // Initialize unified services asynchronously
    _initializeUnifiedServices();

    // Context7 MCP: Location listening will be handled in build method using ref.watch
    // This prevents the debugDoingBuild assertion error

    // Context7 MCP: Initialize automatic location integration (without ref.listen)
    _initializeAutomaticLocationIntegration();
  }

  /// Initialize unified services from Riverpod providers
  Future<void> _initializeUnifiedServices() async {
    try {
      // Get unified services from providers
      _unifiedMapService = await ref.read(unifiedMapServiceProvider.future);
      _unifiedMarkerService = ref.read(unifiedMarkerServiceProvider);

      // Start memory monitoring for large datasets
      _unifiedMapService?.startMemoryMonitoring();

      debugPrint('OptimizedMapWidget: Unified services initialized successfully');
    } on Exception catch (e) {
      debugPrint('OptimizedMapWidget: Error initializing unified services: $e');
    }
  }

  /// Initialize automatic location integration with Context7 MCP best practices
  /// Context7 MCP: Seamless location-map integration for automatic centering
  void _initializeAutomaticLocationIntegration() {
    try {
      debugPrint('OptimizedMapWidget: Initializing automatic location integration...');

      // Context7 MCP: Location listener is now set up directly in initState
      // This prevents the debugDoingBuild assertion error

      // Context7 MCP: Location availability check is now handled in build method
      // This prevents any ref access issues in initState

      debugPrint('OptimizedMapWidget: Automatic location integration initialized');
    } on Exception catch (e) {
      debugPrint('OptimizedMapWidget: Error initializing automatic location integration: $e');
    }
  }

  // Context7 MCP: Old automatic centering methods removed
  // Replaced with LocationCenteringController for proper state management

  @override
  void dispose() {
    disposeMapState();
    if (_isHierarchicalClusteringInitialized) {
      _unifiedClustering.dispose();
    }

    // Stop memory monitoring via unified map service
    _unifiedMapService?.stopMemoryMonitoring();

    _cameraController.dispose();
    // Context7 MCP: Dispose location centering controller
    _locationCenteringController.dispose();
    super.dispose();
  }

  /// Handle optimized camera movement
  void _onOptimizedCameraMove(CameraPosition position) {
    // Debug zoom level changes for clustering optimization
    debugPrint('🔍 MAP: Camera moved - zoom: ${position.zoom.toStringAsFixed(2)}, target: ${position.target}');

    // Call the original camera move handler from MapStateMixin
    onCameraMove(position);
  }

  /// Handle optimized camera idle
  void _onOptimizedCameraIdle() {
    // Call the original camera idle handler from MapStateMixin
    onCameraIdle();
  }

  /// Handle camera performance updates
  void _onCameraPerformanceUpdate(double frameSkipPercentage) {
    // Performance monitoring - can be used for debugging if needed
    debugPrint('Camera performance: ${frameSkipPercentage.toStringAsFixed(1)}% frame skip');
  }

  /// Initialize hierarchical clustering with masjid data
  Future<void> _initializeHierarchicalClustering(List<MasjidModel> masjids) async {
    if (_isHierarchicalClusteringInitialized && masjids.length == _lastMasjidCount) {
      debugPrint('Hierarchical clustering already initialized for ${masjids.length} masjids');
      return;
    }

    try {
      debugPrint('🔄 Initializing Bahrain-optimized hierarchical clustering for ${masjids.length} masjids...');
      final stopwatch = Stopwatch()..start();

      // Clear any existing cache and reinitialize with new Bahrain-optimized logic
      _unifiedClustering.clearCache();
      await _unifiedClustering.initialize(masjids);

      stopwatch.stop();
      _isHierarchicalClusteringInitialized = true;
      _lastMasjidCount = masjids.length;
      final metrics = _unifiedClustering.getPerformanceMetrics();
      _lastPerformanceMetrics = PerformanceMetrics(
        averageClusteringTime: metrics['averageClusteringTime'] ?? 0.0,
        cacheHitRate: metrics['cacheHitRate'] ?? 0.0,
        totalOperations: metrics['totalOperations'] ?? 0,
        clusterLevels: metrics['clusterLevels'] ?? 0,
        cachedResults: metrics['cacheSize'] ?? 0,
      );

      debugPrint(
        '✅ Bahrain-optimized unified clustering initialized successfully in ${stopwatch.elapsedMilliseconds}ms',
      );
      debugPrint('📊 Performance metrics: ${_lastPerformanceMetrics.toJson()}');
    } on Exception catch (e) {
      debugPrint('❌ Failed to initialize unified clustering: $e');
      _isHierarchicalClusteringInitialized = false;
    }
  }

  /// Track last masjid count for initialization optimization
  int _lastMasjidCount = 0;

  /// Cluster markers using hierarchical clustering with fallback to legacy clustering
  Future<void> _clusterMarkers() async {
    // Check if the widget is still mounted before proceeding
    if (!mounted || _isClusteringInProgress) {
      debugPrint('Widget not mounted or clustering in progress, skipping clustering');
      return;
    }

    debugPrint(
      'Clustering markers: controller=${mapController != null ? 'available' : 'null'}, markers=${_markers.length}, zoom=$currentZoom',
    );

    // Validate required objects
    if (mapController == null) {
      debugPrint('Skipping clustering: No map controller available');
      // Try again after a short delay in case controller is being initialized
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted && mapController != null) {
          debugPrint('Map controller now available, retrying clustering');
          _clusterMarkers();
        }
      });
      return;
    }

    if (_markers.isEmpty) {
      debugPrint('⚠️ Skipping clustering: No markers to cluster (${_markers.length} markers available)');
      return;
    }

    _isClusteringInProgress = true;

    try {
      debugPrint('Starting optimized marker clustering with ${_markers.length} markers at zoom $currentZoom');

      // Use unified clustering check from MapStateMixin
      debugPrint('🔍 Clustering check: markers=${_markers.length}, zoom=$currentZoom');
      debugPrint(
        '🔍 Thresholds: maxMarkers=${ClusteringConfig.maxMarkersWithoutClustering}, minZoom=${ClusteringConfig.minZoomForIndividualMarkers}',
      );

      if (shouldShowIndividualMarkers(_markers.length)) {
        debugPrint('✋ Using individual markers (small count or high zoom)');
        if (mounted) {
          setState(() {
            _clusteredMarkers = Set<Marker>.from(_markers);
          });
        }
        _isClusteringInProgress = false;
        return;
      }

      debugPrint('🎯 Proceeding with clustering: ${_markers.length} markers at zoom $currentZoom');

      // Get a reference to the controller to avoid null issues
      final controller = mapController;
      if (controller == null) {
        debugPrint('Map controller became null, skipping clustering');
        _isClusteringInProgress = false;
        return;
      }

      Set<Marker> clustered;

      // Try hierarchical clustering first if available and initialized
      // Debug logging can be enabled for troubleshooting
      // debugPrint('🔧 Hierarchical clustering initialized: $_isHierarchicalClusteringInitialized, masjidData: ${_lastMasjidData?.length}');
      if (_isHierarchicalClusteringInitialized && _lastMasjidData != null) {
        debugPrint('✅ Using hierarchical clustering for ${_lastMasjidData!.length} masjids');

        try {
          // Get current viewport with proper error handling
          LatLngBounds? viewport;
          try {
            // Add a small delay to ensure map is fully initialized
            await Future.delayed(const Duration(milliseconds: 100));

            // Check if controller is still valid and map is ready
            if (!mounted || mapController == null) {
              debugPrint('Map controller became invalid during clustering');
              return;
            }

            viewport = await controller.getVisibleRegion();
          } on PlatformException catch (platformError) {
            debugPrint('Platform channel error getting visible region: $platformError');
            // Use fallback clustering without viewport
            if (mounted) {
              setState(() {
                _clusteredMarkers = Set<Marker>.from(_markers);
              });
            }
            return;
          } on Exception catch (error) {
            debugPrint('Unexpected error getting visible region: $error');
            // Use fallback clustering without viewport
            if (mounted) {
              setState(() {
                _clusteredMarkers = Set<Marker>.from(_markers);
              });
            }
            return;
          }

          final locale = ref.read(currentLanguageCodeProvider);

          // Use unified clustering method
          clustered = await _unifiedClustering
              .getClusteredMarkers(
                viewport: viewport,
                zoom: currentZoom,
                onMasjidTapped: _handleMasjidTapped,
                locale: locale,
              )
              .timeout(
                getClusteringTimeout(), // Use unified timeout from ClusteringConfig
                onTimeout: () {
                  debugPrint('Unified clustering timed out, using individual markers');
                  return Set<Marker>.from(_markers);
                },
              );

          final metrics = _unifiedClustering.getPerformanceMetrics();
          _lastPerformanceMetrics = PerformanceMetrics(
            averageClusteringTime: metrics['averageClusteringTime'] ?? 0.0,
            cacheHitRate: metrics['cacheHitRate'] ?? 0.0,
            totalOperations: metrics['totalOperations'] ?? 0,
            clusterLevels: metrics['clusterLevels'] ?? 0,
            cachedResults: metrics['cacheSize'] ?? 0,
          );

          debugPrint(
            'Unified clustering complete: ${clustered.length} clustered markers created '
            '(avg time: ${_lastPerformanceMetrics.averageClusteringTime.toStringAsFixed(1)}ms, '
            'cache hit rate: ${_lastPerformanceMetrics.cacheHitRate.toStringAsFixed(1)}%)',
          );
        } on Exception catch (e) {
          debugPrint('Hierarchical clustering failed: $e, using individual markers');
          clustered = Set<Marker>.from(_markers);
        }
      } else {
        // Hierarchical clustering not available - use individual markers
        debugPrint('Hierarchical clustering not initialized, using individual markers');
        clustered = Set<Marker>.from(_markers);
      }

      // Update the state with clustered markers
      if (mounted) {
        setState(() {
          _clusteredMarkers = clustered;
          // debugPrint('🎯 CLUSTERING RESULT: Set ${_clusteredMarkers.length} clustered markers (from ${clustered.length} input)');
        });
      }
    } on Exception catch (e) {
      debugPrint('Error during clustering: $e');
      // Fallback to original markers
      if (mounted) {
        setState(() {
          _clusteredMarkers = Set<Marker>.from(_markers);
        });
      }
    } finally {
      _isClusteringInProgress = false;
    }
  }

  /// Build markers from filtered masjids data
  Future<void> _buildMarkersFromMasjids(List<MasjidModel> masjids, String locale) async {
    if (!mounted) return;

    try {
      // Initialize hierarchical clustering with masjid data
      await _initializeHierarchicalClustering(masjids);

      // Create markers directly from masjid data
      final newMarkers = <Marker>{};

      for (final masjid in masjids) {
        final marker = Marker(
          markerId: MarkerId(masjid.id),
          position: LatLng(masjid.latitude, masjid.longitude),
          infoWindow: InfoWindow(
            title: locale == 'ar'
                ? (masjid.officialNameAr ?? masjid.commonNameAr ?? 'مسجد')
                : (masjid.officialNameEn ?? masjid.commonNameEn ?? 'Masjid'),
          ),
          onTap: () => _handleMasjidTapped(masjid),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
        );
        newMarkers.add(marker);
      }

      // Schedule state update after the current build is complete
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _markers.clear();
              _markers.addAll(newMarkers);
            });

            debugPrint('✅ Markers built: ${_markers.length} markers created');

            // Trigger clustering after markers are built and state is updated
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted && mapController != null) {
                debugPrint('🔄 Triggering clustering after markers are set');
                _clusterMarkers();
              }
            });
          }
        });
      }
    } on Exception catch (e) {
      debugPrint('Error building markers from masjids data: $e');
    }
  }

  /// Handle masjid marker tap - call callback with masjid data
  void _handleMasjidTapped(MasjidModel masjid) {
    if (widget.onMasjidTapped == null) return;

    // Track interaction using unified marker service
    _unifiedMarkerService?.trackUserInteraction(masjid.id);

    // Track memory usage using unified map service
    _unifiedMapService?.trackMemoryUsage(masjid.id, 0.1); // Estimate 0.1MB per marker

    // Call the callback with the masjid data
    widget.onMasjidTapped!(masjid);
  }

  /// Get comprehensive memory statistics
  /// Get detailed memory and performance statistics
  Map<String, dynamic> getDetailedStatistics() {
    final memoryStats = _unifiedMapService?.getMemoryStats() ?? {};
    final progressiveStats = _unifiedMarkerService?.getProgressiveLoadingStats() ?? {};

    return {
      'memory_optimization': memoryStats,
      'progressive_loading': progressiveStats,
      'total_markers': _markers.length,
      'clustering_enabled': _isHierarchicalClusteringInitialized,
    };
  }

  /// Get comprehensive performance statistics including smart filtering
  Map<String, dynamic> getComprehensiveStatistics() {
    final memoryStats = _unifiedMapService?.getMemoryStats() ?? {};
    final progressiveStats = _unifiedMarkerService?.getProgressiveLoadingStats() ?? {};
    final smartFilteringStats = _unifiedMarkerService?.getFilteringStats() ?? {};
    final offlineStats = _unifiedMapService?.getPerformanceMetrics() ?? {};

    return {
      'memory_optimization': memoryStats,
      'progressive_loading': progressiveStats,
      'smart_filtering': smartFilteringStats,
      'offline_management': offlineStats,
      'total_markers': _markers.length,
      'clustering_enabled': _isHierarchicalClusteringInitialized,
      'performance_summary': {
        'memory_usage_mb': (memoryStats['current_memory_mb'] as double?) ?? 0.0,
        'cache_hit_rate': (progressiveStats['cache_hit_rate'] as double?) ?? 0.0,
        'filtering_operations': (smartFilteringStats['total_operations'] as int?) ?? 0,
        'offline_tiles_cached':
            ((offlineStats['tile_cache'] as Map<String, dynamic>?)?['total_tiles_cached'] as int?) ?? 0,
      },
    };
  }

  /// Get offline statistics from unified map service
  Map<String, dynamic> getOfflineStatistics() {
    return _unifiedMapService?.getPerformanceMetrics() ?? {};
  }

  /// Get memory statistics from unified map service
  Map<String, dynamic> getMemoryStatistics() {
    return _unifiedMapService?.getMemoryStats() ?? {};
  }

  /// Get marker statistics from unified marker service
  Map<String, dynamic> getMarkerStatistics() {
    return _unifiedMarkerService?.getPerformanceMetrics() ?? {};
  }

  @override
  Widget build(BuildContext context) {
    // Context7 MCP: Listen for location availability changes and trigger centering when it becomes available
    final locationState = ref.watch(masterLocationManagerProvider);
    final isLocationAvailable = locationState.hasLocation;
    final masterLocationData = locationState.currentLocation ?? locationState.lastKnownLocation;

    ref.listen(masterLocationManagerProvider.select((state) => state.hasLocation), (previous, next) {
      if (mounted && (previous == false) && next) {
        debugPrint('OptimizedMapWidget: Location became available, using location centering controller');
        final locationState = ref.read(masterLocationManagerProvider);
        final masterLocationData = locationState.currentLocation ?? locationState.lastKnownLocation;
        if (masterLocationData != null) {
          _locationCenteringController.handleLocationAvailable(masterLocationData);
        }
      }
    });

    // Context7 MCP: Handle location availability with proper state management
    if (isLocationAvailable && masterLocationData != null) {
      _locationCenteringController.handleLocationAvailable(masterLocationData);
    }

    // Context7 MCP: Handle first launch location initialization
    final firstLaunchStatus = ref.watch(firstLaunchStatusProvider);
    final isFirstLaunch = firstLaunchStatus['isFirstLaunch'] as bool?;
    final firstLaunchLocation = firstLaunchStatus['location'] as LocationData?;

    // If first launch completed successfully and we have location data, use it for centering
    if (isFirstLaunch == true && firstLaunchLocation != null && !isLocationAvailable) {
      debugPrint('OptimizedMapWidget: Using first launch location data for initial centering');
      _locationCenteringController.handleLocationAvailable(firstLaunchLocation, force: true);
    }

    // Use select() to only rebuild when language code changes
    final locale = ref.watch(currentLanguageCodeProvider);

    // Get tutorial keys for the location button
    final tutorialKeys = ref.watch(tutorialKeysProvider);

    // Get the current search query - use select() to only rebuild when search status changes
    final isSearchActive = ref.watch(homeSearchQueryProvider.select((query) => query.trim().isNotEmpty));

    // Get masjids based on search or filter (same logic as other homepage map widgets)
    final masjidsAsync = isSearchActive
        ? ref.watch(debouncedHomeSearchResultsProvider)
        : ref.watch(filteredMasjidsProvider);

    return MapStateWidget<List<MasjidModel>>(
      asyncValue: masjidsAsync,
      dataBuilder: (masjids) => _buildMapWidgetFromMasjids(masjids, locale, tutorialKeys),
      isEmpty: (masjids) => masjids.isEmpty,
      errorBuilder: (error, stackTrace) =>
          MapErrorWidget(error: error, onRetry: () async => _handleMapRetry(isSearchActive)),
    );
  }

  /// Build the actual map widget from filtered masjids data
  Widget _buildMapWidgetFromMasjids(
    List<MasjidModel> masjids,
    String locale,
    Map<TutorialTargetType, GlobalKey> tutorialKeys,
  ) {
    // Only build markers if data has changed or markers haven't been built yet
    if (!_markersBuilt || _lastMasjidData != masjids) {
      debugPrint('Building markers: ${masjids.length} masjids, locale: $locale');
      // Initialize markers and hierarchical clustering asynchronously
      _buildMarkersFromMasjids(masjids, locale).then((_) {
        if (mounted) {
          debugPrint('Hierarchical clustering initialization complete, ready for clustering');
          // Trigger clustering after initialization is complete
          if (mapController != null) {
            _clusterMarkers();
          }
        }
      });
      _markersBuilt = true;
      _lastMasjidData = masjids;
    }

    // Update masjid counts
    if (widget.onMasjidCountsUpdated != null) {
      final activeCount = masjids.where((m) => m.isActive == true).length;
      final inactiveCount = masjids.length - activeCount;
      widget.onMasjidCountsUpdated!(activeCount, inactiveCount);
    }

    // Determine which markers to use (clustered or individual)
    final markersToUse = _clusteredMarkers.isNotEmpty ? _clusteredMarkers : _markers;

    // debugPrint('🗺️ MAP MARKERS: Using ${markersToUse.length} markers (_clusteredMarkers: ${_clusteredMarkers.length}, _markers: ${_markers.length})');

    return RepaintBoundary(
      child: Stack(
        children: [
          // Resilient Google Map with enhanced error handling
          Container(
            key: tutorialKeys[TutorialTargetType.mapView],
            child: ResilientMapWidget(
              initialCameraPosition: _defaultCameraPosition,
              markers: markersToUse,
              showUserLocation: true,
              showLocationButton: ClusteringConfig.showMyLocationButton,
              onMapCreated: (controller) {
                // Set map controller in mixin first
                onMapCreated(controller);
                // Then set it in camera controller
                _cameraController.setMapController(controller);
                // Context7 MCP: Set map controller in location centering controller
                _locationCenteringController.setMapController(controller);
                // Set map controller in unified clustering service for cluster expansion
                _unifiedClustering.setMapController(controller);
                // Add longer delay to ensure everything is properly initialized and platform channels are ready
                Future.delayed(const Duration(milliseconds: 1000), () {
                  if (mounted && mapController != null) {
                    debugPrint('Map controller fully initialized, ready for clustering');
                    // Trigger initial clustering if needed
                    _clusterMarkers();
                  }
                });
              },
              onCameraMove: _cameraController.onCameraMove,
              onCameraIdle: _cameraController.onCameraIdle,
            ),
          ),

          // Nearby Masjids Button (top left)
          const PositionedNearbyMasjidsButton(top: 16, left: 16),

          // Current Location Button (top right corner)
          Positioned(
            top: 0,
            right: 0,
            child: CurrentLocationButton(
              mapController: mapController,
              tutorialKey: tutorialKeys[TutorialTargetType.locationButton],
            ),
          ),

          // Masjid card overlay at the bottom of the map
          if (widget.selectedMasjid != null)
            Positioned(
              bottom: 10,
              left: 10,
              right: 10,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Masjid card
                    MapMasjidCard(
                      masjid: widget.selectedMasjid!,
                      onClose: () {}, // Empty callback since we handle close externally
                      showNearestLabel: widget.isNearestMasjid,
                    ),

                    // Close button positioned higher up and smaller
                    Positioned(
                      top: -30,
                      right: -8,
                      child: Container(
                        width: 38,
                        height: 38,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(color: Colors.black.withAlpha(51), blurRadius: 4, offset: const Offset(0, 2)),
                          ],
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.close, color: Colors.grey),
                          onPressed: widget.onOverlayClose,
                          iconSize: 16,
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(minWidth: 38, minHeight: 38),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Map state management methods (previously from MapStateMixin)
  void initializeMapState({VoidCallback? onClusteringRefreshNeeded}) {
    // Initialize map state - functionality moved from MapStateMixin
  }

  void disposeMapState() {
    _debounceTimer?.cancel();
    mapController?.dispose();
    mapController = null;
  }

  void onCameraMove(CameraPosition position) {
    currentZoom = position.zoom;
    // Context7 MCP: Track user camera interaction to prevent automatic centering
    _locationCenteringController.handleUserCameraInteraction();
  }

  void onCameraIdle() {
    debugPrint('🔍 MAP: Camera idle at zoom ${currentZoom.toStringAsFixed(2)}, triggering clustering');

    // Use unified camera debounce delay to avoid clustering on every tiny camera movement
    // This follows the same pattern as other map widgets in the codebase
    Future.delayed(ClusteringConfig.cameraDebounceDelay, () {
      if (mounted && mapController != null) {
        debugPrint('🔄 Executing delayed clustering after camera idle');
        _clusterMarkers();
      } else {
        debugPrint('⚠️ Skipping clustering: widget not mounted or map controller null');
      }
    });
  }

  bool shouldShowIndividualMarkers(int markerCount) {
    return markerCount <= ClusteringConfig.maxMarkersWithoutClustering ||
        currentZoom >= ClusteringConfig.minZoomForIndividualMarkers;
  }

  Duration getClusteringTimeout() {
    return ClusteringConfig.clusteringTimeout;
  }

  void onMapCreated(GoogleMapController controller) {
    mapController = controller;
    // Set controller for camera operations
    debugPrint('Map controller set');
  }

  /// Enhanced map retry with comprehensive provider management
  /// Following Context7 MCP best practices for error recovery
  Future<void> _handleMapRetry(bool isSearchActive) async {
    if (kDebugMode) {
      print('🔄 Starting enhanced map retry with Context7 MCP best practices, isSearchActive: $isSearchActive');
    }

    try {
      // Reset internal state
      _markersBuilt = false;
      _lastMasjidData = null;
      _clusteredMarkers.clear();
      _markers.clear();

      // Determine provider IDs based on search state
      final providerIds = isSearchActive
          ? ['debouncedHomeSearchResultsProvider', 'homeSearchQueryProvider']
          : ['randomizedFilteredMasjidsProvider'];

      // Use enhanced retry system with circuit breaker and exponential backoff
      final retryResult = await ref.invalidateProvidersWithEnhancedRetry(providerIds, resetCircuitBreakers: true);

      // Context7 MCP best practice: Proper Result<void> handling with switch pattern matching
      switch (retryResult) {
        case Success():
          if (kDebugMode) print('✅ Enhanced map retry completed successfully');
        case Failure(error: final error):
          if (kDebugMode) print('⚠️ Enhanced retry had issues: ${error.toString()}, attempting fallback');
          // Fallback to basic invalidation
          await _performBasicInvalidation(isSearchActive);
      }

      // Add stabilization delay
      await Future.delayed(const Duration(milliseconds: 300));
    } on Exception catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Error during enhanced map retry: $e');
        print('Stack trace: $stackTrace');
      }

      // Log error with enhanced system
      try {
        final retrySystem = ref.read(enhancedMapRetrySystemProvider);
        retrySystem.getSystemHealth(); // This will log internally
      } on Exception catch (_) {
        // Ignore logging errors
      }

      // Fallback to basic invalidation
      await _performBasicInvalidation(isSearchActive);
    }
  }

  /// Perform basic provider invalidation as fallback
  Future<void> _performBasicInvalidation(bool isSearchActive) async {
    try {
      if (isSearchActive) {
        ref.invalidate(debouncedHomeSearchResultsProvider);
        ref.invalidate(homeSearchQueryProvider);
      } else {
        ref.invalidate(filteredMasjidsProvider);
      }

      // Small delay for processing
      await Future.delayed(const Duration(milliseconds: 100));

      if (kDebugMode) print('✅ Basic invalidation completed as fallback');
    } on Exception catch (e) {
      if (kDebugMode) print('❌ Even basic invalidation failed: $e');
    }
  }
}
