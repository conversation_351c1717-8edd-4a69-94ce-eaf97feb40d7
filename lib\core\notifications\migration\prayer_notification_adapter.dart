import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../logging/app_logger.dart';
import 'deployment_config.dart';
import 'main_app_adapter.dart';
import 'service_registry_adapter.dart' as registry;

part 'prayer_notification_adapter.g.dart';

/// Prayer Notification Adapter for Critical Path Migration
///
/// **Context7 MCP Implementation:**
/// - Provides transparent routing for prayer notification functionality
/// - Ensures backward compatibility during migration
/// - Handles graceful fallback to legacy providers
/// - Monitors prayer notification performance and health
/// - Supports emergency rollback scenarios
/// - Implements comprehensive dependency injection patterns
///
/// **Critical Path Features:**
/// - Prayer time scheduling with location-based calculations
/// - Notification permission management
/// - Sound and vibration configuration
/// - Quiet hours and Do Not Disturb integration
/// - Multi-language notification content
/// - Notification history and analytics
@riverpod
Future<PrayerNotificationAdapter> prayerNotificationAdapter(Ref ref) async {
  try {
    AppLogger.debug('PrayerNotificationAdapter: Initializing prayer notification adapter');

    // Get deployment configuration
    final deploymentConfig = await ref.watch(deploymentConfigProvider.future);

    // Get service registry
    final serviceRegistry = await ref.watch(registry.unifiedServiceRegistryProvider.future);

    // Get main app adapters
    final notificationManager = await ref.watch(mainAppNotificationManagerProvider.future);
    final notificationSettings = await ref.watch(mainAppNotificationSettingsProvider.future);

    // Create prayer notification adapter
    final adapter = PrayerNotificationAdapter(
      serviceRegistry: serviceRegistry,
      notificationManager: notificationManager,
      notificationSettings: notificationSettings,
      deploymentConfig: deploymentConfig,
      isUsingUnifiedProvider: deploymentConfig.shouldUseUnifiedProvider,
    );

    // Validate adapter functionality
    await adapter.validateConfiguration();

    AppLogger.debug(
      'PrayerNotificationAdapter: Adapter initialized successfully',
      context: {
        'usingUnifiedProvider': deploymentConfig.shouldUseUnifiedProvider,
        'settingsSource': notificationSettings.source,
        'managerSource': notificationManager.source,
      },
    );

    return adapter;
  } on Exception catch (e, stackTrace) {
    AppLogger.error(
      'PrayerNotificationAdapter: Failed to initialize adapter',
      context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
    );

    // Return emergency fallback adapter
    return PrayerNotificationAdapter.emergency();
  }
}

/// Prayer Notification Adapter
///
/// Context7 MCP: Provides unified prayer notification management with comprehensive
/// service integration, deployment configuration, and migration support.
///
/// This adapter serves as the primary interface for prayer notification functionality,
/// consolidating multiple notification providers into a single, cohesive service.
///
/// Features:
/// - Unified service registry integration
/// - Progressive deployment support
/// - Comprehensive error handling and fallback mechanisms
/// - Performance monitoring and analytics
/// - Migration utilities for legacy provider consolidation
class PrayerNotificationAdapter {
  /// Context7 MCP: Unified service registry for centralized service management
  ///
  /// Provides access to all notification-related services including notification
  /// managers, settings providers, and deployment configurations.
  final registry.UnifiedServiceRegistry serviceRegistry;

  /// Context7 MCP: Main application notification manager
  ///
  /// Handles core notification delivery, scheduling, and lifecycle management
  /// with comprehensive error handling and fallback strategies.
  final MainAppNotificationManager notificationManager;

  /// Context7 MCP: Main application notification settings
  ///
  /// Manages user preferences, notification configurations, and permission
  /// settings with automatic persistence and validation.
  final MainAppNotificationSettings notificationSettings;

  /// Context7 MCP: Deployment configuration for progressive rollout
  ///
  /// Controls feature flags, rollout percentages, and deployment strategies
  /// for safe and controlled notification system updates.
  final DeploymentConfig deploymentConfig;

  /// Context7 MCP: Flag indicating unified provider usage
  ///
  /// Determines whether the adapter is using the new unified notification
  /// provider or falling back to legacy providers during migration.
  final bool isUsingUnifiedProvider;

  /// Context7 MCP: Adapter creation timestamp
  ///
  /// Records when this adapter instance was created for debugging,
  /// analytics, and lifecycle management purposes.
  final DateTime createdAt;

  /// Context7 MCP: Creates a new prayer notification adapter instance
  ///
  /// Initializes the adapter with all required services and configurations
  /// for comprehensive prayer notification management.
  ///
  /// Parameters:
  /// - [serviceRegistry]: Unified service registry for service access
  /// - [notificationManager]: Main notification manager instance
  /// - [notificationSettings]: User notification preferences
  /// - [deploymentConfig]: Deployment and feature flag configuration
  /// - [isUsingUnifiedProvider]: Whether to use unified or legacy providers
  /// - [createdAt]: Optional creation timestamp (defaults to now)
  PrayerNotificationAdapter({
    required this.serviceRegistry,
    required this.notificationManager,
    required this.notificationSettings,
    required this.deploymentConfig,
    required this.isUsingUnifiedProvider,
  }) : createdAt = DateTime.now();

  /// Create emergency fallback adapter
  factory PrayerNotificationAdapter.emergency() {
    return PrayerNotificationAdapter(
      serviceRegistry: registry.UnifiedServiceRegistry.emergency(),
      notificationManager: MainAppNotificationManager.noOp(),
      notificationSettings: MainAppNotificationSettings.safe(),
      deploymentConfig: DeploymentConfig.emergency(),
      isUsingUnifiedProvider: false,
    );
  }

  /// Schedule prayer notifications for specific date and location
  Future<void> schedulePrayerNotifications({
    required DateTime date,
    required double latitude,
    required double longitude,
    List<String>? specificPrayers,
  }) async {
    try {
      AppLogger.debug(
        'PrayerNotificationAdapter: Scheduling prayer notifications',
        context: {
          'date': date.toIso8601String(),
          'latitude': latitude,
          'longitude': longitude,
          'specificPrayers': specificPrayers,
          'usingUnified': isUsingUnifiedProvider,
        },
      );

      // Check if prayer notifications are enabled
      if (!notificationSettings.prayerNotificationsEnabled) {
        AppLogger.debug('PrayerNotificationAdapter: Prayer notifications disabled, skipping');
        return;
      }

      // Use prayer notification service interface
      final prayerService = serviceRegistry.prayerNotificationService;

      await prayerService.schedulePrayerNotifications(
        date: date,
        latitude: latitude,
        longitude: longitude,
        specificPrayers: specificPrayers,
      );

      AppLogger.debug('PrayerNotificationAdapter: Prayer notifications scheduled successfully');
    } catch (e, stackTrace) {
      AppLogger.error(
        'PrayerNotificationAdapter: Failed to schedule prayer notifications',
        context: {
          'error': e.toString(),
          'stackTrace': stackTrace.toString(),
          'date': date.toIso8601String(),
          'latitude': latitude,
          'longitude': longitude,
        },
      );
      rethrow;
    }
  }

  /// Cancel all prayer notifications
  Future<void> cancelAllPrayerNotifications() async {
    try {
      AppLogger.debug('PrayerNotificationAdapter: Cancelling all prayer notifications');

      await notificationManager.cancelAllNotifications();

      AppLogger.debug('PrayerNotificationAdapter: All prayer notifications cancelled');
    } catch (e, stackTrace) {
      AppLogger.error(
        'PrayerNotificationAdapter: Failed to cancel prayer notifications',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Get enabled prayers
  List<String> get enabledPrayers {
    return serviceRegistry.prayerNotificationService.enabledPrayers;
  }

  /// Check if specific prayer is enabled
  bool isPrayerEnabled(String prayerName) {
    return notificationSettings.isPrayerEnabled(prayerName);
  }

  /// Get prayer notification settings
  PrayerNotificationSettings get prayerSettings {
    return PrayerNotificationSettings(
      globallyEnabled: notificationSettings.globallyEnabled,
      prayerNotificationsEnabled: notificationSettings.prayerNotificationsEnabled,
      soundEnabled: notificationSettings.soundEnabled,
      vibrationEnabled: notificationSettings.vibrationEnabled,
      prayerSettings: notificationSettings.prayerSettings,
      source: notificationSettings.source,
      isUsingUnifiedProvider: isUsingUnifiedProvider,
    );
  }

  /// Validate adapter configuration
  Future<void> validateConfiguration() async {
    try {
      // Validate notification manager
      await notificationManager.validateConfiguration();

      // Validate service registry
      final isValid = await serviceRegistry.validateServices();
      if (!isValid) {
        throw Exception('Service registry validation failed');
      }

      // Validate prayer notification service
      final prayerService = serviceRegistry.prayerNotificationService;
      if (!prayerService.isUsingUnifiedProvider && isUsingUnifiedProvider) {
        throw Exception('Prayer service provider mismatch');
      }

      AppLogger.debug('PrayerNotificationAdapter: Configuration validated successfully');
    } catch (e, stackTrace) {
      AppLogger.error(
        'PrayerNotificationAdapter: Configuration validation failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Get adapter health status
  PrayerNotificationAdapterHealth get health {
    return PrayerNotificationAdapterHealth(
      isHealthy: _isHealthy(),
      usingUnifiedProvider: isUsingUnifiedProvider,
      settingsSource: notificationSettings.source,
      managerSource: notificationManager.source,
      serviceRegistryHealth: serviceRegistry.health,
      uptime: DateTime.now().difference(createdAt),
    );
  }

  /// Check if adapter is healthy
  bool _isHealthy() {
    return notificationSettings.source != 'error' &&
        notificationManager.source != 'noOp' &&
        serviceRegistry.health.isHealthy &&
        deploymentConfig.isValid;
  }
}

/// Prayer Notification Settings
class PrayerNotificationSettings {
  final bool globallyEnabled;
  final bool prayerNotificationsEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final Map<String, bool> prayerSettings;
  final String source;
  final bool isUsingUnifiedProvider;

  const PrayerNotificationSettings({
    required this.globallyEnabled,
    required this.prayerNotificationsEnabled,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.prayerSettings,
    required this.source,
    required this.isUsingUnifiedProvider,
  });

  /// Check if specific prayer is enabled
  bool isPrayerEnabled(String prayerName) {
    return globallyEnabled && prayerNotificationsEnabled && (prayerSettings[prayerName] ?? false);
  }

  /// Get enabled prayers list
  List<String> get enabledPrayers {
    return prayerSettings.entries.where((entry) => entry.value).map((entry) => entry.key).toList();
  }
}

/// Prayer Notification Adapter Health
class PrayerNotificationAdapterHealth {
  final bool isHealthy;
  final bool usingUnifiedProvider;
  final String settingsSource;
  final String managerSource;
  final registry.ServiceRegistryHealth serviceRegistryHealth;
  final Duration uptime;

  const PrayerNotificationAdapterHealth({
    required this.isHealthy,
    required this.usingUnifiedProvider,
    required this.settingsSource,
    required this.managerSource,
    required this.serviceRegistryHealth,
    required this.uptime,
  });

  /// Get health summary
  String get healthSummary {
    if (!isHealthy) return 'Unhealthy';
    if (usingUnifiedProvider) return 'Healthy (Unified)';
    return 'Healthy (Legacy)';
  }

  /// Get detailed status
  Map<String, dynamic> get detailedStatus {
    return {
      'healthy': isHealthy,
      'unified_provider': usingUnifiedProvider,
      'settings_source': settingsSource,
      'manager_source': managerSource,
      'service_registry': serviceRegistryHealth.detailedStatus,
      'uptime_seconds': uptime.inSeconds,
      'health_summary': healthSummary,
    };
  }
}
