import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/home/<USER>/providers/unified_nearby_masjids_provider.dart';
import '../models/location_permission_models.dart';
import 'master_location_provider.dart';

/// Location readiness coordination system with Context7 MCP best practices
///
/// Context7 MCP Features:
/// - Comprehensive provider-based location readiness coordination
/// - Automatic triggering of location-dependent features using reactive patterns
/// - Race condition prevention between location services and UI components
/// - Graceful degradation when location is unavailable
/// - Performance optimized with proper state management
///
/// This coordinator ensures that all location-dependent features are properly
/// synchronized and triggered when location becomes available, providing a
/// seamless user experience without manual intervention.

/// Simple location readiness provider with Context7 MCP comprehensive readiness check
/// Context7 MCP: Comprehensive location readiness assessment
final locationFullyReadyProvider = Provider<bool>((ref) {
  final isLocationServiceReady = ref.watch(isLocationServiceReadyProvider);
  final hasPermission = ref.watch(permissionStatusSelectorProvider);

  // Context7 MCP: Convert permission status to boolean
  final hasLocationPermission = hasPermission == LocationPermissionStatus.granted;

  debugPrint('LocationReadiness: Available=$isLocationServiceReady, Permission=$hasLocationPermission');

  return isLocationServiceReady && hasLocationPermission;
});

/// Location readiness level provider with Context7 MCP granular assessment
/// Context7 MCP: Granular readiness assessment with weighted factors
final locationReadinessLevelProvider = Provider<double>((ref) {
  final isLocationServiceReady = ref.watch(isLocationServiceReadyProvider);
  final hasPermission = ref.watch(permissionStatusSelectorProvider);

  // Context7 MCP: Convert permission status to boolean
  final hasLocationPermission = hasPermission == LocationPermissionStatus.granted;

  var level = 0.0;
  if (hasLocationPermission) level += 0.5; // Permission is critical
  if (isLocationServiceReady) level += 0.5; // Location availability is critical

  return level;
});

/// Location partial readiness provider with Context7 MCP partial state assessment
/// Context7 MCP: Partial readiness for graceful degradation
final locationPartiallyReadyProvider = Provider<bool>((ref) {
  final hasPermission = ref.watch(permissionStatusSelectorProvider);

  // Context7 MCP: Convert permission status to boolean
  final hasLocationPermission = hasPermission == LocationPermissionStatus.granted;

  return hasLocationPermission; // At least permission is granted
});

/// Location readiness coordinator state provider
/// Context7 MCP: Simple state provider for coordination
final locationReadinessCoordinatorProvider = Provider<bool>((ref) {
  // Just return the current readiness state
  return ref.watch(locationFullyReadyProvider);
});

/// Initialize location readiness coordination with Context7 MCP proactive initialization
/// Context7 MCP: Proactive coordination initialization for immediate setup
void initializeLocationReadinessCoordination(WidgetRef ref) {
  debugPrint('LocationReadinessCoordinator: Initializing comprehensive location readiness coordination...');

  try {
    // Context7 MCP: Set up listeners for location readiness changes
    ref.listen<bool>(locationFullyReadyProvider, (previous, next) {
      if (previous == false && next == true) {
        debugPrint('LocationReadinessCoordinator: Location became fully ready, triggering dependent features');

        // Context7 MCP: Trigger nearby masjids refresh with proper invalidation
        ref.invalidate(nearbyMasjidsProvider);

        debugPrint('LocationReadinessCoordinator: Dependent features triggered successfully');
      }
    });

    // Context7 MCP: Set up listeners for location availability changes
    ref.listen<bool>(isLocationServiceReadyProvider, (previous, next) {
      if (previous == false && next == true) {
        debugPrint('LocationReadinessCoordinator: Location became available, checking full readiness');

        // Check if we're now fully ready
        final isFullyReady = ref.read(locationFullyReadyProvider);
        if (isFullyReady) {
          debugPrint('LocationReadinessCoordinator: Now fully ready, triggering features');
          ref.invalidate(nearbyMasjidsProvider);
        }
      }
    });

    // Context7 MCP: Check initial state and trigger if already ready
    final isInitiallyReady = ref.read(locationFullyReadyProvider);
    if (isInitiallyReady) {
      debugPrint('LocationReadinessCoordinator: Location already ready on initialization, triggering features');
      ref.invalidate(nearbyMasjidsProvider);
    }

    debugPrint('LocationReadinessCoordinator: Comprehensive location readiness coordination initialized');
  } on Exception catch (e) {
    debugPrint('LocationReadinessCoordinator: Error during initialization: $e');
  }
}

/// Force trigger dependent features with Context7 MCP manual coordination
/// Context7 MCP: Manual coordination trigger for exceptional cases
void forceTriggerLocationDependentFeatures(WidgetRef ref) {
  debugPrint('LocationReadinessCoordinator: Force triggering location-dependent features');

  try {
    // Context7 MCP: Force invalidate nearby masjids provider
    ref.invalidate(nearbyMasjidsProvider);

    debugPrint('LocationReadinessCoordinator: Force trigger completed successfully');
  } on Exception catch (e) {
    debugPrint('LocationReadinessCoordinator: Error during force trigger: $e');
  }
}

/// Check location readiness status with Context7 MCP comprehensive status check
/// Context7 MCP: Comprehensive readiness status for debugging and monitoring
String getLocationReadinessStatus(WidgetRef ref) {
  try {
    final isFullyReady = ref.read(locationFullyReadyProvider);
    final isPartiallyReady = ref.read(locationPartiallyReadyProvider);
    final readinessLevel = ref.read(locationReadinessLevelProvider);
    final isLocationAvailable = ref.read(isLocationServiceReadyProvider);
    final permissionStatus = ref.read(permissionStatusSelectorProvider);

    return 'LocationReadinessStatus('
        'fullyReady: $isFullyReady, '
        'partiallyReady: $isPartiallyReady, '
        'readinessLevel: ${readinessLevel.toStringAsFixed(2)}, '
        'locationAvailable: $isLocationAvailable, '
        'permissionStatus: $permissionStatus)';
  } on Exception catch (e) {
    return 'LocationReadinessStatus(error: $e)';
  }
}
