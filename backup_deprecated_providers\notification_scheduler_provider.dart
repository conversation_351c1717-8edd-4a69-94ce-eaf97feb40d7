import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:masajid_albahrain/core/notifications/providers/unified_notification_provider.dart';
import 'package:masajid_albahrain/core/utils/logger.dart';
import 'package:masajid_albahrain/features/prayer_times/domain/providers/custom_calculation_method_provider.dart';
import 'package:masajid_albahrain/features/prayer_times/presentation/providers/prayer_times_provider.dart';
// Context7 MCP: Updated to use unified notification settings provider
// import '../../domain/providers/notification_settings_provider.dart';
// import '../../domain/services/notification_manager.dart';

part 'notification_scheduler_provider.g.dart';

/// Context7 MCP: Unified Notification Scheduler Provider
///
/// DEPRECATED: This provider is being replaced by the unified notification system.
/// Use prayerNotificationIntegration from prayer_times_provider.dart instead.
///
/// This provider is maintained for backward compatibility during the migration
/// but delegates all functionality to the unified notification manager.
@riverpod
void notificationScheduler(Ref ref) {
  AppLogger.info('🔄 Legacy notification scheduler delegating to unified system');

  // Context7 MCP: Delegate to unified notification integration
  // This ensures backward compatibility while using the new unified system
  ref.read(prayerNotificationIntegrationProvider);

  // Context7 MCP: Listen to prayer times changes and delegate to unified system
  ref.listen(allPrayerTimesProvider, (previous, next) {
    AppLogger.debug('NotificationSchedulerProvider: Prayer times changed - delegating to unified system');
    _scheduleUnifiedPrayerTimeNotifications(ref);
  });

  // Context7 MCP: Listen to calculation method changes and delegate to unified system
  ref.listen(customCalculationMethodProvider, (previous, next) {
    AppLogger.debug('NotificationSchedulerProvider: Calculation method changed - delegating to unified system');
    _scheduleUnifiedPrayerTimeNotifications(ref);
  });

  // Context7 MCP: Listen to unified notification settings for backward compatibility
  ref.listen(unifiedNotificationSettingsProvider, (previous, next) {
    AppLogger.debug('NotificationSchedulerProvider: Unified settings changed - delegating to unified system');

    // Context7 MCP: Handle settings changes through unified system
    if (next.hasValue) {
      final settings = next.value!;
      if (!settings.generalSettings.globallyEnabled && previous?.value?.generalSettings.globallyEnabled == true) {
        AppLogger.debug('Notifications disabled - delegating cancellation to unified system');
        _cancelUnifiedNotifications(ref);
        return;
      }

      if (settings.generalSettings.globallyEnabled) {
        AppLogger.debug('Notifications enabled - delegating scheduling to unified system');
        _scheduleUnifiedPrayerTimeNotifications(ref);
      }
    }
  });
}

/// Context7 MCP: Schedule prayer time notifications through unified system
///
/// This function delegates to the unified notification manager for consistent
/// notification scheduling following Context7 MCP best practices.
void _scheduleUnifiedPrayerTimeNotifications(Ref ref) {
  try {
    AppLogger.debug('Delegating prayer time notification scheduling to unified system');

    // Context7 MCP: Use unified notification manager for scheduling
    final notificationManager = ref.read(unifiedNotificationManagerProvider.notifier);
    final location = ref.read(userLocationProvider);

    // Context7 MCP: Schedule through unified manager
    notificationManager.schedulePrayerNotifications(
      date: DateTime.now(),
      latitude: location.latitude,
      longitude: location.longitude,
    );

    AppLogger.debug('Prayer time notifications scheduled through unified system');
  } on Exception catch (e) {
    AppLogger.error('Error scheduling unified prayer time notifications: $e');
    // Context7 MCP: Continue without crashing - notifications can be scheduled later
  }
}

/// Context7 MCP: Cancel notifications through unified system
///
/// This function delegates to the unified notification manager for consistent
/// notification cancellation following Context7 MCP best practices.
void _cancelUnifiedNotifications(Ref ref) {
  try {
    AppLogger.debug('Delegating notification cancellation to unified system');

    // Context7 MCP: Use unified notification manager for cancellation
    final notificationManager = ref.read(unifiedNotificationManagerProvider.notifier);

    // Context7 MCP: Cancel through unified manager
    notificationManager.cancelAllNotifications();

    AppLogger.debug('Notifications cancelled through unified system');
  } on Exception catch (e) {
    AppLogger.error('Error cancelling unified notifications: $e');
    // Context7 MCP: Continue without crashing
  }
}
