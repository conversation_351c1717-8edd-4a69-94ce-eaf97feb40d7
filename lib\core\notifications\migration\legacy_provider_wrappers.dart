import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'deprecation_warning_system.dart';

part 'legacy_provider_wrappers.g.dart';

/// Legacy Provider Wrappers
///
/// **Task 4.1.2: Create deprecation warnings for old provider usage**
///
/// This file provides legacy provider wrappers that show deprecation warnings
/// while maintaining backward compatibility following Context7 MCP patterns.
///
/// Features:
/// - Automatic deprecation warnings on provider access
/// - Backward compatible API surface
/// - Migration guidance and code examples
/// - Severity-based warning levels
/// - Stack trace tracking for debugging
/// - Analytics integration for migration tracking
/// - Comprehensive documentation links
/// - Developer-friendly error messages

/// Legacy Prayer Notification Settings
@Deprecated('Use UnifiedNotificationSettings instead')
class LegacyPrayerNotificationSettings {
  final bool enabled;
  final bool fajrEnabled;
  final bool dhuhrEnabled;
  final bool asrEnabled;
  final bool maghribEnabled;
  final bool ishaEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final int reminderMinutes;

  const LegacyPrayerNotificationSettings({
    required this.enabled,
    required this.fajrEnabled,
    required this.dhuhrEnabled,
    required this.asrEnabled,
    required this.maghribEnabled,
    required this.ishaEnabled,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.reminderMinutes,
  });

  factory LegacyPrayerNotificationSettings.initial() {
    return const LegacyPrayerNotificationSettings(
      enabled: true,
      fajrEnabled: true,
      dhuhrEnabled: true,
      asrEnabled: true,
      maghribEnabled: true,
      ishaEnabled: true,
      soundEnabled: true,
      vibrationEnabled: true,
      reminderMinutes: 5,
    );
  }

  LegacyPrayerNotificationSettings copyWith({
    bool? enabled,
    bool? fajrEnabled,
    bool? dhuhrEnabled,
    bool? asrEnabled,
    bool? maghribEnabled,
    bool? ishaEnabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    int? reminderMinutes,
  }) {
    return LegacyPrayerNotificationSettings(
      enabled: enabled ?? this.enabled,
      fajrEnabled: fajrEnabled ?? this.fajrEnabled,
      dhuhrEnabled: dhuhrEnabled ?? this.dhuhrEnabled,
      asrEnabled: asrEnabled ?? this.asrEnabled,
      maghribEnabled: maghribEnabled ?? this.maghribEnabled,
      ishaEnabled: ishaEnabled ?? this.ishaEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      reminderMinutes: reminderMinutes ?? this.reminderMinutes,
    );
  }
}

/// Legacy Prayer Notification Provider
///
/// **Context7 MCP Migration Pattern:**
/// - Shows deprecation warnings on access
/// - Maintains backward compatibility
/// - Provides migration guidance
/// - Tracks usage for analytics
/// - Supports gradual migration
///
/// **Usage (Deprecated):**
/// ```dart
/// final settings = ref.watch(legacyPrayerNotificationProvider);
/// await ref.read(legacyPrayerNotificationProvider.notifier).enableNotifications();
/// ```
///
/// **Migration Path:**
/// ```dart
/// // Old (deprecated)
/// final settings = ref.watch(legacyPrayerNotificationProvider);
/// 
/// // New (recommended)
/// final settings = ref.watch(unifiedNotificationSettingsProvider);
/// ```
@Deprecated('Use unifiedNotificationSettingsProvider instead. This provider will be removed in v2.0.0')
@riverpod
class LegacyPrayerNotification extends _$LegacyPrayerNotification {
  @override
  LegacyPrayerNotificationSettings build() {
    // Show deprecation warning when provider is accessed
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyPrayerNotificationProvider',
      'build',
      severity: DeprecationSeverity.high,
      stackTrace: StackTrace.current,
      metadata: {
        'accessedAt': DateTime.now().toIso8601String(),
        'migrationGuide': 'https://docs.masajid-albahrain.com/migration/prayer-notifications',
      },
    );

    return LegacyPrayerNotificationSettings.initial();
  }

  /// Enable notifications (legacy method)
  @Deprecated('Use UnifiedNotificationSettingsNotifier.updateGlobalSettings instead')
  Future<void> enableNotifications() async {
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyPrayerNotificationProvider',
      'enableNotifications',
      severity: DeprecationSeverity.high,
      stackTrace: StackTrace.current,
      customMessage: '''
legacyPrayerNotificationProvider.enableNotifications() is deprecated.

Migration Guide:
Replace with: ref.read(unifiedNotificationSettingsProvider.notifier).updateGlobalSettings(globallyEnabled: true)

This method will be removed in v2.0.0.
See: https://docs.masajid-albahrain.com/migration/prayer-notifications
''',
    );
    
    // Simulate legacy behavior by updating state
    state = state.copyWith(enabled: true);
  }

  /// Disable notifications (legacy method)
  @Deprecated('Use UnifiedNotificationSettingsNotifier.updateGlobalSettings instead')
  Future<void> disableNotifications() async {
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyPrayerNotificationProvider',
      'disableNotifications',
      severity: DeprecationSeverity.high,
      stackTrace: StackTrace.current,
      customMessage: '''
legacyPrayerNotificationProvider.disableNotifications() is deprecated.

Migration Guide:
Replace with: ref.read(unifiedNotificationSettingsProvider.notifier).updateGlobalSettings(globallyEnabled: false)

This method will be removed in v2.0.0.
See: https://docs.masajid-albahrain.com/migration/prayer-notifications
''',
    );
    
    // Simulate legacy behavior by updating state
    state = state.copyWith(enabled: false);
  }

  /// Set prayer notification (legacy method)
  @Deprecated('Use UnifiedNotificationSettingsNotifier.updatePrayerSettings instead')
  Future<void> setPrayerNotification(String prayer, bool enabled) async {
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyPrayerNotificationProvider',
      'setPrayerNotification',
      severity: DeprecationSeverity.high,
      stackTrace: StackTrace.current,
      metadata: {'prayer': prayer, 'enabled': enabled},
      customMessage: '''
legacyPrayerNotificationProvider.setPrayerNotification() is deprecated.

Migration Guide:
Replace with: ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(PrayerType.$prayer, enabled: $enabled)

This method will be removed in v2.0.0.
See: https://docs.masajid-albahrain.com/migration/prayer-settings
''',
    );
    
    // Simulate legacy behavior by updating specific prayer setting
    switch (prayer.toLowerCase()) {
      case 'fajr':
        state = state.copyWith(fajrEnabled: enabled);
        break;
      case 'dhuhr':
      case 'zuhr':
        state = state.copyWith(dhuhrEnabled: enabled);
        break;
      case 'asr':
        state = state.copyWith(asrEnabled: enabled);
        break;
      case 'maghrib':
        state = state.copyWith(maghribEnabled: enabled);
        break;
      case 'isha':
        state = state.copyWith(ishaEnabled: enabled);
        break;
    }
  }

  /// Set sound enabled (legacy method)
  @Deprecated('Use UnifiedNotificationSettingsNotifier.updateSoundSettings instead')
  Future<void> setSoundEnabled(bool enabled) async {
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyPrayerNotificationProvider',
      'setSoundEnabled',
      severity: DeprecationSeverity.medium,
      stackTrace: StackTrace.current,
      metadata: {'enabled': enabled},
      customMessage: '''
legacyPrayerNotificationProvider.setSoundEnabled() is deprecated.

Migration Guide:
Replace with: ref.read(unifiedNotificationSettingsProvider.notifier).updateSoundSettings(enabled: $enabled)

This method will be removed in v2.0.0.
See: https://docs.masajid-albahrain.com/migration/sound-settings
''',
    );
    
    state = state.copyWith(soundEnabled: enabled);
  }

  /// Set vibration enabled (legacy method)
  @Deprecated('Use UnifiedNotificationSettingsNotifier.updateVibrationSettings instead')
  Future<void> setVibrationEnabled(bool enabled) async {
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyPrayerNotificationProvider',
      'setVibrationEnabled',
      severity: DeprecationSeverity.medium,
      stackTrace: StackTrace.current,
      metadata: {'enabled': enabled},
      customMessage: '''
legacyPrayerNotificationProvider.setVibrationEnabled() is deprecated.

Migration Guide:
Replace with: ref.read(unifiedNotificationSettingsProvider.notifier).updateVibrationSettings(enabled: $enabled)

This method will be removed in v2.0.0.
See: https://docs.masajid-albahrain.com/migration/vibration-settings
''',
    );
    
    state = state.copyWith(vibrationEnabled: enabled);
  }

  /// Set reminder minutes (legacy method)
  @Deprecated('Use UnifiedNotificationSettingsNotifier.updateReminderSettings instead')
  Future<void> setReminderMinutes(int minutes) async {
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyPrayerNotificationProvider',
      'setReminderMinutes',
      severity: DeprecationSeverity.medium,
      stackTrace: StackTrace.current,
      metadata: {'minutes': minutes},
      customMessage: '''
legacyPrayerNotificationProvider.setReminderMinutes() is deprecated.

Migration Guide:
Replace with: ref.read(unifiedNotificationSettingsProvider.notifier).updateReminderSettings(defaultMinutesBefore: $minutes)

This method will be removed in v2.0.0.
See: https://docs.masajid-albahrain.com/migration/reminder-settings
''',
    );
    
    state = state.copyWith(reminderMinutes: minutes);
  }
}

/// Legacy Community Notification Settings
@Deprecated('Use UnifiedNotificationSettings instead')
class LegacyCommunityNotificationSettings {
  final bool enabled;
  final bool announcementsEnabled;
  final bool eventsEnabled;
  final bool newsEnabled;
  final bool soundEnabled;

  const LegacyCommunityNotificationSettings({
    required this.enabled,
    required this.announcementsEnabled,
    required this.eventsEnabled,
    required this.newsEnabled,
    required this.soundEnabled,
  });

  factory LegacyCommunityNotificationSettings.initial() {
    return const LegacyCommunityNotificationSettings(
      enabled: true,
      announcementsEnabled: true,
      eventsEnabled: true,
      newsEnabled: true,
      soundEnabled: true,
    );
  }

  LegacyCommunityNotificationSettings copyWith({
    bool? enabled,
    bool? announcementsEnabled,
    bool? eventsEnabled,
    bool? newsEnabled,
    bool? soundEnabled,
  }) {
    return LegacyCommunityNotificationSettings(
      enabled: enabled ?? this.enabled,
      announcementsEnabled: announcementsEnabled ?? this.announcementsEnabled,
      eventsEnabled: eventsEnabled ?? this.eventsEnabled,
      newsEnabled: newsEnabled ?? this.newsEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
    );
  }
}

/// Legacy Community Notification Provider
///
/// **Context7 MCP Migration Pattern:**
/// - Shows deprecation warnings on access
/// - Maintains backward compatibility
/// - Provides migration guidance
/// - Tracks usage for analytics
/// - Supports gradual migration
@Deprecated('Use unifiedNotificationSettingsProvider instead. This provider will be removed in v2.0.0')
@riverpod
class LegacyCommunityNotification extends _$LegacyCommunityNotification {
  @override
  LegacyCommunityNotificationSettings build() {
    // Show deprecation warning when provider is accessed
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyCommunityNotificationProvider',
      'build',
      severity: DeprecationSeverity.high,
      stackTrace: StackTrace.current,
      metadata: {
        'accessedAt': DateTime.now().toIso8601String(),
        'migrationGuide': 'https://docs.masajid-albahrain.com/migration/community-notifications',
      },
    );

    return LegacyCommunityNotificationSettings.initial();
  }

  /// Enable community notifications (legacy method)
  @Deprecated('Use UnifiedNotificationSettingsNotifier.updateCommunitySettings instead')
  Future<void> enableCommunityNotifications() async {
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyCommunityNotificationProvider',
      'enableCommunityNotifications',
      severity: DeprecationSeverity.high,
      stackTrace: StackTrace.current,
      customMessage: '''
legacyCommunityNotificationProvider.enableCommunityNotifications() is deprecated.

Migration Guide:
Replace with: ref.read(unifiedNotificationSettingsProvider.notifier).updateCommunitySettings(enabled: true)

This method will be removed in v2.0.0.
See: https://docs.masajid-albahrain.com/migration/community-notifications
''',
    );
    
    state = state.copyWith(enabled: true);
  }

  /// Disable community notifications (legacy method)
  @Deprecated('Use UnifiedNotificationSettingsNotifier.updateCommunitySettings instead')
  Future<void> disableCommunityNotifications() async {
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyCommunityNotificationProvider',
      'disableCommunityNotifications',
      severity: DeprecationSeverity.high,
      stackTrace: StackTrace.current,
      customMessage: '''
legacyCommunityNotificationProvider.disableCommunityNotifications() is deprecated.

Migration Guide:
Replace with: ref.read(unifiedNotificationSettingsProvider.notifier).updateCommunitySettings(enabled: false)

This method will be removed in v2.0.0.
See: https://docs.masajid-albahrain.com/migration/community-notifications
''',
    );
    
    state = state.copyWith(enabled: false);
  }

  /// Set announcements enabled (legacy method)
  @Deprecated('Use UnifiedNotificationSettingsNotifier.updateCommunitySettings instead')
  Future<void> setAnnouncementsEnabled(bool enabled) async {
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyCommunityNotificationProvider',
      'setAnnouncementsEnabled',
      severity: DeprecationSeverity.medium,
      stackTrace: StackTrace.current,
      metadata: {'enabled': enabled},
      customMessage: '''
legacyCommunityNotificationProvider.setAnnouncementsEnabled() is deprecated.

Migration Guide:
Replace with: ref.read(unifiedNotificationSettingsProvider.notifier).updateCommunitySettings(announcements: $enabled)

This method will be removed in v2.0.0.
See: https://docs.masajid-albahrain.com/migration/community-settings
''',
    );
    
    state = state.copyWith(announcementsEnabled: enabled);
  }

  /// Set events enabled (legacy method)
  @Deprecated('Use UnifiedNotificationSettingsNotifier.updateCommunitySettings instead')
  Future<void> setEventsEnabled(bool enabled) async {
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyCommunityNotificationProvider',
      'setEventsEnabled',
      severity: DeprecationSeverity.medium,
      stackTrace: StackTrace.current,
      metadata: {'enabled': enabled},
      customMessage: '''
legacyCommunityNotificationProvider.setEventsEnabled() is deprecated.

Migration Guide:
Replace with: ref.read(unifiedNotificationSettingsProvider.notifier).updateCommunitySettings(events: $enabled)

This method will be removed in v2.0.0.
See: https://docs.masajid-albahrain.com/migration/community-settings
''',
    );
    
    state = state.copyWith(eventsEnabled: enabled);
  }

  /// Set news enabled (legacy method)
  @Deprecated('Use UnifiedNotificationSettingsNotifier.updateCommunitySettings instead')
  Future<void> setNewsEnabled(bool enabled) async {
    DeprecationWarningSystem().showDeprecationWarning(
      'legacyCommunityNotificationProvider',
      'setNewsEnabled',
      severity: DeprecationSeverity.medium,
      stackTrace: StackTrace.current,
      metadata: {'enabled': enabled},
      customMessage: '''
legacyCommunityNotificationProvider.setNewsEnabled() is deprecated.

Migration Guide:
Replace with: ref.read(unifiedNotificationSettingsProvider.notifier).updateCommunitySettings(news: $enabled)

This method will be removed in v2.0.0.
See: https://docs.masajid-albahrain.com/migration/community-settings
''',
    );
    
    state = state.copyWith(newsEnabled: enabled);
  }
}
