// ignore_for_file: deprecated_member_use_from_same_package

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../logging/app_logger.dart';
import '../providers/unified_notification_provider.dart';
import 'backward_compatibility_providers.dart';
import 'progressive_deployment_provider.dart';

part 'provider_router.g.dart';

/// Provider Router for Notification System Migration
///
/// **Context7 MCP Implementation:**
/// - Intelligently routes between unified and legacy providers
/// - Implements transparent fallback mechanisms
/// - Provides consistent API regardless of underlying provider
/// - Monitors routing decisions for analytics
/// - Supports A/B testing and gradual rollout
///
/// **Routing Strategy:**
/// - Check deployment state and feature flags
/// - Route to unified provider if enabled and healthy
/// - Fallback to legacy provider with compatibility layer
/// - Log routing decisions for monitoring
/// - Handle errors gracefully with automatic fallback
///
/// **Usage:**
/// ```dart
/// // Transparent routing - consumers don't need to know which provider is used
/// final settings = ref.watch(routedNotificationSettingsProvider);
/// final manager = ref.watch(routedNotificationManagerProvider);
/// ```
@riverpod
class RoutedNotificationSettings extends _$RoutedNotificationSettings {
  @override
  Future<UnifiedNotificationSettings> build() async {
    try {
      // Get deployment state
      final deployment = await ref.watch(progressiveDeploymentProvider.future);

      if (deployment.unifiedProviderEnabled && deployment.healthStatus != DeploymentHealthStatus.critical) {
        AppLogger.debug('ProviderRouter: Routing to unified notification settings');

        // Route to unified provider
        final unifiedSettings = await ref.watch(unifiedNotificationSettingsProvider.future);

        _logRoutingDecision('unified', 'settings', success: true);
        return unifiedSettings;
      } else {
        AppLogger.debug('ProviderRouter: Routing to legacy notification settings with compatibility layer');

        // Route to legacy provider with compatibility layer
        final legacySettings = ref.watch(legacyPrayerNotificationProvider);

        // Convert legacy settings to unified format
        final unifiedSettings = _convertLegacyToUnified(legacySettings);

        _logRoutingDecision('legacy', 'settings', success: true);
        return unifiedSettings;
      }
    } on Exception catch (e) {
      AppLogger.error('ProviderRouter: Settings routing failed: $e');

      // Emergency fallback to legacy provider
      try {
        final legacySettings = ref.watch(legacyPrayerNotificationProvider);
        final unifiedSettings = _convertLegacyToUnified(legacySettings);

        _logRoutingDecision('legacy', 'settings', success: false, error: e.toString());
        return unifiedSettings;
      } on Exception catch (fallbackError) {
        AppLogger.error('ProviderRouter: Emergency fallback failed: $fallbackError');

        // Return safe defaults
        _logRoutingDecision('default', 'settings', success: false, error: fallbackError.toString());
        return UnifiedNotificationSettings.defaultSettings();
      }
    }
  }

  /// Convert legacy settings to unified format
  ///
  /// Context7 MCP: This method is deprecated and will be removed in the next version.
  /// The provider router now delegates directly to the unified notification provider
  /// instead of creating intermediate conversion objects.
  @Deprecated('Use direct delegation to unified provider instead')
  LegacyPrayerNotificationSettings _convertLegacyToUnified(LegacyPrayerNotificationSettings legacy) {
    // Context7 MCP: Return the legacy settings as-is since this is a compatibility layer
    // The actual conversion happens in the unified notification provider
    return legacy;
  }

  /// Log routing decision for monitoring
  void _logRoutingDecision(String provider, String component, {required bool success, String? error}) {
    AppLogger.info(
      'ProviderRouter: Routing decision',
      context: {
        'provider': provider,
        'component': component,
        'success': success,
        'error': error,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
}

/// Routed Notification Manager Provider
@riverpod
class RoutedNotificationManager extends _$RoutedNotificationManager {
  @override
  Future<NotificationManagerInterface> build() async {
    try {
      // Get deployment state
      final deployment = await ref.watch(progressiveDeploymentProvider.future);

      if (deployment.unifiedProviderEnabled && deployment.healthStatus != DeploymentHealthStatus.critical) {
        AppLogger.debug('ProviderRouter: Routing to unified notification manager');

        // Route to unified manager
        final unifiedManager = ref.watch(unifiedNotificationManagerProvider.notifier);

        _logRoutingDecision('unified', 'manager', success: true);
        return UnifiedNotificationManagerAdapter(unifiedManager);
      } else {
        AppLogger.debug('ProviderRouter: Routing to legacy notification manager with compatibility layer');

        // Route to legacy manager with compatibility layer
        final legacyManager = LegacyNotificationManagerAdapter(ref);

        _logRoutingDecision('legacy', 'manager', success: true);
        return legacyManager;
      }
    } on Exception catch (e) {
      AppLogger.error('ProviderRouter: Manager routing failed: $e');

      // Emergency fallback to legacy manager
      try {
        final legacyManager = LegacyNotificationManagerAdapter(ref);

        _logRoutingDecision('legacy', 'manager', success: false, error: e.toString());
        return legacyManager;
      } on Exception catch (fallbackError) {
        AppLogger.error('ProviderRouter: Emergency fallback failed: $fallbackError');

        // Return no-op manager
        _logRoutingDecision('noop', 'manager', success: false, error: fallbackError.toString());
        return NoOpNotificationManager();
      }
    }
  }

  /// Log routing decision for monitoring
  void _logRoutingDecision(String provider, String component, {required bool success, String? error}) {
    AppLogger.info(
      'ProviderRouter: Routing decision',
      context: {
        'provider': provider,
        'component': component,
        'success': success,
        'error': error,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
}

/// Notification Manager Interface for consistent API
abstract class NotificationManagerInterface {
  Future<void> schedulePrayerNotifications({
    required DateTime date,
    required double latitude,
    required double longitude,
  });

  Future<void> cancelAllNotifications();
  Future<void> validateConfiguration();
  Future<List<PendingNotificationRequest>> getPendingNotifications();
}

/// Unified Notification Manager Adapter
class UnifiedNotificationManagerAdapter implements NotificationManagerInterface {
  final UnifiedNotificationManager _manager;

  UnifiedNotificationManagerAdapter(this._manager);

  @override
  Future<void> schedulePrayerNotifications({
    required DateTime date,
    required double latitude,
    required double longitude,
  }) async {
    return _manager.schedulePrayerNotifications(date: date, latitude: latitude, longitude: longitude);
  }

  @override
  Future<void> cancelAllNotifications() async {
    return _manager.cancelAllNotifications();
  }

  @override
  Future<void> validateConfiguration() async {
    return _manager.validateConfiguration();
  }

  @override
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return _manager.getPendingNotifications();
  }
}

/// Legacy Notification Manager Adapter
class LegacyNotificationManagerAdapter implements NotificationManagerInterface {
  final Ref _ref;

  LegacyNotificationManagerAdapter(this._ref);

  @override
  Future<void> schedulePrayerNotifications({
    required DateTime date,
    required double latitude,
    required double longitude,
  }) async {
    // Delegate to legacy prayer notification provider
    final legacyNotifier = _ref.read(legacyPrayerNotificationProvider.notifier);
    await legacyNotifier.scheduleNotifications(date, latitude, longitude);
  }

  @override
  Future<void> cancelAllNotifications() async {
    final legacyNotifier = _ref.read(legacyPrayerNotificationProvider.notifier);
    await legacyNotifier.cancelAllNotifications();
  }

  @override
  Future<void> validateConfiguration() async {
    // Legacy providers don't have validation - return success
    return;
  }

  @override
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    // Return empty list for legacy providers
    return [];
  }
}

/// No-Op Notification Manager for emergency fallback
class NoOpNotificationManager implements NotificationManagerInterface {
  @override
  Future<void> schedulePrayerNotifications({
    required DateTime date,
    required double latitude,
    required double longitude,
  }) async {
    AppLogger.warning('NoOpNotificationManager: schedulePrayerNotifications called');
  }

  @override
  Future<void> cancelAllNotifications() async {
    AppLogger.warning('NoOpNotificationManager: cancelAllNotifications called');
  }

  @override
  Future<void> validateConfiguration() async {
    AppLogger.warning('NoOpNotificationManager: validateConfiguration called');
  }

  @override
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    AppLogger.warning('NoOpNotificationManager: getPendingNotifications called');
    return [];
  }
}

// Placeholder types for compilation
class PendingNotificationRequest {
  final int id;
  final String title;
  final String body;
  final DateTime scheduledDate;

  const PendingNotificationRequest({
    required this.id,
    required this.title,
    required this.body,
    required this.scheduledDate,
  });
}
