import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../logging/app_logger.dart';
import 'feature_flag_system.dart';

part 'deployment_config.g.dart';

/// Deployment Configuration for Phase 1
///
/// **Context7 MCP Implementation:**
/// - Manages deployment configuration for progressive rollout
/// - Provides feature flag integration for gradual enablement
/// - Supports emergency rollback capabilities
/// - Monitors deployment health and performance
/// - Enables A/B testing and canary deployments
///
/// **Phase 1 Strategy:**
/// - Deploy unified providers alongside existing ones
/// - Start with 5% rollout to minimize risk
/// - Monitor performance and error rates
/// - Maintain backward compatibility
/// - Enable emergency rollback if needed
@riverpod
Future<DeploymentConfig> deploymentConfig(Ref ref) async {
  try {
    AppLogger.info('DeploymentConfig: Initializing Phase 1 deployment configuration');

    // Initialize feature flag service
    final featureFlagService = FeatureFlagService();
    await featureFlagService.initialize();

    // Check feature flags
    final unifiedEnabled = await featureFlagService.isEnabled(MigrationFeatureFlags.unifiedProviderEnabled);

    final emergencyRollback = await featureFlagService.isEnabled(MigrationFeatureFlags.emergencyRollbackEnabled);

    final migrationValidation = await featureFlagService.isEnabled(MigrationFeatureFlags.validationEnabled);

    // Create deployment configuration
    final config = DeploymentConfig(
      phase: DeploymentPhase.phase1,
      unifiedProviderEnabled: unifiedEnabled && !emergencyRollback,
      legacyProviderEnabled: true, // Always enabled in Phase 1
      compatibilityLayerEnabled: true, // Always enabled in Phase 1
      rolloutPercentage: unifiedEnabled ? 5 : 0,
      validationEnabled: migrationValidation,
      monitoringEnabled: true,
      emergencyRollbackEnabled: emergencyRollback,
      featureFlags: {
        MigrationFeatureFlags.unifiedProviderEnabled: unifiedEnabled,
        MigrationFeatureFlags.emergencyRollbackEnabled: emergencyRollback,
        MigrationFeatureFlags.validationEnabled: migrationValidation,
      },
      lastUpdated: DateTime.now(),
    );

    AppLogger.info(
      'DeploymentConfig: Configuration initialized',
      context: {
        'phase': config.phase.name,
        'unifiedEnabled': config.unifiedProviderEnabled,
        'rolloutPercentage': config.rolloutPercentage,
        'emergencyRollback': config.emergencyRollbackEnabled,
      },
    );

    return config;
  } on Exception catch (e, stackTrace) {
    AppLogger.error(
      'DeploymentConfig: Failed to initialize configuration',
      context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
    );

    // Return safe fallback configuration
    return DeploymentConfig(
      phase: DeploymentPhase.phase1,
      unifiedProviderEnabled: false,
      legacyProviderEnabled: true,
      compatibilityLayerEnabled: true,
      rolloutPercentage: 0,
      validationEnabled: true,
      monitoringEnabled: true,
      emergencyRollbackEnabled: true,
      featureFlags: {},
      lastUpdated: DateTime.now(),
      error: e.toString(),
    );
  }
}

/// Deployment Configuration
class DeploymentConfig {
  final DeploymentPhase phase;
  final bool unifiedProviderEnabled;
  final bool legacyProviderEnabled;
  final bool compatibilityLayerEnabled;
  final int rolloutPercentage;
  final bool validationEnabled;
  final bool monitoringEnabled;
  final bool emergencyRollbackEnabled;
  final Map<String, bool> featureFlags;
  final DateTime lastUpdated;
  final String? error;

  const DeploymentConfig({
    required this.phase,
    required this.unifiedProviderEnabled,
    required this.legacyProviderEnabled,
    required this.compatibilityLayerEnabled,
    required this.rolloutPercentage,
    required this.validationEnabled,
    required this.monitoringEnabled,
    required this.emergencyRollbackEnabled,
    required this.featureFlags,
    required this.lastUpdated,
    this.error,
  });

  /// Check if unified provider should be used
  bool get shouldUseUnifiedProvider => unifiedProviderEnabled && !emergencyRollbackEnabled;

  /// Check if legacy provider should be used
  bool get shouldUseLegacyProvider => legacyProviderEnabled || emergencyRollbackEnabled;

  /// Check if compatibility layer should be active
  bool get shouldUseCompatibilityLayer =>
      compatibilityLayerEnabled && (unifiedProviderEnabled || legacyProviderEnabled);

  /// Get deployment status summary
  String get statusSummary {
    if (error != null) return 'Error: $error';
    if (emergencyRollbackEnabled) return 'Emergency Rollback Active';
    if (unifiedProviderEnabled) return 'Unified Provider Active ($rolloutPercentage%)';
    return 'Legacy Provider Only';
  }

  /// Copy with new values
  DeploymentConfig copyWith({
    DeploymentPhase? phase,
    bool? unifiedProviderEnabled,
    bool? legacyProviderEnabled,
    bool? compatibilityLayerEnabled,
    int? rolloutPercentage,
    bool? validationEnabled,
    bool? monitoringEnabled,
    bool? emergencyRollbackEnabled,
    Map<String, bool>? featureFlags,
    DateTime? lastUpdated,
    String? error,
  }) {
    return DeploymentConfig(
      phase: phase ?? this.phase,
      unifiedProviderEnabled: unifiedProviderEnabled ?? this.unifiedProviderEnabled,
      legacyProviderEnabled: legacyProviderEnabled ?? this.legacyProviderEnabled,
      compatibilityLayerEnabled: compatibilityLayerEnabled ?? this.compatibilityLayerEnabled,
      rolloutPercentage: rolloutPercentage ?? this.rolloutPercentage,
      validationEnabled: validationEnabled ?? this.validationEnabled,
      monitoringEnabled: monitoringEnabled ?? this.monitoringEnabled,
      emergencyRollbackEnabled: emergencyRollbackEnabled ?? this.emergencyRollbackEnabled,
      featureFlags: featureFlags ?? this.featureFlags,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      error: error ?? this.error,
    );
  }
}

/// Deployment Phase
enum DeploymentPhase {
  phase1, // Deploy alongside existing
  phase2, // Migrate critical paths
  phase3, // Update remaining dependencies
  phase4, // Remove deprecated providers
  phase5, // Cleanup and optimization
}

/// Deployment Configuration Manager
@riverpod
class DeploymentConfigManager extends _$DeploymentConfigManager {
  @override
  Future<DeploymentConfig> build() async {
    return ref.watch(deploymentConfigProvider.future);
  }

  /// Update rollout percentage
  Future<void> updateRolloutPercentage(int percentage) async {
    try {
      final featureFlagService = FeatureFlagService();

      // Update feature flag
      await featureFlagService.setOverride(MigrationFeatureFlags.unifiedProviderEnabled, percentage > 0);

      // Refresh configuration
      ref.invalidate(deploymentConfigProvider);

      AppLogger.info('DeploymentConfigManager: Updated rollout percentage', context: {'percentage': percentage});
    } catch (e, stackTrace) {
      AppLogger.error(
        'DeploymentConfigManager: Failed to update rollout',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Trigger emergency rollback
  Future<void> emergencyRollback({required String reason}) async {
    try {
      AppLogger.warning('DeploymentConfigManager: Emergency rollback triggered', context: {'reason': reason});

      final featureFlagService = FeatureFlagService();

      // Disable unified provider and enable emergency rollback
      await featureFlagService.setOverride(MigrationFeatureFlags.unifiedProviderEnabled, false);
      await featureFlagService.setOverride(MigrationFeatureFlags.emergencyRollbackEnabled, true);

      // Refresh configuration
      ref.invalidate(deploymentConfigProvider);

      AppLogger.error('DeploymentConfigManager: Emergency rollback completed');
    } catch (e, stackTrace) {
      AppLogger.error(
        'DeploymentConfigManager: Emergency rollback failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Clear emergency rollback
  Future<void> clearEmergencyRollback() async {
    try {
      final featureFlagService = FeatureFlagService();

      await featureFlagService.removeOverride(MigrationFeatureFlags.emergencyRollbackEnabled);

      // Refresh configuration
      ref.invalidate(deploymentConfigProvider);

      AppLogger.info('DeploymentConfigManager: Emergency rollback cleared');
    } catch (e, stackTrace) {
      AppLogger.error(
        'DeploymentConfigManager: Failed to clear emergency rollback',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Validate deployment configuration
  Future<bool> validateConfiguration() async {
    try {
      final config = await future;

      // Basic validation checks
      if (config.error != null) return false;
      if (config.rolloutPercentage < 0 || config.rolloutPercentage > 100) return false;
      if (!config.legacyProviderEnabled && !config.unifiedProviderEnabled) return false;

      return true;
    } on Exception catch (e, stackTrace) {
      AppLogger.error(
        'DeploymentConfigManager: Configuration validation failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      return false;
    }
  }
}
