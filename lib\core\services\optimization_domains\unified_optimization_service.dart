import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../error_handling/sentry_error_handler.dart';
import '../../interfaces/logger_interface.dart';
import '../../isolates/background_isolate_manager.dart';
import '../../isolates/isolate_task_types.dart';
import '../../logging/custom_log_types.dart';
import '../../logging/global_talker_provider.dart';
import '../../logging/talker_extensions.dart';
import '../../utils/disposable_resources.dart';
import '../../utils/memory_leak_prevention_utility.dart';
import 'base/base_optimization_domain.dart';
import 'config/unified_optimization_config.dart';
import 'coordinators/domain_coordinator.dart';
import 'interfaces/automation_optimization_domain_interface.dart';
import 'interfaces/background_activity_domain_interface.dart';
import 'interfaces/battery_optimization_domain_interface.dart';
import 'interfaces/memory_optimization_domain_interface.dart';
import 'interfaces/network_optimization_domain_interface.dart';
import 'interfaces/performance_optimization_domain_interface.dart';
import 'interfaces/unified_optimization_service_interface.dart' as service_interface;
import 'models/device_tier.dart';
import 'models/optimization_exceptions.dart';
import 'models/optimization_models.dart' as models;
import 'models/priority.dart';

/// Unified optimization service implementation following Context7 MCP clean architecture patterns.
///
/// This service consolidates all optimization functionality into a single, well-architected
/// service that maintains all functionality while improving maintainability and reducing
/// memory overhead.
///
/// The service follows Context7 MCP principles:
/// - Interface-based design for testability and flexibility
/// - Dependency injection for loose coupling
/// - Proper error handling with Result pattern
/// - Comprehensive logging and monitoring
/// - Resource lifecycle management
/// - Domain separation with coordinators
///
/// Architecture:
/// ```
/// UnifiedOptimizationService
/// ├── DomainCoordinator (manages domain lifecycle and communication)
/// ├── MemoryOptimizationDomain
/// ├── NetworkOptimizationDomain
/// ├── PerformanceOptimizationDomain
/// ├── BatteryOptimizationDomain
/// ├── BackgroundActivityDomain
/// └── AutomationOptimizationDomain
/// ```
///
/// Example usage:
/// ```dart
/// final service = UnifiedOptimizationService(
///   config: config,
///   memoryDomain: memoryDomain,
///   networkDomain: networkDomain,
///   // ... other domains
///   logger: logger,
/// );
///
/// await service.initialize();
///
/// // Use service through interface
/// final deviceTier = service.deviceTier;
/// final result = await service.makeOptimizedRequest(...);
/// ```
class UnifiedOptimizationService implements service_interface.UnifiedOptimizationServiceInterface {
  // ============================================================================
  // DEPENDENCIES AND CONFIGURATION
  // ============================================================================

  /// Service configuration
  final UnifiedOptimizationConfig _config;

  /// Domain coordinator for managing domain lifecycle and communication
  late final DomainCoordinator _domainCoordinator;

  /// Individual domain instances
  final MemoryOptimizationDomainInterface _memoryDomain;
  final NetworkOptimizationDomainInterface _networkDomain;
  final PerformanceOptimizationDomainInterface _performanceDomain;
  final BatteryOptimizationDomainInterface _batteryDomain;
  final BackgroundActivityDomainInterface _backgroundDomain;
  final AutomationOptimizationDomainInterface _automationDomain;

  /// Logger for service operations
  final LoggerInterface _logger;

  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  /// Whether the service has been initialized
  bool _isInitialized = false;

  /// Whether the service is currently initializing
  bool _isInitializing = false;

  /// Whether the service is currently disposing
  bool _isDisposing = false;

  /// Initialization completer for handling concurrent initialization attempts
  Completer<void>? _initializationCompleter;

  /// Disposal completer for handling concurrent disposal attempts
  Completer<void>? _disposalCompleter;

  /// Service start time for uptime tracking
  DateTime? _startTime;

  /// Last error that occurred during service operations
  Exception? _lastError;

  // ============================================================================
  // CONTEXT7 MCP: BACKGROUND PROCESSING INFRASTRUCTURE
  // ============================================================================

  /// Background isolate manager for heavy operations
  BackgroundIsolateManager? _isolateManager;

  /// Resource management for proper disposal
  final List<Disposable> _managedResources = [];

  /// Service identifier for memory leak tracking
  late final String _serviceId;

  /// Talker instance for structured logging
  late final AppTalkerProvider _talkerProvider;

  /// Gets the last error that occurred during service operations
  ///
  /// This is useful for debugging and monitoring service health.
  /// Returns null if no error has occurred since the last successful operation.
  Exception? get lastError => _lastError;

  /// Service metrics for monitoring and debugging
  final Map<String, dynamic> _serviceMetrics = {};

  // ============================================================================
  // CONSTRUCTOR AND DEPENDENCY INJECTION
  // ============================================================================

  /// Creates a new unified optimization service instance.
  ///
  /// All dependencies are injected through the constructor following
  /// Context7 MCP dependency injection patterns.
  ///
  /// Parameters:
  /// - [config]: Unified configuration for all optimization domains
  /// - [memoryDomain]: Memory optimization domain implementation
  /// - [networkDomain]: Network optimization domain implementation
  /// - [performanceDomain]: Performance optimization domain implementation
  /// - [batteryDomain]: Battery optimization domain implementation
  /// - [backgroundDomain]: Background activity domain implementation
  /// - [automationDomain]: Automation optimization domain implementation
  /// - [logger]: Logger for service operations
  UnifiedOptimizationService({
    required UnifiedOptimizationConfig config,
    required MemoryOptimizationDomainInterface memoryDomain,
    required NetworkOptimizationDomainInterface networkDomain,
    required PerformanceOptimizationDomainInterface performanceDomain,
    required BatteryOptimizationDomainInterface batteryDomain,
    required BackgroundActivityDomainInterface backgroundDomain,
    required AutomationOptimizationDomainInterface automationDomain,
    required LoggerInterface logger,
  }) : _config = config,
       _memoryDomain = memoryDomain,
       _networkDomain = networkDomain,
       _performanceDomain = performanceDomain,
       _batteryDomain = batteryDomain,
       _backgroundDomain = backgroundDomain,
       _automationDomain = automationDomain,
       _logger = logger {
    // Initialize domain coordinator with all domains
    _domainCoordinator = DomainCoordinator(
      domains: {
        'memory': _memoryDomain as BaseOptimizationDomain,
        'network': _networkDomain as BaseOptimizationDomain,
        'performance': _performanceDomain as BaseOptimizationDomain,
        'battery': _batteryDomain as BaseOptimizationDomain,
        'background': _backgroundDomain as BaseOptimizationDomain,
        'automation': _automationDomain as BaseOptimizationDomain,
      },
      logger: _logger,
    );

    // Context7 MCP: Initialize background processing infrastructure
    _serviceId = 'UnifiedOptimizationService_$hashCode';
    _talkerProvider = AppTalkerProvider.instance;

    _logger.info('UnifiedOptimizationService: Instance created with ${_domainCoordinator.domainCount} domains');
  }

  // ============================================================================
  // SERVICE LIFECYCLE MANAGEMENT
  // ============================================================================

  /// Initializes the unified optimization service and all its domains.
  ///
  /// This method performs the following initialization steps:
  /// 1. Validates the service configuration
  /// 2. Initializes all optimization domains through the domain coordinator
  /// 3. Sets up inter-domain communication
  /// 4. Loads and applies configuration settings
  /// 5. Starts service monitoring
  ///
  /// Throws [InitializationException] if initialization fails.
  @override
  Future<void> initialize() async {
    // Handle concurrent initialization attempts
    if (_isInitialized) {
      _logger.debug('UnifiedOptimizationService: Already initialized');
      return;
    }

    if (_isInitializing) {
      _logger.debug('UnifiedOptimizationService: Initialization already in progress');
      return _initializationCompleter?.future ?? Future.value();
    }

    if (_isDisposing) {
      throw OptimizationException('Cannot initialize service while disposing');
    }

    _isInitializing = true;
    _initializationCompleter = Completer<void>();

    try {
      _logger.info('UnifiedOptimizationService: Starting initialization');
      _startTime = DateTime.now();

      // Validate configuration
      if (!_validateConfiguration()) {
        throw OptimizationException('Invalid service configuration');
      }

      // Initialize domain coordinator and all domains
      await _initializeDomains();

      // Set up inter-domain communication
      await _setupInterDomainCommunication();

      // Load and apply configuration
      await _loadConfiguration();

      // Start service monitoring
      _startServiceMonitoring();

      // Mark as initialized
      _isInitialized = true;
      _lastError = null; // Clear any previous errors on successful initialization
      _updateMetric('initialization_time_ms', DateTime.now().difference(_startTime!).inMilliseconds);

      _logger.info('UnifiedOptimizationService: Initialization completed successfully');
      _initializationCompleter!.complete();
    } on Exception catch (e) {
      _lastError = e;
      _logger.error('UnifiedOptimizationService: Initialization failed', e);
      _initializationCompleter!.completeError(e);
      rethrow;
    } finally {
      _isInitializing = false;
      _initializationCompleter = null;
    }
  }

  /// Disposes the unified optimization service and cleans up all resources.
  ///
  /// This method performs the following cleanup steps:
  /// 1. Stops service monitoring
  /// 2. Disposes all optimization domains through the domain coordinator
  /// 3. Clears service metrics and state
  ///
  /// Should be called when the service is no longer needed to prevent memory leaks.
  @override
  void dispose() {
    // Handle concurrent disposal attempts
    if (_isDisposing) {
      _logger.debug('UnifiedOptimizationService: Disposal already in progress');
      return;
    }

    if (!_isInitialized) {
      _logger.debug('UnifiedOptimizationService: Not initialized, nothing to dispose');
      return;
    }

    _isDisposing = true;
    _disposalCompleter = Completer<void>();

    try {
      _logger.info('UnifiedOptimizationService: Starting disposal');

      // Stop service monitoring
      _stopServiceMonitoring();

      // Context7 MCP: Dispose background processing infrastructure
      _disposeBackgroundProcessing();

      // Dispose domain coordinator and all domains
      _domainCoordinator.dispose();

      // Clear service state
      _isInitialized = false;
      _serviceMetrics.clear();

      _logger.info('UnifiedOptimizationService: Disposal completed successfully');
      _disposalCompleter!.complete();
    } on Exception catch (e) {
      _lastError = e;
      _logger.error('UnifiedOptimizationService: Disposal failed', e);
      _disposalCompleter!.completeError(e);
    } finally {
      _isDisposing = false;
      _disposalCompleter = null;
    }
  }

  /// Context7 MCP: Dispose background processing infrastructure
  void _disposeBackgroundProcessing() {
    try {
      // Context7 MCP: Dispose managed resources
      for (final resource in _managedResources) {
        try {
          resource.dispose();
        } catch (e) {
          _logger.warning('UnifiedOptimizationService: Failed to dispose resource: $e');
        }
      }
      _managedResources.clear();

      // Context7 MCP: Unregister from memory leak prevention
      MemoryLeakPreventionUtility().unregisterResource(_serviceId);

      // Context7 MCP: Structured logging for background processing disposal
      _talkerProvider.talker.logPerformance(
        'Background processing infrastructure disposed',
        operation: 'background_processing_dispose',
        metadata: {
          'service_id': _serviceId,
          'disposed_resources_count': _managedResources.length,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      _logger.info('UnifiedOptimizationService: Background processing infrastructure disposed');
    } catch (e, stackTrace) {
      // Context7 MCP: Error handling for disposal
      _talkerProvider.talker.logError(
        'Failed to dispose background processing infrastructure',
        error: e,
        metadata: {
          'service_id': _serviceId,
          'error_type': e.runtimeType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      _logger.error('UnifiedOptimizationService: Failed to dispose background processing infrastructure', e);
    }
  }

  // ============================================================================
  // PRIVATE INITIALIZATION METHODS
  // ============================================================================

  /// Validates the service configuration
  bool _validateConfiguration() {
    try {
      if (!_config.validate()) {
        _logger.error('UnifiedOptimizationService: Configuration validation failed');
        return false;
      }

      _logger.debug('UnifiedOptimizationService: Configuration validation passed');
      return true;
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Configuration validation error', e);
      return false;
    }
  }

  /// Initializes all domains through the domain coordinator
  Future<void> _initializeDomains() async {
    try {
      _logger.info('UnifiedOptimizationService: Initializing domains');

      // Context7 MCP: Initialize background isolate manager
      await _initializeBackgroundProcessing();

      await _domainCoordinator.initializeAllDomains();

      _updateMetric('initialized_domains', _domainCoordinator.initializedDomainCount);
      _logger.info('UnifiedOptimizationService: All domains initialized successfully');
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Domain initialization failed', e);
      rethrow;
    }
  }

  /// Context7 MCP: Initialize background processing infrastructure
  Future<void> _initializeBackgroundProcessing() async {
    try {
      // Context7 MCP: Register with memory leak prevention
      MemoryLeakPreventionUtility().registerResource(_serviceId, this);

      // Context7 MCP: Initialize background isolate manager
      _isolateManager = BackgroundIsolateManager.instance;
      await _isolateManager!.initialize();

      // Context7 MCP: Structured logging for background processing initialization
      _talkerProvider.talker.logPerformance(
        'Background processing infrastructure initialized',
        operation: 'background_processing_init',
        metadata: {
          'service_id': _serviceId,
          'isolate_manager_initialized': _isolateManager != null,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      _logger.info('UnifiedOptimizationService: Background processing infrastructure initialized');
    } catch (e, stackTrace) {
      // Context7 MCP: Comprehensive error handling
      await SentryErrorHandler.captureException(
        e,
        stackTrace: stackTrace,
        context: 'UnifiedOptimizationService.initializeBackgroundProcessing',
        metadata: {'service_id': _serviceId, 'operation': 'background_processing_init'},
      );

      _talkerProvider.talker.logError(
        'Failed to initialize background processing infrastructure',
        error: e,
        metadata: {
          'service_id': _serviceId,
          'error_type': e.runtimeType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      rethrow;
    }
  }

  /// Sets up communication channels between domains
  Future<void> _setupInterDomainCommunication() async {
    try {
      _logger.info('UnifiedOptimizationService: Setting up inter-domain communication');

      await _domainCoordinator.setupInterDomainCommunication();

      _logger.info('UnifiedOptimizationService: Inter-domain communication setup completed');
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Inter-domain communication setup failed', e);
      rethrow;
    }
  }

  /// Loads configuration and applies it to domains
  Future<void> _loadConfiguration() async {
    try {
      _logger.info('UnifiedOptimizationService: Loading configuration');

      // Apply configuration to domain coordinator
      await _domainCoordinator.applyConfiguration(_config);

      _logger.info('UnifiedOptimizationService: Configuration loaded and applied');
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Configuration loading failed', e);
      rethrow;
    }
  }

  /// Starts service monitoring and metrics collection
  void _startServiceMonitoring() {
    try {
      _logger.debug('UnifiedOptimizationService: Starting service monitoring');

      // Initialize service metrics
      _updateMetric('service_start_time', _startTime?.toIso8601String());
      _updateMetric('service_version', _config.version);
      _updateMetric('domain_count', _domainCoordinator.domainCount);

      _logger.debug('UnifiedOptimizationService: Service monitoring started');
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to start service monitoring', e);
      // Don't rethrow as this is not critical for service operation
    }
  }

  /// Stops service monitoring
  void _stopServiceMonitoring() {
    try {
      _logger.debug('UnifiedOptimizationService: Stopping service monitoring');

      // Update final metrics
      if (_startTime != null) {
        _updateMetric('service_uptime_ms', DateTime.now().difference(_startTime!).inMilliseconds);
      }

      _logger.debug('UnifiedOptimizationService: Service monitoring stopped');
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to stop service monitoring', e);
      // Don't rethrow as this is not critical
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /// Updates a service metric
  void _updateMetric(String key, dynamic value) {
    _serviceMetrics[key] = value;
  }

  /// Gets a service metric value with type safety
  ///
  /// Returns the metric value cast to type [T] if it exists and is of the correct type,
  /// otherwise returns null. This provides type-safe access to service metrics.
  ///
  /// Following Context7 MCP best practices for type-safe metric access.
  T? getMetric<T>(String key) {
    final value = _serviceMetrics[key];
    return value is T ? value : null;
  }

  /// Ensures the service is ready for operations
  void _ensureReady() {
    if (!_isInitialized) {
      throw OptimizationException('Service not initialized');
    }

    if (_isDisposing) {
      throw OptimizationException('Service is disposing');
    }
  }

  /// Executes an operation with error handling and logging
  Future<T> _executeOperation<T>(String operationName, Future<T> Function() operation, {T? fallbackValue}) async {
    try {
      _ensureReady();
      _logger.debug('UnifiedOptimizationService: Executing $operationName');

      final result = await operation();

      _logger.debug('UnifiedOptimizationService: $operationName completed successfully');
      return result;
    } on Exception catch (e) {
      _lastError = e;
      _logger.error('UnifiedOptimizationService: $operationName failed', e);

      if (fallbackValue != null) {
        _logger.debug('UnifiedOptimizationService: Using fallback value for $operationName');
        return fallbackValue;
      }

      rethrow;
    }
  }

  // ============================================================================
  // MEMORY OPTIMIZATION DOMAIN IMPLEMENTATION
  // ============================================================================

  /// Initializes the memory optimization domain
  Future<void> initializeMemoryOptimization() async {
    return _executeOperation('initializeMemoryOptimization', () => _memoryDomain.initialize());
  }

  /// Gets an optimization setting value by key.
  ///
  /// Parameters:
  /// - [key]: The setting key to retrieve
  ///
  /// Returns the setting value cast to type [T], or null if not found or cast fails.
  @override
  T? getOptimizationSetting<T>(String key) {
    try {
      _ensureReady();
      return _memoryDomain.getOptimizationSetting<T>(key);
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to get optimization setting $key', e);
      return null;
    }
  }

  /// Gets the current memory status and optimization information.
  ///
  /// Returns memory usage statistics, available memory, and optimization recommendations.
  @override
  Map<String, dynamic> getMemoryStatus() {
    try {
      _ensureReady();
      _logger.debug('UnifiedOptimizationService: Getting memory status');

      final result = _memoryDomain.getMemoryStatus();

      _logger.debug('UnifiedOptimizationService: Memory status retrieved successfully');
      return result;
    } on Exception catch (e) {
      _lastError = e;
      _logger.error('UnifiedOptimizationService: Failed to get memory status', e);
      return {};
    }
  }

  /// Gets the device tier classification for optimization decisions.
  ///
  /// Returns the device tier (low-end, mid-range, high-end, premium) or null if unavailable.
  @override
  DeviceTier? get deviceTier {
    try {
      _ensureReady();
      return _memoryDomain.deviceTier;
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to get device tier', e);
      return null;
    }
  }

  /// Checks if the current device is classified as low-end.
  ///
  /// Returns true if the device has limited resources and should use aggressive optimization.
  @override
  bool get isLowEndDevice {
    try {
      _ensureReady();
      return _memoryDomain.isLowEndDevice;
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to check if low-end device', e);
      return false;
    }
  }

  // ============================================================================
  // NETWORK OPTIMIZATION DOMAIN IMPLEMENTATION
  // ============================================================================

  @override
  Future<T> makeOptimizedRequest<T>({
    required String requestId,
    required Future<T> Function() networkCall,
    required T Function(Map<String, dynamic>) fromJson,
    Duration? cacheDuration,
    bool forceRefresh = false,
  }) async {
    return _executeOperation(
      'makeOptimizedRequest($requestId)',
      () => _networkDomain.makeOptimizedRequest<T>(
        requestId: requestId,
        networkCall: networkCall,
        fromJson: fromJson,
        cacheDuration: cacheDuration,
        forceRefresh: forceRefresh,
      ),
    );
  }

  @override
  Map<String, dynamic> getNetworkStats() {
    try {
      _ensureReady();
      return _networkDomain.getNetworkStats();
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to get network stats', e);
      return {};
    }
  }

  /// Clears the network cache to free memory
  Future<void> clearNetworkCache() async {
    return _executeOperation('clearNetworkCache', () => _networkDomain.clearCache());
  }

  // ============================================================================
  // PERFORMANCE OPTIMIZATION DOMAIN IMPLEMENTATION
  // ============================================================================

  @override
  Future<T> queueHeavyOperation<T>({
    required Future<T> Function() operation,
    required String operationName,
    Priority priority = Priority.normal,
    Duration? timeout,
  }) async {
    return _executeOperation(
      'queueHeavyOperation($operationName)',
      () => _performanceDomain.queueHeavyOperation<T>(
        operation: operation,
        operationName: operationName,
        priority: priority,
        timeout: timeout,
      ),
    );
  }

  @override
  Future<T> executeInIsolate<T>({required T Function() operation, required String operationName}) async {
    return _executeOperation(
      'executeInIsolate($operationName)',
      () => _performanceDomain.executeInIsolate<T>(operation: (params) => operation(), operationName: operationName),
    );
  }

  @override
  void trackFrameTime(double frameTimeMs) {
    try {
      _ensureReady();
      _performanceDomain.trackFrameTime(frameTimeMs);
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to track frame time', e);
    }
  }

  @override
  Map<String, dynamic> getPerformanceStats() {
    try {
      _ensureReady();
      return _performanceDomain.getPerformanceStats();
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to get performance stats', e);
      return {};
    }
  }

  // ============================================================================
  // CONTEXT7 MCP: BACKGROUND DATA PROCESSING IMPLEMENTATION
  // ============================================================================

  /// Context7 MCP: Execute heavy data transformations in background isolates
  ///
  /// This method moves CPU-intensive data processing operations to background isolates
  /// to prevent blocking the main UI thread, following Context7 MCP best practices.
  Future<T> executeDataTransformationInBackground<T>({
    required List<Map<String, dynamic>> data,
    required String transformationType,
    required String operationName,
    Map<String, dynamic>? options,
  }) async {
    if (_isolateManager == null) {
      throw OptimizationException('Background isolate manager not initialized');
    }

    final taskId = 'data_transform_${DateTime.now().millisecondsSinceEpoch}';

    // Context7 MCP: Structured logging for operation start
    _talkerProvider.talker.logPerformance(
      'Starting background data transformation',
      operation: 'data_transformation_start',
      metadata: {
        'task_id': taskId,
        'operation_name': operationName,
        'transformation_type': transformationType,
        'data_count': data.length,
        'service_id': _serviceId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    try {
      // Context7 MCP: Create data processing task
      final task = DataProcessingTask(
        id: taskId,
        data: data,
        processingOptions: {'transformation_type': transformationType, 'operation_name': operationName, ...?options},
        priority: TaskPriority.normal,
        timeout: const Duration(minutes: 2),
      );

      // Context7 MCP: Execute task in background isolate
      final result = await _isolateManager!.executeTask<T>(task);

      // Context7 MCP: Structured logging for operation completion
      _talkerProvider.talker.logPerformance(
        'Background data transformation completed',
        operation: 'data_transformation_complete',
        metadata: {
          'task_id': taskId,
          'operation_name': operationName,
          'transformation_type': transformationType,
          'service_id': _serviceId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return result;
    } catch (e, stackTrace) {
      // Context7 MCP: Comprehensive error handling with Sentry integration
      await SentryErrorHandler.captureException(
        e,
        stackTrace: stackTrace,
        context: 'UnifiedOptimizationService.executeDataTransformationInBackground',
        metadata: {
          'task_id': taskId,
          'operation_name': operationName,
          'transformation_type': transformationType,
          'data_count': data.length,
          'service_id': _serviceId,
        },
      );

      _talkerProvider.talker.logError(
        'Background data transformation failed',
        error: e,
        metadata: {
          'task_id': taskId,
          'operation_name': operationName,
          'transformation_type': transformationType,
          'error_type': e.runtimeType.toString(),
          'service_id': _serviceId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Context7 MCP: Graceful fallback to main thread processing
      _logger.warning('UnifiedOptimizationService: Falling back to main thread processing for $operationName');
      return await _executeDataTransformationOnMainThread<T>(
        data: data,
        transformationType: transformationType,
        operationName: operationName,
        options: options,
      );
    }
  }

  /// Context7 MCP: Fallback method for main thread data processing
  Future<T> _executeDataTransformationOnMainThread<T>({
    required List<Map<String, dynamic>> data,
    required String transformationType,
    required String operationName,
    Map<String, dynamic>? options,
  }) async {
    // Context7 MCP: Basic data transformation on main thread as fallback
    // This is a simplified implementation - in production, you would implement
    // specific transformation logic based on transformationType

    _talkerProvider.talker.logPerformance(
      'Executing data transformation on main thread (fallback)',
      operation: 'data_transformation_fallback',
      metadata: {
        'operation_name': operationName,
        'transformation_type': transformationType,
        'data_count': data.length,
        'service_id': _serviceId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    // Context7 MCP: Simulate data processing with yield control
    final processedData = <Map<String, dynamic>>[];
    for (int i = 0; i < data.length; i++) {
      final item = data[i];

      // Context7 MCP: Apply transformation based on type
      final processedItem = _applyTransformation(item, transformationType, options);
      processedData.add(processedItem);

      // Context7 MCP: Yield control periodically to prevent blocking
      if (i % 100 == 0) {
        await Future.delayed(Duration.zero);
      }
    }

    return processedData as T;
  }

  /// Context7 MCP: Apply specific data transformation
  Map<String, dynamic> _applyTransformation(
    Map<String, dynamic> item,
    String transformationType,
    Map<String, dynamic>? options,
  ) {
    switch (transformationType) {
      case 'json_optimization':
        return _optimizeJsonStructure(item);
      case 'data_aggregation':
        return _aggregateData(item, options);
      case 'data_filtering':
        return _filterData(item, options);
      case 'data_sorting':
        return _sortData(item, options);
      default:
        return Map<String, dynamic>.from(item);
    }
  }

  /// Context7 MCP: Optimize JSON structure for better performance
  Map<String, dynamic> _optimizeJsonStructure(Map<String, dynamic> item) {
    final optimized = <String, dynamic>{};

    // Context7 MCP: Remove null values and empty collections
    for (final entry in item.entries) {
      final value = entry.value;
      if (value != null) {
        if (value is List && value.isNotEmpty) {
          optimized[entry.key] = value;
        } else if (value is Map && value.isNotEmpty) {
          optimized[entry.key] = value;
        } else if (value is! List && value is! Map) {
          optimized[entry.key] = value;
        }
      }
    }

    return optimized;
  }

  /// Context7 MCP: Aggregate data based on options
  Map<String, dynamic> _aggregateData(Map<String, dynamic> item, Map<String, dynamic>? options) {
    // Context7 MCP: Basic aggregation implementation
    final aggregated = Map<String, dynamic>.from(item);

    if (options != null && options.containsKey('aggregate_fields')) {
      final fields = options['aggregate_fields'] as List<String>?;
      if (fields != null) {
        for (final field in fields) {
          if (aggregated.containsKey(field)) {
            final value = aggregated[field];
            if (value is num) {
              aggregated['${field}_aggregated'] = value;
            }
          }
        }
      }
    }

    return aggregated;
  }

  /// Context7 MCP: Filter data based on options
  Map<String, dynamic> _filterData(Map<String, dynamic> item, Map<String, dynamic>? options) {
    if (options == null || !options.containsKey('filter_fields')) {
      return item;
    }

    final filterFields = options['filter_fields'] as List<String>?;
    if (filterFields == null) return item;

    final filtered = <String, dynamic>{};
    for (final field in filterFields) {
      if (item.containsKey(field)) {
        filtered[field] = item[field];
      }
    }

    return filtered;
  }

  /// Context7 MCP: Sort data based on options
  Map<String, dynamic> _sortData(Map<String, dynamic> item, Map<String, dynamic>? options) {
    // Context7 MCP: Basic sorting implementation
    final sorted = <String, dynamic>{};
    final keys = item.keys.toList();

    if (options != null && options.containsKey('sort_ascending')) {
      final ascending = options['sort_ascending'] as bool? ?? true;
      keys.sort((a, b) => ascending ? a.compareTo(b) : b.compareTo(a));
    } else {
      keys.sort();
    }

    for (final key in keys) {
      sorted[key] = item[key];
    }

    return sorted;
  }

  /// Context7 MCP: Execute batch processing for large datasets in background isolates
  Future<List<T>> executeBatchProcessingInBackground<T>({
    required List<Map<String, dynamic>> dataset,
    required String operationName,
    int batchSize = 100,
    Map<String, dynamic>? processingOptions,
  }) async {
    if (_isolateManager == null) {
      throw OptimizationException('Background isolate manager not initialized');
    }

    final taskId = 'batch_process_${DateTime.now().millisecondsSinceEpoch}';

    // Context7 MCP: Structured logging for batch processing start
    _talkerProvider.talker.logPerformance(
      'Starting background batch processing',
      operation: 'batch_processing_start',
      metadata: {
        'task_id': taskId,
        'operation_name': operationName,
        'dataset_size': dataset.length,
        'batch_size': batchSize,
        'service_id': _serviceId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    try {
      final results = <T>[];
      final batches = <List<Map<String, dynamic>>>[];

      // Context7 MCP: Split dataset into batches
      for (int i = 0; i < dataset.length; i += batchSize) {
        final end = (i + batchSize < dataset.length) ? i + batchSize : dataset.length;
        batches.add(dataset.sublist(i, end));
      }

      // Context7 MCP: Process each batch in background isolate
      for (int batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        final batch = batches[batchIndex];
        final batchTaskId = '${taskId}_batch_$batchIndex';

        final task = DataProcessingTask(
          id: batchTaskId,
          data: batch,
          processingOptions: {
            'operation_name': operationName,
            'batch_index': batchIndex,
            'total_batches': batches.length,
            ...?processingOptions,
          },
          priority: TaskPriority.normal,
          timeout: const Duration(minutes: 1),
        );

        final batchResult = await _isolateManager!.executeTask<List<T>>(task);
        results.addAll(batchResult);

        // Context7 MCP: Log batch completion
        _talkerProvider.talker.logPerformance(
          'Batch processing completed',
          operation: 'batch_processing_batch_complete',
          metadata: {
            'batch_task_id': batchTaskId,
            'batch_index': batchIndex,
            'batch_size': batch.length,
            'total_batches': batches.length,
            'service_id': _serviceId,
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      }

      // Context7 MCP: Log overall completion
      _talkerProvider.talker.logPerformance(
        'Background batch processing completed',
        operation: 'batch_processing_complete',
        metadata: {
          'task_id': taskId,
          'operation_name': operationName,
          'total_processed': results.length,
          'total_batches': batches.length,
          'service_id': _serviceId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return results;
    } catch (e, stackTrace) {
      // Context7 MCP: Comprehensive error handling
      await SentryErrorHandler.captureException(
        e,
        stackTrace: stackTrace,
        context: 'UnifiedOptimizationService.executeBatchProcessingInBackground',
        metadata: {
          'task_id': taskId,
          'operation_name': operationName,
          'dataset_size': dataset.length,
          'batch_size': batchSize,
          'service_id': _serviceId,
        },
      );

      _talkerProvider.talker.logError(
        'Background batch processing failed',
        error: e,
        metadata: {
          'task_id': taskId,
          'operation_name': operationName,
          'error_type': e.runtimeType.toString(),
          'service_id': _serviceId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      rethrow;
    }
  }

  /// Context7 MCP: Optimize JSON parsing and serialization in background isolates
  Future<T> optimizeJsonProcessingInBackground<T>({
    required String jsonData,
    required String operationName,
    required String processingType, // 'parse', 'serialize', 'optimize'
    Map<String, dynamic>? options,
  }) async {
    if (_isolateManager == null) {
      throw OptimizationException('Background isolate manager not initialized');
    }

    final taskId = 'json_process_${DateTime.now().millisecondsSinceEpoch}';

    // Context7 MCP: Structured logging for JSON processing start
    _talkerProvider.talker.logPerformance(
      'Starting background JSON processing',
      operation: 'json_processing_start',
      metadata: {
        'task_id': taskId,
        'operation_name': operationName,
        'processing_type': processingType,
        'data_size_bytes': jsonData.length,
        'service_id': _serviceId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    try {
      // Context7 MCP: Create JSON parsing task
      final task = JsonParsingTask(
        id: taskId,
        jsonData: jsonData,
        parsingOptions: {'processing_type': processingType, 'operation_name': operationName, ...?options},
        priority: TaskPriority.normal,
        timeout: const Duration(minutes: 1),
      );

      // Context7 MCP: Execute task in background isolate
      final result = await _isolateManager!.executeTask<T>(task);

      // Context7 MCP: Structured logging for operation completion
      _talkerProvider.talker.logPerformance(
        'Background JSON processing completed',
        operation: 'json_processing_complete',
        metadata: {
          'task_id': taskId,
          'operation_name': operationName,
          'processing_type': processingType,
          'service_id': _serviceId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return result;
    } on Exception catch (e, stackTrace) {
      // Context7 MCP: Comprehensive error handling
      await SentryErrorHandler.captureException(
        e,
        stackTrace: stackTrace,
        context: 'UnifiedOptimizationService.optimizeJsonProcessingInBackground',
        metadata: {
          'task_id': taskId,
          'operation_name': operationName,
          'processing_type': processingType,
          'data_size_bytes': jsonData.length,
          'service_id': _serviceId,
        },
      );

      _talkerProvider.talker.logError(
        'Background JSON processing failed',
        error: e,
        metadata: {
          'task_id': taskId,
          'operation_name': operationName,
          'processing_type': processingType,
          'error_type': e.runtimeType.toString(),
          'service_id': _serviceId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Context7 MCP: Graceful fallback to main thread processing
      _logger.warning('UnifiedOptimizationService: Falling back to main thread JSON processing for $operationName');
      return await _executeJsonProcessingOnMainThread<T>(
        jsonData: jsonData,
        processingType: processingType,
        operationName: operationName,
        options: options,
      );
    }
  }

  /// Context7 MCP: Fallback method for main thread JSON processing
  Future<T> _executeJsonProcessingOnMainThread<T>({
    required String jsonData,
    required String processingType,
    required String operationName,
    Map<String, dynamic>? options,
  }) async {
    _talkerProvider.talker.logPerformance(
      'Executing JSON processing on main thread (fallback)',
      operation: 'json_processing_fallback',
      metadata: {
        'operation_name': operationName,
        'processing_type': processingType,
        'data_size_bytes': jsonData.length,
        'service_id': _serviceId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    // Context7 MCP: Basic JSON processing on main thread
    switch (processingType) {
      case 'parse':
        return jsonDecode(jsonData) as T;
      case 'serialize':
        return jsonEncode(jsonData) as T;
      case 'optimize':
        final parsed = jsonDecode(jsonData);
        final optimized = _optimizeJsonStructure(parsed as Map<String, dynamic>);
        return jsonEncode(optimized) as T;
      default:
        return jsonData as T;
    }
  }

  // ============================================================================
  // BATTERY OPTIMIZATION DOMAIN IMPLEMENTATION
  // ============================================================================

  @override
  Future<bool> isIgnoringBatteryOptimizations() async {
    return _executeOperation(
      'isIgnoringBatteryOptimizations',
      () => _batteryDomain.isIgnoringBatteryOptimizations(),
      fallbackValue: false,
    );
  }

  @override
  Future<bool> requestIgnoreBatteryOptimizations() async {
    return _executeOperation(
      'requestIgnoreBatteryOptimizations',
      () => _batteryDomain.requestIgnoreBatteryOptimizations(),
      fallbackValue: false,
    );
  }

  @override
  Future<void> showBatteryOptimizationDialog(BuildContext context) async {
    return _executeOperation('showBatteryOptimizationDialog', () async {
      _batteryDomain.showBatteryOptimizationDialog(context);
    });
  }

  // ============================================================================
  // BACKGROUND ACTIVITY DOMAIN IMPLEMENTATION
  // ============================================================================

  @override
  bool get isAppActive {
    try {
      _ensureReady();
      return _backgroundDomain.isAppActive;
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to check if app is active', e);
      return false;
    }
  }

  @override
  Future<bool> hasBeenUsedRecently({Duration? threshold}) async {
    return _executeOperation(
      'hasBeenUsedRecently',
      () => _backgroundDomain.hasBeenUsedRecently(threshold: threshold ?? const Duration(hours: 24)),
      fallbackValue: false,
    );
  }

  @override
  AppLifecycleState get currentLifecycleState {
    try {
      _ensureReady();
      return _backgroundDomain.currentLifecycleState;
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to get lifecycle state', e);
      return AppLifecycleState.resumed; // Safe default
    }
  }

  @override
  DateTime? get lastActiveTime {
    try {
      _ensureReady();
      return _backgroundDomain.lastActiveTime;
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to get last active time', e);
      return null;
    }
  }

  // ============================================================================
  // AUTOMATION OPTIMIZATION DOMAIN IMPLEMENTATION
  // ============================================================================

  @override
  void addOptimizationRule(models.OptimizationRule rule) {
    try {
      _ensureReady();
      // Rule is already in the correct domain format
      _automationDomain.addOptimizationRule(rule);
      _logger.info('UnifiedOptimizationService: Added optimization rule ${rule.id}');
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to add optimization rule ${rule.id}', e);
    }
  }

  @override
  Map<String, dynamic> getAutomationStats() {
    try {
      _ensureReady();
      return _automationDomain.getAutomationStats();
    } on Exception catch (e) {
      _logger.error('UnifiedOptimizationService: Failed to get automation stats', e);
      return {};
    }
  }

  @override
  Future<void> executeOptimizations() async {
    return _executeOperation('executeOptimizations', () => _automationDomain.executeOptimizations());
  }
}
