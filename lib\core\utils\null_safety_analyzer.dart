/// Null safety analyzer for identifying improvement opportunities
///
/// This utility analyzes code patterns and suggests improvements for better
/// null safety practices throughout the codebase.
library null_safety_analyzer;

import 'dart:io';

/// Analyzer for null safety patterns and improvements
class NullSafetyAnalyzer {
  static const String _analysisResultsFile = 'null_safety_analysis.md';

  /// Analysis results
  final List<NullSafetyIssue> issues = [];
  final List<NullSafetyImprovement> improvements = [];
  final Map<String, int> patternCounts = {};

  /// Analyze a file for null safety patterns
  Future<void> analyzeFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) return;

      final content = await file.readAsString();
      final lines = content.split('\n');

      for (int i = 0; i < lines.length; i++) {
        final line = lines[i];
        final lineNumber = i + 1;

        _analyzeLineForPatterns(filePath, lineNumber, line);
      }
    } on Exception catch (e) {
      print('Error analyzing file $filePath: $e');
    }
  }

  /// Analyze a single line for null safety patterns
  void _analyzeLineForPatterns(String filePath, int lineNumber, String line) {
    final trimmedLine = line.trim();

    // Skip comments and empty lines
    if (trimmedLine.startsWith('//') || trimmedLine.startsWith('/*') || trimmedLine.isEmpty) {
      return;
    }

    // Check for nullable fields that could be late
    _checkNullableFieldsForLate(filePath, lineNumber, trimmedLine);

    // Check for redundant null checks
    _checkRedundantNullChecks(filePath, lineNumber, trimmedLine);

    // Check for missing null assertions
    _checkMissingNullAssertions(filePath, lineNumber, trimmedLine);

    // Check for nullable parameters that could be required
    _checkNullableParameters(filePath, lineNumber, trimmedLine);

    // Check for unnecessary null-aware operators
    _checkUnnecessaryNullAware(filePath, lineNumber, trimmedLine);

    // Count patterns
    _countPatterns(trimmedLine);
  }

  /// Check for nullable fields that could be late
  void _checkNullableFieldsForLate(String filePath, int lineNumber, String line) {
    // Pattern: Type? fieldName;
    final nullableFieldPattern = RegExp(r'(\w+)\?\s+(\w+);');
    final match = nullableFieldPattern.firstMatch(line);

    if (match != null && !line.contains('final')) {
      final type = match.group(1);
      final fieldName = match.group(2);

      // Skip if it's likely meant to be nullable
      if (!_isLikelyMeantToBeNullable(fieldName!)) {
        improvements.add(
          NullSafetyImprovement(
            filePath: filePath,
            lineNumber: lineNumber,
            type: ImprovementType.nullableToLate,
            description: 'Consider using "late $type $fieldName" if this field is always initialized before use',
            currentCode: line,
            suggestedCode: line.replaceFirst('$type?', 'late $type'),
          ),
        );
      }
    }
  }

  /// Check for redundant null checks
  void _checkRedundantNullChecks(String filePath, int lineNumber, String line) {
    // Pattern: variable != null after previous null check
    if (line.contains('!= null') || line.contains('== null')) {
      issues.add(
        NullSafetyIssue(
          filePath: filePath,
          lineNumber: lineNumber,
          type: IssueType.potentialRedundantCheck,
          description: 'Potential redundant null check - verify if flow analysis makes this unnecessary',
          code: line,
        ),
      );
    }
  }

  /// Check for missing null assertions where they might be safe
  void _checkMissingNullAssertions(String filePath, int lineNumber, String line) {
    // Pattern: nullable_variable?.method() where ! might be safer
    final nullAwarePattern = RegExp(r'(\w+)\?\.');
    if (nullAwarePattern.hasMatch(line) && !line.contains('??')) {
      improvements.add(
        NullSafetyImprovement(
          filePath: filePath,
          lineNumber: lineNumber,
          type: ImprovementType.nullAwareToAssertion,
          description: 'Consider using ! instead of ?. if you know the value is never null',
          currentCode: line,
          suggestedCode: 'Review if null assertion (!) is more appropriate',
        ),
      );
    }
  }

  /// Check for nullable parameters that could be required
  void _checkNullableParameters(String filePath, int lineNumber, String line) {
    // Pattern: function parameters with nullable types
    if (line.contains('({') && line.contains('?')) {
      final paramPattern = RegExp(r'(\w+)\?\s+(\w+)[,}]');
      final matches = paramPattern.allMatches(line);

      for (final match in matches) {
        final type = match.group(1);
        final paramName = match.group(2);

        if (!_isLikelyMeantToBeNullable(paramName!)) {
          improvements.add(
            NullSafetyImprovement(
              filePath: filePath,
              lineNumber: lineNumber,
              type: ImprovementType.nullableToRequired,
              description: 'Consider making "$paramName" required if it should never be null',
              currentCode: line,
              suggestedCode: 'Change to "required $type $paramName"',
            ),
          );
        }
      }
    }
  }

  /// Check for unnecessary null-aware operators
  void _checkUnnecessaryNullAware(String filePath, int lineNumber, String line) {
    // This would require more sophisticated analysis
    // For now, just flag potential cases for manual review
    if (line.contains('?.') && line.contains('!')) {
      issues.add(
        NullSafetyIssue(
          filePath: filePath,
          lineNumber: lineNumber,
          type: IssueType.mixedNullHandling,
          description: 'Mixed null-aware and assertion operators - review for consistency',
          code: line,
        ),
      );
    }
  }

  /// Count various patterns for statistics
  void _countPatterns(String line) {
    if (line.contains('late ')) {
      patternCounts['late_variables'] = (patternCounts['late_variables'] ?? 0) + 1;
    }
    if (line.contains('?.')) {
      patternCounts['null_aware_access'] = (patternCounts['null_aware_access'] ?? 0) + 1;
    }
    if (line.contains('!')) {
      patternCounts['null_assertions'] = (patternCounts['null_assertions'] ?? 0) + 1;
    }
    if (line.contains('??')) {
      patternCounts['null_coalescing'] = (patternCounts['null_coalescing'] ?? 0) + 1;
    }
    if (line.contains('required ')) {
      patternCounts['required_params'] = (patternCounts['required_params'] ?? 0) + 1;
    }
  }

  /// Check if a field/parameter name suggests it's meant to be nullable
  bool _isLikelyMeantToBeNullable(String name) {
    final nullableIndicators = [
      'optional',
      'maybe',
      'fallback',
      'default',
      'cache',
      'temp',
      'avatar',
      'image',
      'url',
      'description',
      'note',
      'comment',
      'metadata',
      'extra',
      'additional',
      'custom',
    ];

    final lowerName = name.toLowerCase();
    return nullableIndicators.any((indicator) => lowerName.contains(indicator));
  }

  /// Generate analysis report
  Future<void> generateReport() async {
    final buffer = StringBuffer();

    buffer.writeln('# Null Safety Analysis Report');
    buffer.writeln();
    buffer.writeln('Generated on: ${DateTime.now()}');
    buffer.writeln();

    // Summary
    buffer.writeln('## Summary');
    buffer.writeln();
    buffer.writeln('- **Issues Found**: ${issues.length}');
    buffer.writeln('- **Improvements Suggested**: ${improvements.length}');
    buffer.writeln();

    // Pattern Statistics
    buffer.writeln('## Pattern Statistics');
    buffer.writeln();
    patternCounts.forEach((pattern, count) {
      buffer.writeln('- **${pattern.replaceAll('_', ' ').toUpperCase()}**: $count');
    });
    buffer.writeln();

    // Issues
    if (issues.isNotEmpty) {
      buffer.writeln('## Issues');
      buffer.writeln();
      for (final issue in issues) {
        buffer.writeln('### ${issue.type.name}');
        buffer.writeln();
        buffer.writeln('**File**: ${issue.filePath}:${issue.lineNumber}');
        buffer.writeln('**Description**: ${issue.description}');
        buffer.writeln('**Code**: `${issue.code}`');
        buffer.writeln();
      }
    }

    // Improvements
    if (improvements.isNotEmpty) {
      buffer.writeln('## Suggested Improvements');
      buffer.writeln();
      for (final improvement in improvements) {
        buffer.writeln('### ${improvement.type.name}');
        buffer.writeln();
        buffer.writeln('**File**: ${improvement.filePath}:${improvement.lineNumber}');
        buffer.writeln('**Description**: ${improvement.description}');
        buffer.writeln('**Current**: `${improvement.currentCode}`');
        buffer.writeln('**Suggested**: `${improvement.suggestedCode}`');
        buffer.writeln();
      }
    }

    // Write report to file
    final reportFile = File(_analysisResultsFile);
    await reportFile.writeAsString(buffer.toString());

    print('Null safety analysis complete. Report saved to $_analysisResultsFile');
    print('Found ${issues.length} issues and ${improvements.length} improvement opportunities.');
  }
}

/// Types of null safety issues
enum IssueType { potentialRedundantCheck, mixedNullHandling, unsafeNullAssertion }

/// Types of null safety improvements
enum ImprovementType { nullableToLate, nullableToRequired, nullAwareToAssertion, addNullCheck }

/// Represents a null safety issue
class NullSafetyIssue {
  final String filePath;
  final int lineNumber;
  final IssueType type;
  final String description;
  final String code;

  NullSafetyIssue({
    required this.filePath,
    required this.lineNumber,
    required this.type,
    required this.description,
    required this.code,
  });
}

/// Represents a null safety improvement opportunity
class NullSafetyImprovement {
  final String filePath;
  final int lineNumber;
  final ImprovementType type;
  final String description;
  final String currentCode;
  final String suggestedCode;

  NullSafetyImprovement({
    required this.filePath,
    required this.lineNumber,
    required this.type,
    required this.description,
    required this.currentCode,
    required this.suggestedCode,
  });
}
