import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/presentation/pages/forgot_password_page.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/signup_page.dart';
import '../../features/debug/screens/analytics_debug_screen.dart';
import '../../features/duas/presentation/pages/duas_page.dart';
import '../../features/duas/presentation/widgets/duas_layout.dart';
import '../../features/events/presentation/pages/events_page.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/home/<USER>/widgets/home_layout.dart';
import '../../features/maps/presentation/pages/full_map_page.dart';
import '../../features/masjids/presentation/pages/city_masjids_page.dart';
import '../../features/masjids/presentation/pages/instagram_test_page.dart';
import '../../features/masjids/presentation/pages/masjid_detail_page.dart';
import '../../features/masjids/presentation/pages/masjids_debug_page_fixed.dart';
import '../../features/masjids/presentation/pages/masjids_page.dart';
import '../../features/masjids/presentation/pages/supabase_test_page.dart';
import '../../features/masjids/presentation/widgets/masjids_layout.dart';
import '../../features/notifications/presentation/pages/notification_settings_page.dart';
import '../../features/prayer_times/presentation/pages/prayer_times_page.dart';
import '../../features/prayer_times/presentation/widgets/prayer_times_layout.dart';
import '../../features/qibla/presentation/pages/qibla_page.dart';
import '../../features/qibla/presentation/widgets/qibla_layout.dart';
import '../../features/settings/presentation/pages/about_page.dart';
import '../../features/settings/presentation/pages/contact_us_page.dart';
import '../../features/settings/presentation/pages/report_issue_page.dart';
import '../../generated/l10n/app_localizations.dart';
import '../../performance/performance_test_page.dart';
import '../auth/providers/unified_auth_provider.dart';
import '../constants/route_constants.dart';

/// Provider for the app router
final routerProvider = Provider<GoRouter>((ref) {
  return AppRouter(ref).router;
});

/// App router configuration
class AppRouter {
  /// Reference to the Riverpod container
  final Ref _ref;

  /// Creates an [AppRouter] with the given [_ref]
  AppRouter(this._ref);

  /// Get the configured router
  GoRouter get router {
    return GoRouter(
      initialLocation: RouteConstants.root,
      debugLogDiagnostics: true,
      routes: _routes,
      errorBuilder: (context, state) => const _ErrorScreen(),
      redirect: _guardRoutes,
      // Disable page transition animations
      routerNeglect: true,
    );
  }

  /// Route guard for authentication and initial setup
  String? _guardRoutes(BuildContext context, GoRouterState state) {
    // We no longer need to check for auth routes since we allow navigation to them

    // Get authentication status
    final isAuthenticated = _ref.read(isAuthenticatedProvider);

    // Check if the route requires authentication
    final requiresAuth =
        state.matchedLocation == RouteConstants.profile || state.matchedLocation == RouteConstants.favorites;

    // If not authenticated and trying to access routes that require auth, redirect to login
    if (!isAuthenticated && requiresAuth) {
      return RouteConstants.login;
    }

    // Allow navigation to login page even when authenticated
    return null;
  }

  /// Define app routes
  List<RouteBase> get _routes {
    return [
      GoRoute(path: RouteConstants.root, redirect: (_, _) => RouteConstants.home),
      // Main routes with bottom navigation
      GoRoute(
        path: RouteConstants.home,
        pageBuilder: (context, state) => const MaterialPage(child: HomeLayout(body: HomePage())),
      ),
      GoRoute(
        path: RouteConstants.masjids,
        pageBuilder: (context, state) => const MaterialPage(child: MasjidsLayout(body: MasjidsPage())),
      ),
      GoRoute(
        path: RouteConstants.prayerTimes,
        pageBuilder: (context, state) => const MaterialPage(child: PrayerTimesLayout(body: PrayerTimesPage())),
      ),
      GoRoute(
        path: RouteConstants.duas,
        pageBuilder: (context, state) => const MaterialPage(child: DuasLayout(body: DuasPage())),
      ),
      GoRoute(
        path: RouteConstants.qibla,
        pageBuilder: (context, state) => const MaterialPage(child: QiblaLayout(body: QiblaPage())),
      ),

      // Other routes without bottom navigation
      GoRoute(
        path: RouteConstants.initialSetup,
        pageBuilder: (context, state) => MaterialPage(
          child: Scaffold(
            backgroundColor: Theme.of(context).colorScheme.surface,
            body: Center(child: Text(AppLocalizations.of(context)?.initialSetupPageTitle ?? 'Initial Setup')),
          ),
        ),
      ),
      GoRoute(
        path: RouteConstants.login,
        pageBuilder: (context, state) => const MaterialPage(child: LoginPage()),
      ),
      GoRoute(
        path: RouteConstants.register,
        pageBuilder: (context, state) => const MaterialPage(child: SignupPage()),
      ),
      GoRoute(
        path: RouteConstants.forgotPassword,
        pageBuilder: (context, state) => const MaterialPage(child: ForgotPasswordPage()),
      ),
      GoRoute(
        path: RouteConstants.cityMasjids,
        pageBuilder: (context, state) =>
            MaterialPage(child: CityMasjidsPage(cityId: state.pathParameters[RouteConstants.cityIdParam] ?? '')),
      ),
      GoRoute(
        path: RouteConstants.masjidDetails,
        pageBuilder: (context, state) =>
            MaterialPage(child: MasjidDetailPage(masjidId: state.pathParameters[RouteConstants.idParam] ?? '')),
      ),
      GoRoute(
        path: RouteConstants.duaDetails,
        pageBuilder: (context, state) => MaterialPage(
          child: Scaffold(
            backgroundColor: Theme.of(context).colorScheme.surface,
            body: Center(child: Text('Dua Details: ${state.pathParameters[RouteConstants.idParam]}')),
          ),
        ),
      ),
      GoRoute(
        path: RouteConstants.fullMap,
        pageBuilder: (context, state) => const MaterialPage(child: FullMapPage()),
      ),
      GoRoute(
        path: RouteConstants.settings,
        pageBuilder: (context, state) => MaterialPage(
          child: Scaffold(
            backgroundColor: Theme.of(context).colorScheme.surface,
            body: Center(child: Text(AppLocalizations.of(context)?.settingsPageTitle ?? 'Settings')),
          ),
        ),
      ),
      GoRoute(
        path: RouteConstants.profile,
        pageBuilder: (context, state) => MaterialPage(
          child: Scaffold(
            backgroundColor: Theme.of(context).colorScheme.surface,
            body: Center(child: Text(AppLocalizations.of(context)?.profilePageTitle ?? 'Profile')),
          ),
        ),
      ),
      GoRoute(
        path: RouteConstants.favorites,
        pageBuilder: (context, state) => MaterialPage(
          child: Scaffold(
            backgroundColor: Theme.of(context).colorScheme.surface,
            body: Center(child: Text(AppLocalizations.of(context)?.favoritesPageTitle ?? 'Favorites')),
          ),
        ),
      ),
      GoRoute(
        path: RouteConstants.events,
        pageBuilder: (context, state) => const MaterialPage(child: EventsPage()),
      ),
      GoRoute(
        path: RouteConstants.adjustTiming,
        pageBuilder: (context, state) => MaterialPage(
          child: Scaffold(
            backgroundColor: Theme.of(context).colorScheme.surface,
            body: Center(child: Text(AppLocalizations.of(context)?.adjustTimingPageTitle ?? 'Adjust Timing')),
          ),
        ),
      ),
      GoRoute(
        path: RouteConstants.language,
        pageBuilder: (context, state) => MaterialPage(
          child: Scaffold(
            backgroundColor: Theme.of(context).colorScheme.surface,
            body: Center(child: Text(AppLocalizations.of(context)?.languagePageTitle ?? 'Language')),
          ),
        ),
      ),
      GoRoute(
        path: RouteConstants.about,
        pageBuilder: (context, state) => const MaterialPage(child: AboutPage()),
      ),
      GoRoute(
        path: RouteConstants.contact,
        pageBuilder: (context, state) => const MaterialPage(child: ContactUsPage()),
      ),
      GoRoute(
        path: RouteConstants.reportIssue,
        pageBuilder: (context, state) {
          debugPrint('🔍 ROUTER: ===== REPORT ISSUE ROUTE ACCESSED =====');
          debugPrint('🔍 ROUTER: state.path = ${state.path}');
          debugPrint('🔍 ROUTER: state.fullPath = ${state.fullPath}');
          debugPrint('🔍 ROUTER: state.uri = ${state.uri}');
          debugPrint('🔍 ROUTER: state.uri.path = ${state.uri.path}');
          debugPrint('🔍 ROUTER: state.uri.query = ${state.uri.query}');
          debugPrint('🔍 ROUTER: state.uri.queryParameters = ${state.uri.queryParameters}');

          // Check for actualMasjidId in query parameters
          final actualMasjidId = state.uri.queryParameters['actualMasjidId'];
          debugPrint('🔍 ROUTER: Extracted actualMasjidId = $actualMasjidId');
          debugPrint('🔍 ROUTER: Creating ReportIssuePage with actualMasjidId = $actualMasjidId');

          return MaterialPage(child: ReportIssuePage(actualMasjidId: actualMasjidId));
        },
      ),
      GoRoute(
        path: RouteConstants.notifications,
        pageBuilder: (context, state) => const MaterialPage(child: NotificationSettingsPage()),
      ),
      // Debug routes
      GoRoute(
        path: RouteConstants.supabaseTest,
        pageBuilder: (context, state) => const MaterialPage(child: SupabaseTestPage()),
      ),
      GoRoute(
        path: RouteConstants.masjidsDebug,
        pageBuilder: (context, state) => const MaterialPage(child: MasjidsDebugPage()),
      ),
      GoRoute(
        path: RouteConstants.performanceTest,
        pageBuilder: (context, state) => const MaterialPage(child: PerformanceTestPage()),
      ),
      GoRoute(
        path: RouteConstants.instagramTest,
        pageBuilder: (context, state) => const MaterialPage(child: InstagramTestPage()),
      ),
      GoRoute(
        path: RouteConstants.analyticsDebug,
        pageBuilder: (context, state) => const MaterialPage(child: AnalyticsDebugScreen()),
      ),
    ];
  }
}

/// Error screen for route errors
class _ErrorScreen extends StatelessWidget {
  const _ErrorScreen();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(AppLocalizations.of(context)?.errorPageTitle ?? 'Error')),
      // Use the theme's surface color for background
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: Center(child: Text(AppLocalizations.of(context)?.pageNotFoundMessage ?? 'Page not found')),
    );
  }
}
