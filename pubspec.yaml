name: masajid_albahrain
description: "Masajid AlBah<PERSON> - A mosque finder and prayer times app for Bahrain"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.3.0+8

environment:
  sdk: '>=3.8.0 <4.0.0'
  flutter: '>=3.32.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  adhan: ^2.0.0+1
  battery_plus: ^6.2.3
  cached_network_image: ^3.4.1
  connectivity_plus: ^6.1.4
  crypto: ^3.0.6
  cupertino_icons: ^1.0.8

  device_info_plus: ^11.5.0
  dio: ^5.7.0
  drift: ^2.20.3
  encrypt: ^5.0.3
  equatable: ^2.0.7
  firebase_analytics: ^12.0.0
  firebase_core: ^4.0.0
  flutter:
    sdk: flutter
  flutter_compass: ^0.8.1
  flutter_dotenv: ^6.0.0
  flutter_image_compress: ^2.4.0
  flutter_islamic_icons: ^1.0.2
  flutter_local_notifications: ^19.2.1
  flutter_localization: ^0.3.2
  flutter_localizations:
    sdk: flutter
  flutter_riverpod: ^3.0.0-dev.17
  flutter_secure_storage: ^9.2.4  # Keep current version due to platform interface constraints
  flutter_svg: ^2.1.0
  font_awesome_flutter: ^10.8.0
  fpdart: ^1.1.0
  freezed_annotation: ^3.1.0
  geolocator: ^14.0.1
  get_it: ^8.0.2
  go_router: ^16.0.0
  google_fonts: ^6.2.1
  google_maps_flutter: ^2.12.2
  hijri: ^3.0.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  http: ^1.4.0
  image: ^4.2.0
  image_picker: ^1.1.2
  internet_connection_checker_plus: ^2.7.2
  intl: ^0.20.2 # Pinned by flutter_localizations
  json_annotation: ^4.9.0
  location: ^7.0.0  # Context7 MCP: Flutter Location for real-time location streams
  logger: ^2.5.0
  media_kit: ^1.1.11
  media_kit_libs_audio: ^1.0.5
  path: any
  path_provider: ^2.1.5
  permission_handler: ^12.0.1
  photo_view: ^0.15.0
  retry: ^3.1.2
  riverpod_annotation: ^3.0.0-dev.17
  rxdart: ^0.28.0
  sensors_plus: ^6.0.1
  sentry_flutter: ^8.11.0
  shared_preferences: ^2.5.3
  shimmer: ^3.0.0
  sqlite3_flutter_libs: ^0.5.24
  supabase_flutter: ^2.9.0
  talker_flutter: ^5.0.0
  timezone: ^0.10.1
  tutorial_coach_mark: ^1.3.0
  url_launcher: ^6.3.1
  uuid: ^4.5.1
  workmanager: ^0.9.0+2

  collection: any
  state_notifier: ^1.0.0
dev_dependencies:
  alchemist: ^0.12.0
  build_runner: ^2.6.1
  custom_lint: ^0.8.0
  drift_dev: ^2.28.1
  flutter_driver:
    sdk: flutter
  flutter_launcher_icons: ^0.14.3
  flutter_lints: ^6.0.0
  flutter_test:
    sdk: flutter
  freezed: ^3.2.0
  integration_test:
    sdk: flutter
  json_serializable: ^6.7.1
  mockito: ^5.5.0
  mocktail: ^1.0.4
  riverpod_generator: ^3.0.0-dev.17
  riverpod_lint: ^3.0.0-dev.17
  test: ^1.25.15

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# Dependency overrides to resolve version conflicts
dependency_overrides:
  # Build system packages - use latest compatible versions
  # Remove analyzer override to use latest compatible version
  # analyzer: ">=5.2.0 <5.13.0"
  # build: ">=2.3.0 <2.5.0"
  # build_resolvers: ">=2.1.0 <2.5.0"
  # dart_style: ">=2.2.0 <2.4.0"
  # source_gen: ">=1.2.0 <1.5.0"
  
  # Core dependencies
  characters: ^1.4.1
  
  # Flutter secure storage compatibility
  flutter_secure_storage_linux: ^2.0.1
  flutter_secure_storage_macos: ^4.0.0
  flutter_secure_storage_platform_interface: ^2.0.1
  flutter_secure_storage_web: ^2.0.0
  flutter_secure_storage_windows: ^4.0.0
  
  # Testing and debugging
  leak_tracker: ^11.0.1
  leak_tracker_flutter_testing: ^3.0.10
  leak_tracker_testing: ^3.0.2
  
  # Material and UI
  material_color_utilities: ^0.13.0
  
  # Core Dart packages
  meta: ^1.17.0
  petitparser: ^7.0.0
  pointycastle: ^4.0.0
  process: ^5.0.5
  vector_math: ^2.2.0
  vm_service: ^15.0.2
  xml: ^6.6.0

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Enable generation of localized strings from arb files
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icon/
    - assets/l10n/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/masajid_albahrain_icon.webp"
  remove_alpha_ios: true  # Remove alpha channel for App Store compliance
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/icon/masajid_albahrain_icon.webp"
    background_color: "#1E88E5"  # Using the primary color from the app
    theme_color: "#1E88E5"
  windows:
    generate: true
    image_path: "assets/icon/masajid_albahrain_icon.webp"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/icon/masajid_albahrain_icon.webp"
