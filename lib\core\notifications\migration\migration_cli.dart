#!/usr/bin/env dart

/// Migration CLI Script
///
/// **Task 4.1.3: Add migration utilities for automated code updates**
///
/// This standalone CLI script provides command-line interface for automated
/// provider migration following Context7 MCP patterns.
///
/// **Usage:**
/// ```bash
/// dart run lib/core/notifications/migration/migration_cli.dart scan
/// dart run lib/core/notifications/migration/migration_cli.dart migrate --all
/// dart run lib/core/notifications/migration/migration_cli.dart rollback --file path/to/file.dart.backup
/// ```
///
/// Features:
/// - Project scanning for legacy provider usage
/// - Automated batch migration with progress tracking
/// - Individual file migration with validation
/// - Rollback capabilities using backup files
/// - Migration script generation
/// - Comprehensive validation and reporting
/// - Context7 MCP compliance verification
/// - Developer-friendly command interface

import 'dart:io';

import 'migration_utilities.dart';

/// Main entry point for migration CLI
Future<void> main(List<String> args) async {
  final cli = MigrationCLI();
  await cli.run(args);
}

/// Migration CLI Implementation
class MigrationCLI {
  /// Run migration CLI with provided arguments
  Future<void> run(List<String> args) async {
    if (args.isEmpty) {
      _printUsage();
      return;
    }

    final command = args[0];
    final options = _parseOptions(args.skip(1).toList());

    try {
      switch (command) {
        case 'scan':
          await _runScan(options);
          break;
        case 'migrate':
          await _runMigrate(options);
          break;
        case 'rollback':
          await _runRollback(options);
          break;
        case 'validate':
          await _runValidate(options);
          break;
        case 'generate-script':
          await _runGenerateScript(options);
          break;
        case 'help':
        case '--help':
        case '-h':
          _printUsage();
          break;
        default:
          print('❌ Unknown command: $command');
          print('');
          _printUsage();
          exit(1);
      }
    } on MigrationException catch (e) {
      print('❌ Migration Error: ${e.message}');
      exit(1);
    } catch (e) {
      print('❌ Unexpected Error: $e');
      exit(1);
    }
  }

  /// Run scan command
  Future<void> _runScan(Map<String, String> options) async {
    print('🔍 Scanning project for legacy provider usage...');
    print('');

    final migrationUtils = MigrationUtilities();
    final scanResult = await migrationUtils.scanProject(
      projectPath: options['path'] ?? '.',
      includePaths: options['include']?.split(',') ?? ['lib/'],
      excludePaths: options['exclude']?.split(',') ?? ['.dart_tool/', 'build/'],
    );

    print('📊 Scan Results:');
    print('  Total files scanned: ${scanResult.totalFilesScanned}');
    print('  Files to migrate: ${scanResult.filesToMigrate.length}');
    print('  Legacy usages found: ${scanResult.legacyUsages.length}');
    print('');

    if (scanResult.legacyUsages.isNotEmpty) {
      print('📋 Legacy Usages Found:');
      print('');
      
      // Group usages by file
      final usagesByFile = <String, List<LegacyUsage>>{};
      for (final usage in scanResult.legacyUsages) {
        usagesByFile.putIfAbsent(usage.filePath, () => []).add(usage);
      }

      for (final entry in usagesByFile.entries) {
        print('  📄 ${entry.key}:');
        for (final usage in entry.value) {
          print('    Line ${usage.lineNumber}: ${usage.type.name}');
          print('      Current:   ${usage.content.trim()}');
          print('      Suggested: ${usage.suggestedReplacement.trim()}');
          print('');
        }
      }
    }

    if (scanResult.filesToMigrate.isNotEmpty) {
      print('💡 Next Steps:');
      print('  • Run batch migration: dart run migration_cli.dart migrate --all');
      print('  • Generate script: dart run migration_cli.dart generate-script');
      print('  • Migrate single file: dart run migration_cli.dart migrate --file <path>');
      print('');
      print('⚠️  Remember to commit your changes before running migration!');
    } else {
      print('✅ Excellent! No migration needed - all files are up to date!');
    }
  }

  /// Run migrate command
  Future<void> _runMigrate(Map<String, String> options) async {
    final migrationUtils = MigrationUtilities();

    if (options.containsKey('all')) {
      print('🚀 Starting batch migration...');
      print('');

      final scanResult = await migrationUtils.scanProject();
      if (scanResult.filesToMigrate.isEmpty) {
        print('✅ No files need migration!');
        return;
      }

      print('📋 Files to migrate: ${scanResult.filesToMigrate.length}');
      for (final file in scanResult.filesToMigrate) {
        print('  • $file');
      }
      print('');

      // Confirm before proceeding
      if (!options.containsKey('yes')) {
        stdout.write('Continue with migration? (y/N): ');
        final response = stdin.readLineSync()?.toLowerCase();
        if (response != 'y' && response != 'yes') {
          print('Migration cancelled.');
          return;
        }
      }

      final batchResult = await migrationUtils.migrateBatch(
        scanResult.filesToMigrate,
        createBackups: !options.containsKey('no-backup'),
        validateAfterMigration: !options.containsKey('no-validate'),
        onProgress: (current, total) {
          final percentage = (current / total * 100).round();
          print('Progress: $current/$total ($percentage%)');
        },
      );

      print('');
      print('📊 Migration Results:');
      print('  Total files: ${batchResult.totalFiles}');
      print('  ✅ Successful: ${batchResult.successCount}');
      print('  ❌ Failed: ${batchResult.failureCount}');
      print('');

      if (batchResult.successCount > 0) {
        print('✅ Successfully migrated files:');
        for (final result in batchResult.results.where((r) => r.success)) {
          print('  • ${result.filePath}: ${result.changesApplied} changes');
          if (result.backupPath != null) {
            print('    Backup: ${result.backupPath}');
          }
        }
        print('');
      }

      if (batchResult.failureCount > 0) {
        print('❌ Failed migrations:');
        for (final result in batchResult.results.where((r) => !r.success)) {
          print('  • ${result.filePath}: ${result.message}');
        }
        print('');
      }

      if (batchResult.successCount > 0) {
        print('🎉 Migration completed successfully!');
        print('💡 Next steps:');
        print('  • Test your application thoroughly');
        print('  • Run: flutter test');
        print('  • Commit your changes');
      }
    } else if (options.containsKey('file')) {
      final filePath = options['file']!;
      print('🔧 Migrating file: $filePath');
      print('');

      final result = await migrationUtils.migrateFile(
        filePath,
        createBackup: !options.containsKey('no-backup'),
        validateAfterMigration: !options.containsKey('no-validate'),
      );

      if (result.success) {
        print('✅ Migration successful!');
        print('  Changes applied: ${result.changesApplied}');
        if (result.backupPath != null) {
          print('  Backup created: ${result.backupPath}');
        }
        print('  Message: ${result.message}');
      } else {
        print('❌ Migration failed!');
        print('  Error: ${result.message}');
        exit(1);
      }
    } else {
      print('❌ Error: Specify --all or --file <path>');
      print('');
      _printUsage();
      exit(1);
    }
  }

  /// Run rollback command
  Future<void> _runRollback(Map<String, String> options) async {
    if (!options.containsKey('file')) {
      print('❌ Error: Specify --file <backup-path>');
      print('');
      _printUsage();
      exit(1);
    }

    final backupPath = options['file']!;
    print('🔄 Rolling back migration: $backupPath');
    print('');

    final migrationUtils = MigrationUtilities();
    final success = await migrationUtils.rollbackMigration(backupPath);

    if (success) {
      print('✅ Rollback successful!');
      print('  File restored from backup');
      print('  Backup file removed');
    } else {
      print('❌ Rollback failed!');
      print('  Check if backup file exists and is readable');
      exit(1);
    }
  }

  /// Run validate command
  Future<void> _runValidate(Map<String, String> options) async {
    if (!options.containsKey('file')) {
      print('❌ Error: Specify --file <path>');
      print('');
      _printUsage();
      exit(1);
    }

    final filePath = options['file']!;
    print('🔍 Validating migration: $filePath');
    print('');

    final migrationUtils = MigrationUtilities();
    final validationResult = await migrationUtils.validateMigration(filePath);

    if (validationResult.isValid) {
      print('✅ Validation successful!');
      print('  No legacy patterns detected');
      print('  File appears to be properly migrated');
    } else {
      print('❌ Validation failed!');
      print('  Issues found:');
      for (final error in validationResult.errors) {
        print('    • $error');
      }
      exit(1);
    }
  }

  /// Run generate script command
  Future<void> _runGenerateScript(Map<String, String> options) async {
    print('📝 Generating migration script...');
    print('');

    final migrationUtils = MigrationUtilities();
    final script = await migrationUtils.generateMigrationScript(
      projectPath: options['path'] ?? '.',
      includePaths: options['include']?.split(',') ?? ['lib/'],
    );

    final scriptPath = options['output'] ?? 'migration_script.dart';
    await File(scriptPath).writeAsString(script);

    print('✅ Migration script generated successfully!');
    print('  Script path: $scriptPath');
    print('  Script size: ${script.length} characters');
    print('');
    print('💡 To run the generated script:');
    print('  dart run $scriptPath');
    print('');
    print('⚠️  Make sure to review the script before running it!');
  }

  /// Parse command line options
  Map<String, String> _parseOptions(List<String> args) {
    final options = <String, String>{};

    for (var i = 0; i < args.length; i++) {
      final arg = args[i];

      if (arg.startsWith('--')) {
        final key = arg.substring(2);

        if (i + 1 < args.length && !args[i + 1].startsWith('--')) {
          options[key] = args[i + 1];
          i++; // Skip next argument as it's the value
        } else {
          options[key] = 'true'; // Flag without value
        }
      }
    }

    return options;
  }

  /// Print usage information
  void _printUsage() {
    print('''
🔧 Migration CLI Tool - Automated Provider Migration

USAGE:
  dart run migration_cli.dart <command> [options]

COMMANDS:
  scan                    Scan project for legacy provider usage
  migrate                 Migrate files to use unified providers
  rollback                Rollback migration using backup file
  validate                Validate migration result
  generate-script         Generate automated migration script
  help                    Show this help message

OPTIONS:
  --path <path>           Project path (default: .)
  --include <paths>       Include paths (comma-separated, default: lib/)
  --exclude <paths>       Exclude paths (comma-separated, default: .dart_tool/,build/)
  --file <path>           Specific file to migrate/rollback/validate
  --all                   Migrate all files
  --yes                   Skip confirmation prompts
  --no-backup             Skip backup creation
  --no-validate           Skip validation after migration
  --output <path>         Output path for generated script (default: migration_script.dart)

EXAMPLES:
  # Scan project for legacy usage
  dart run migration_cli.dart scan

  # Migrate all files with confirmation
  dart run migration_cli.dart migrate --all

  # Migrate all files without confirmation
  dart run migration_cli.dart migrate --all --yes

  # Migrate specific file
  dart run migration_cli.dart migrate --file lib/pages/settings_page.dart

  # Rollback using backup
  dart run migration_cli.dart rollback --file lib/pages/settings_page.dart.backup

  # Validate migration
  dart run migration_cli.dart validate --file lib/pages/settings_page.dart

  # Generate migration script
  dart run migration_cli.dart generate-script --output my_migration.dart

MIGRATION WORKFLOW:
  1. Commit your current changes
  2. Run: dart run migration_cli.dart scan
  3. Review the scan results
  4. Run: dart run migration_cli.dart migrate --all
  5. Test your application
  6. Commit the migrated code

For more information, visit:
https://docs.masajid-albahrain.com/migration

Context7 MCP Compliance: ✅
''');
  }
}
