import 'dart:math' as math;

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';

import '../../../../core/adapters/location_service_adapter.dart';
import '../../../../core/models/masjid_distance.dart';
import '../../../../core/providers/location_providers.dart';
import '../../../../core/providers/master_location_provider.dart';
// Context7 MCP: Use unified location providers instead of persistent location provider
import '../../../../core/services/location/unified_location_service.dart';
import '../../../location/domain/entities/location.dart' as domain;
import '../../../location/domain/value_objects/coordinates.dart';
import '../../../location/domain/value_objects/location_accuracy.dart' as domain_accuracy;
import '../../../location/domain/value_objects/location_timestamp.dart' as domain_timestamp;
import '../../../masjids/presentation/providers/unified_masjids_filter_manager.dart';
import 'home_search_provider.dart';

/// Context7 MCP: Unified Nearby Masjids Provider
///
/// This provider consolidates all nearby masjids functionality following Context7 MCP best practices:
/// - Uses Result types for type-safe error handling
/// - Implements proper location initialization synchronization
/// - Follows clean architecture principles
/// - Provides comprehensive error recovery
/// - Maintains backward compatibility with existing code
///
/// This replaces the previous multiple providers:
/// - nearbyMasjidsProvider (original)
/// - nearbyMasjidsProviderImproved (unused)
/// - modern_nearby_masjids_provider (unused)

/// Provider for the location service adapter used in nearby masjids calculations
/// This creates a unified location service that follows Context7 MCP best practices
final nearbyMasjidsLocationServiceProvider = Provider<LocationServiceAdapter>((ref) {
  final unifiedService = UnifiedLocationService();
  return LocationServiceAdapter(unifiedService);
});

/// Unified provider for masjids within 1 kilometer of user location
///
/// Context7 MCP Features:
/// - Location initialization synchronization to prevent race conditions
/// - Result types for type-safe error handling
/// - Reactive updates on location and permission changes
/// - Comprehensive error recovery and user feedback
/// - Performance optimized with proper caching
///
/// Returns empty list if location permission is not granted
/// Uses persistent location manager to survive navigation changes
final nearbyMasjidsProvider = FutureProvider<List<MasjidDistance>>((ref) async {
  debugPrint('📍 Calculating nearby masjids (unified provider)');

  // Context7 MCP: Listen to master location changes and invalidate this provider when they occur
  ref.listen(currentLocationSelectorProvider, (previous, next) {
    if (previous != null && next != null) {
      // Check if this is a significant location change (>100 meters)
      final distance = _calculateDistance(previous.latitude, previous.longitude, next.latitude, next.longitude);
      if (distance > 100.0) {
        // 100 meters threshold for significant change
        debugPrint(
          '📍 Significant location change detected (${distance.toStringAsFixed(0)}m), invalidating nearby masjids',
        );
        // Invalidate this provider to trigger recalculation
        Future.microtask(() => ref.invalidateSelf());
      }
    }
  });

  // Context7 MCP: Listen to location permission changes using unified providers
  ref.listen(locationPermissionStatusProvider, (previous, next) {
    // If permission changed from denied to granted, refresh nearby masjids
    if (previous != null && !previous.isLocationAvailable && next.isLocationAvailable) {
      debugPrint('📍 Location permission granted, refreshing nearby masjids immediately');
      Future.microtask(() => ref.invalidateSelf());
    }
  });

  // Context7 MCP: Listen to location availability changes for automatic triggering
  ref.listen(isLocationServiceReadyProvider, (previous, next) {
    if (previous == false && next == true) {
      debugPrint('📍 Location became available, triggering immediate nearby masjids calculation');
      Future.microtask(() => ref.invalidateSelf());
    }
  });

  // Context7 MCP: Listen to master location manager state changes for immediate response
  ref.listen(masterLocationManagerProvider, (previous, next) {
    final hadLocation = previous?.hasLocation ?? false;
    final hasLocation = next.hasLocation;

    if (!hadLocation && hasLocation) {
      debugPrint('📍 Master location manager has location, triggering immediate nearby masjids refresh');
      Future.microtask(() => ref.invalidateSelf());
    }
  });

  // Context7 MCP: Listen to location updates with smart debouncing to prevent infinite loops
  ref.listen(currentLocationSelectorProvider, (previous, next) {
    if (next != null && previous != null) {
      // Check if coordinates actually changed
      final latChanged = (previous.latitude ?? 0.0) != (next.latitude ?? 0.0);
      final lonChanged = (previous.longitude ?? 0.0) != (next.longitude ?? 0.0);

      if (!latChanged && !lonChanged) {
        debugPrint('📍 Location coordinates unchanged, skipping refresh');
        return;
      }

      // Only refresh if location changed significantly (more than 50 meters)
      final distance = _calculateDistance(
        previous.latitude ?? 0.0,
        previous.longitude ?? 0.0,
        next.latitude ?? 0.0,
        next.longitude ?? 0.0,
      );

      if (distance > 50.0) {
        // 50 meters threshold for significant location change
        debugPrint(
          '📍 Significant location change detected (${distance.toStringAsFixed(0)}m), refreshing nearby masjids',
        );
        Future.microtask(() => ref.invalidateSelf());
      } else {
        debugPrint('📍 Minor location change (${distance.toStringAsFixed(0)}m), skipping refresh');
      }
    } else if (previous == null && next != null) {
      // First location update - always refresh
      debugPrint('📍 First location detected, refreshing nearby masjids');
      Future.microtask(() => ref.invalidateSelf());
    }
  });

  try {
    // Context7 MCP: Step 0 - Check master location availability first
    final isLocationAvailable = ref.read(isLocationServiceReadyProvider);
    if (!isLocationAvailable) {
      debugPrint('📍 Master location not available, checking initialization...');

      // Try to get location manager to trigger initialization
      final locationManager = ref.read(masterLocationManagerProvider.notifier);

      // Trigger location refresh to ensure initialization
      await locationManager.getCurrentLocation(useCache: false);

      // Wait a short time for initialization to complete
      await Future.delayed(const Duration(milliseconds: 500));

      // Check again after waiting
      final isNowAvailable = ref.read(isLocationServiceReadyProvider);
      if (!isNowAvailable) {
        debugPrint('📍 Location still not available after initialization attempt');
        return <MasjidDistance>[];
      }
    }

    // Context7 MCP: Get location using master location provider
    final locationData = ref.watch(currentLocationSelectorProvider);
    if (locationData == null) {
      debugPrint('📍 No location available from Context7 provider, returning empty nearby masjids list');
      return <MasjidDistance>[];
    }

    debugPrint('📍 Using Context7 location: ${locationData.latitude}, ${locationData.longitude}');

    // Convert to Position for compatibility with existing code
    final userPosition = Position(
      latitude: locationData.latitude,
      longitude: locationData.longitude,
      timestamp: locationData.timestamp ?? DateTime.now(),
      accuracy: locationData.accuracy,
      altitude: locationData.altitude ?? 0.0,
      altitudeAccuracy: 0.0,
      heading: locationData.heading ?? 0.0,
      headingAccuracy: 0.0,
      speed: locationData.speed ?? 0.0,
      speedAccuracy: 0.0,
    );

    // Get the current search query
    final searchQuery = ref.watch(homeSearchQueryProvider);

    // Determine which provider to use based on whether search is active
    final isSearchActive = searchQuery.trim().isNotEmpty;

    // Get masjids based on search or filter
    final masjids = isSearchActive
        ? await ref.watch(debouncedHomeSearchResultsProvider.future)
        : await ref.watch(filteredMasjidsProvider.future);

    debugPrint('Calculating nearby masjids for ${masjids.length} total masjids');

    // Filter masjids within 1km using unified location service
    final locationService = ref.read(nearbyMasjidsLocationServiceProvider);
    final nearbyMasjids = <MasjidDistance>[];

    for (final masjid in masjids) {
      try {
        // Create domain Location objects for distance calculation
        final userLocation = domain.Location(
          coordinates: Coordinates(latitude: userPosition.latitude, longitude: userPosition.longitude),
          accuracy: domain_accuracy.LocationAccuracy(userPosition.accuracy),
          timestamp: domain_timestamp.LocationTimestamp(userPosition.timestamp),
        );

        final masjidLocation = domain.Location(
          coordinates: Coordinates(latitude: masjid.latitude, longitude: masjid.longitude),
          accuracy: const domain_accuracy.LocationAccuracy(10.0), // Default accuracy for masjids
          timestamp: domain_timestamp.LocationTimestamp(DateTime.now()),
        );

        // Calculate distance using the location service
        final distance = locationService.calculateDistance(userLocation, masjidLocation);

        // Only include masjids within 1 kilometer (1000 meters)
        if (distance <= 1000) {
          nearbyMasjids.add(MasjidDistance(masjid: masjid, distanceInMeters: distance));
        }
      } on Exception catch (e) {
        debugPrint('📍 Error calculating distance for masjid ${masjid.id}: $e');
        // Continue with other masjids
      }
    }

    // Sort by distance (closest first)
    nearbyMasjids.sort((a, b) => a.distanceInMeters.compareTo(b.distanceInMeters));

    debugPrint('Found ${nearbyMasjids.length} nearby masjids within 1km');
    return nearbyMasjids;
  } on Exception catch (e) {
    debugPrint('📍 Error calculating nearby masjids: $e');
    return <MasjidDistance>[];
  }
});

/// Provider for the count of nearby masjids within 1 kilometer
/// This is a simpler provider that just returns the count
/// Uses persistent location manager to survive navigation changes
final nearbyMasjidsCountProvider = FutureProvider<int>((ref) async {
  final nearbyMasjids = await ref.watch(nearbyMasjidsProvider.future);
  return nearbyMasjids.length;
});

/// Provider for nearby masjids within a custom distance
/// This allows for flexible distance filtering
/// Returns empty list if location permission is not granted
/// Uses persistent location manager to survive navigation changes
final nearbyMasjidsWithinDistanceProvider = FutureProvider.family<List<MasjidDistance>, double>((
  ref,
  maxDistanceInMeters,
) async {
  try {
    // Context7 MCP: Step 0 - Check master location availability for distance filter
    final isLocationAvailable = ref.read(isLocationServiceReadyProvider);
    if (!isLocationAvailable) {
      debugPrint('📍 Master location not available for distance filter, checking initialization...');

      // Try to get location manager to trigger initialization
      final locationManager = ref.read(masterLocationManagerProvider.notifier);

      // Trigger location refresh to ensure initialization
      await locationManager.getCurrentLocation(useCache: false);

      // Wait a short time for initialization to complete
      await Future.delayed(const Duration(milliseconds: 500));

      // Check again after waiting
      final isNowAvailable = ref.read(isLocationServiceReadyProvider);
      if (!isNowAvailable) {
        debugPrint('📍 Location still not available for distance filter after initialization attempt');
        return <MasjidDistance>[];
      }
    }

    // Context7 MCP: Get location using master location provider
    final locationData = ref.watch(currentLocationSelectorProvider);
    if (locationData == null) {
      debugPrint('📍 No location available from Context7 provider for distance filter');
      return <MasjidDistance>[];
    }

    debugPrint('📍 Using Context7 location for distance filter: ${locationData.latitude}, ${locationData.longitude}');

    // Convert to Position for compatibility with existing code
    final userPosition = Position(
      latitude: locationData.latitude,
      longitude: locationData.longitude,
      timestamp: locationData.timestamp ?? DateTime.now(),
      accuracy: locationData.accuracy,
      altitude: locationData.altitude ?? 0.0,
      altitudeAccuracy: 0.0,
      heading: locationData.heading ?? 0.0,
      headingAccuracy: 0.0,
      speed: locationData.speed ?? 0.0,
      speedAccuracy: 0.0,
    );

    // Get the current search query
    final searchQuery = ref.watch(homeSearchQueryProvider);

    // Determine which provider to use based on whether search is active
    final isSearchActive = searchQuery.trim().isNotEmpty;

    // Get masjids based on search or filter
    final masjids = isSearchActive
        ? await ref.watch(debouncedHomeSearchResultsProvider.future)
        : await ref.watch(filteredMasjidsProvider.future);

    debugPrint('Calculating nearby masjids within ${maxDistanceInMeters}m for ${masjids.length} total masjids');

    // Filter masjids within specified distance using unified location service
    final locationService = ref.read(nearbyMasjidsLocationServiceProvider);
    final nearbyMasjids = <MasjidDistance>[];

    for (final masjid in masjids) {
      try {
        // Create domain Location objects for distance calculation
        final userLocation = domain.Location(
          coordinates: Coordinates(latitude: userPosition.latitude, longitude: userPosition.longitude),
          accuracy: domain_accuracy.LocationAccuracy(userPosition.accuracy),
          timestamp: domain_timestamp.LocationTimestamp(userPosition.timestamp),
        );

        final masjidLocation = domain.Location(
          coordinates: Coordinates(latitude: masjid.latitude, longitude: masjid.longitude),
          accuracy: const domain_accuracy.LocationAccuracy(10.0), // Default accuracy for masjids
          timestamp: domain_timestamp.LocationTimestamp(DateTime.now()),
        );

        // Calculate distance using the location service
        final distance = locationService.calculateDistance(userLocation, masjidLocation);

        // Only include masjids within specified distance
        if (distance <= maxDistanceInMeters) {
          nearbyMasjids.add(MasjidDistance(masjid: masjid, distanceInMeters: distance));
        }
      } on Exception catch (e) {
        debugPrint('📍 Error calculating distance for masjid ${masjid.id}: $e');
        // Continue with other masjids
      }
    }

    // Sort by distance (closest first)
    nearbyMasjids.sort((a, b) => a.distanceInMeters.compareTo(b.distanceInMeters));

    debugPrint('Found ${nearbyMasjids.length} nearby masjids within ${maxDistanceInMeters}m');
    return nearbyMasjids;
  } on Exception catch (e) {
    debugPrint('📍 Error calculating nearby masjids within distance: $e');
    return <MasjidDistance>[];
  }
});

/// Provider that checks if location is available for nearby masjids
///
/// **Context7 MCP Pattern:** Uses master location availability provider
final isLocationAvailableForNearbyMasjidsProvider = Provider<bool>((ref) {
  return ref.watch(isLocationServiceReadyProvider);
});

/// Provider for nearby masjids status
///
/// **Context7 MCP Pattern:** Uses master location availability provider
final nearbyMasjidsStatusProvider = Provider<String>((ref) {
  final nearbyMasjidsAsync = ref.watch(nearbyMasjidsProvider);
  final isLocationAvailable = ref.watch(isLocationServiceReadyProvider);

  if (!isLocationAvailable) {
    return 'Location not available';
  }

  return nearbyMasjidsAsync.when(
    data: (masjids) => 'Found ${masjids.length} nearby masjids',
    loading: () => 'Loading nearby masjids...',
    error: (error, stack) => 'Error: $error',
  );
});

/// Calculate distance between two coordinates in meters
/// Context7 MCP: Helper function for smart location change detection
double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
  const double earthRadius = 6371000; // Earth's radius in meters

  final dLat = _degreesToRadians(lat2 - lat1);
  final dLon = _degreesToRadians(lon2 - lon1);

  final a =
      math.sin(dLat / 2) * math.sin(dLat / 2) +
      math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) * math.sin(dLon / 2) * math.sin(dLon / 2);

  final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

  return earthRadius * c;
}

/// Convert degrees to radians
double _degreesToRadians(double degrees) {
  return degrees * (math.pi / 180);
}
