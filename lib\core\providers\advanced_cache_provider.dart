import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/masjids/presentation/providers/governorates_provider.dart';
import '../../features/masjids/presentation/providers/unified_masjids_data_manager.dart';
import '../../features/prayer_times/presentation/providers/prayer_times_provider.dart';
import '../services/unified_monitoring_service.dart';
import '../utils/result.dart';
import 'unified_service_providers.dart';

part 'advanced_cache_provider.g.dart';

/// Advanced caching provider with intelligent autoDispose and cache warming
///
/// Features:
/// - Intelligent autoDispose based on usage patterns
/// - Cache warming for critical data
/// - Background refresh without blocking UI
/// - Cache performance monitoring
/// - Memory pressure awareness
@riverpod
class AdvancedCacheManager extends _$AdvancedCacheManager {
  Timer? _backgroundRefreshTimer;
  Timer? _cacheWarmingTimer;
  final Map<String, DateTime> _lastAccessTimes = {};
  final Map<String, int> _accessCounts = {};
  final Set<String> _criticalCacheKeys = {};

  // Configuration
  /// Delay before auto-disposing unused cache entries
  static const Duration autoDisposeDelay = Duration(minutes: 5);

  /// Interval for background cache refresh operations
  static const Duration backgroundRefreshInterval = Duration(minutes: 30);

  /// Interval for cache warming operations
  static const Duration cacheWarmingInterval = Duration(hours: 6);

  /// Threshold for considering a cache key as frequently accessed
  static const int maxAccessCountThreshold = 10;

  @override
  CacheManagerState build() {
    _initializeCacheManager();
    return const CacheManagerState(
      isWarming: false,
      isBackgroundRefreshing: false,
      cacheHitRate: 0.0,
      totalRequests: 0,
      cacheHits: 0,
    );
  }

  /// Initialize the cache manager with background tasks
  void _initializeCacheManager() {
    debugPrint('🚀 Advanced Cache Manager initialized');

    // Start background refresh timer
    _backgroundRefreshTimer = Timer.periodic(backgroundRefreshInterval, (_) {
      _performBackgroundRefresh();
    });

    // Start cache warming timer
    _cacheWarmingTimer = Timer.periodic(cacheWarmingInterval, (_) {
      _performCacheWarming();
    });

    // Perform initial cache warming
    Future.delayed(const Duration(seconds: 5), () {
      _performCacheWarming();
    });
  }

  /// Track cache access for intelligent autoDispose
  void trackCacheAccess(String cacheKey) {
    _lastAccessTimes[cacheKey] = DateTime.now();
    _accessCounts[cacheKey] = (_accessCounts[cacheKey] ?? 0) + 1;

    // Update cache hit rate
    final currentState = state;
    final newTotalRequests = currentState.totalRequests + 1;
    final newCacheHits = currentState.cacheHits + 1;
    final newHitRate = newCacheHits / newTotalRequests;

    state = currentState.copyWith(totalRequests: newTotalRequests, cacheHits: newCacheHits, cacheHitRate: newHitRate);
  }

  /// Track cache miss for performance monitoring
  void trackCacheMiss(String cacheKey) {
    final currentState = state;
    final newTotalRequests = currentState.totalRequests + 1;
    final newHitRate = currentState.cacheHits / newTotalRequests;

    state = currentState.copyWith(totalRequests: newTotalRequests, cacheHitRate: newHitRate);
  }

  /// Mark cache key as critical (should not be auto-disposed)
  void markAsCritical(String cacheKey) {
    _criticalCacheKeys.add(cacheKey);
    debugPrint('🔒 Cache key marked as critical: $cacheKey');
  }

  /// Check if a cache key should be auto-disposed
  bool shouldAutoDispose(String cacheKey) {
    // Never auto-dispose critical cache keys
    if (_criticalCacheKeys.contains(cacheKey)) {
      return false;
    }

    final lastAccess = _lastAccessTimes[cacheKey];
    if (lastAccess == null) return true;

    final timeSinceLastAccess = DateTime.now().difference(lastAccess);
    final accessCount = _accessCounts[cacheKey] ?? 0;

    // Don't auto-dispose frequently accessed items
    if (accessCount > maxAccessCountThreshold) {
      return timeSinceLastAccess > autoDisposeDelay * 2;
    }

    return timeSinceLastAccess > autoDisposeDelay;
  }

  /// Perform background refresh of stale cache data
  Future<void> _performBackgroundRefresh() async {
    if (state.isBackgroundRefreshing) return;

    state = state.copyWith(isBackgroundRefreshing: true);
    debugPrint('🔄 Starting background cache refresh');

    try {
      final monitoring = UnifiedMonitoringService();
      await monitoring.measureOperation('background_cache_refresh', () async {
        final cacheService = await ref.read(unifiedCacheServiceProvider.future);
        final statsResult = await cacheService.getStatistics();
        final stats = statsResult.valueOrNull;

        debugPrint('📊 Cache stats before refresh: $stats');

        // Refresh critical cache keys
        // Note: Timestamp-based refresh not available in unified interface
        // Using simple refresh strategy for now
        for (final cacheKey in _criticalCacheKeys) {
          await _refreshCacheKey(cacheKey);
        }
      });
    } on Exception catch (e) {
      debugPrint('❌ Background refresh failed: $e');
    } finally {
      state = state.copyWith(isBackgroundRefreshing: false);
      debugPrint('✅ Background cache refresh completed');
    }
  }

  /// Perform cache warming for critical data
  Future<void> _performCacheWarming() async {
    if (state.isWarming) return;

    state = state.copyWith(isWarming: true);
    debugPrint('🔥 Starting cache warming');

    try {
      final monitoring = UnifiedMonitoringService();
      await monitoring.measureOperation('cache_warming', () async {
        // Warm critical cache keys
        final criticalKeys = [
          'prayer_times_${DateTime.now().toIso8601String().split('T')[0]}',
          'masjids_data',
          'governorates_data',
          'user_location',
        ];

        for (final key in criticalKeys) {
          markAsCritical(key);
          await _warmCacheKey(key);
        }
      });
    } on Exception catch (e) {
      debugPrint('❌ Cache warming failed: $e');
    } finally {
      state = state.copyWith(isWarming: false);
      debugPrint('✅ Cache warming completed');
    }
  }

  /// Refresh a specific cache key
  Future<void> _refreshCacheKey(String cacheKey) async {
    try {
      debugPrint('🔄 Refreshing cache key: $cacheKey');

      // This would trigger the appropriate provider refresh
      // Implementation depends on the specific cache key
      if (cacheKey.startsWith('prayer_times_')) {
        ref.invalidate(prayerTimesProvider);
      } else if (cacheKey == 'masjids_data') {
        ref.invalidate(unifiedMasjidsDataManagerProvider);
      } else if (cacheKey == 'governorates_data') {
        ref.invalidate(governoratesProvider);
      }
    } on Exception catch (e) {
      debugPrint('❌ Failed to refresh cache key $cacheKey: $e');
    }
  }

  /// Warm a specific cache key
  Future<void> _warmCacheKey(String cacheKey) async {
    try {
      debugPrint('🔥 Warming cache key: $cacheKey');

      // Pre-load data for critical cache keys
      if (cacheKey.startsWith('prayer_times_')) {
        ref.read(prayerTimesProvider);
      } else if (cacheKey == 'masjids_data') {
        ref.read(unifiedMasjidsDataManagerProvider);
      } else if (cacheKey == 'governorates_data') {
        ref.read(governoratesProvider);
      }
    } on Exception catch (e) {
      debugPrint('❌ Failed to warm cache key $cacheKey: $e');
    }
  }

  /// Get cache performance metrics
  Future<CachePerformanceMetrics> getPerformanceMetrics() async {
    final cacheService = await ref.read(unifiedCacheServiceProvider.future);
    final statsResult = await cacheService.getStatistics();

    // Context7 MCP Best Practice: Use Result.mapData for safe transformations to avoid extension conflicts
    return statsResult.mapData((stats) {
          return CachePerformanceMetrics(
            hitRate: state.cacheHitRate,
            totalRequests: state.totalRequests,
            cacheHits: state.cacheHits,
            totalCacheEntries: stats.totalEntries,
            validCacheEntries: stats.totalEntries - (stats.evictionCount), // Approximate valid entries
            criticalCacheKeys: _criticalCacheKeys.length,
            lastAccessTimes: Map.from(_lastAccessTimes),
            accessCounts: Map.from(_accessCounts),
          );
        }).valueOrNull ??
        CachePerformanceMetrics(
          // Context7 MCP Best Practice: Explicit fallback with safe defaults
          hitRate: state.cacheHitRate,
          totalRequests: state.totalRequests,
          cacheHits: state.cacheHits,
          totalCacheEntries: 0,
          validCacheEntries: 0,
          criticalCacheKeys: _criticalCacheKeys.length,
          lastAccessTimes: Map.from(_lastAccessTimes),
          accessCounts: Map.from(_accessCounts),
        );
  }

  /// Force cache warming (for manual triggers)
  Future<void> forceCacheWarming() async {
    await _performCacheWarming();
  }

  /// Force background refresh (for manual triggers)
  Future<void> forceBackgroundRefresh() async {
    await _performBackgroundRefresh();
  }

  /// Dispose of timers and cleanup resources
  void dispose() {
    _backgroundRefreshTimer?.cancel();
    _cacheWarmingTimer?.cancel();
  }
}

/// State class for cache manager
class CacheManagerState {
  /// Whether cache warming is currently in progress
  final bool isWarming;

  /// Whether background refresh is currently in progress
  final bool isBackgroundRefreshing;

  /// Current cache hit rate (0.0 to 1.0)
  final double cacheHitRate;

  /// Total number of cache requests made
  final int totalRequests;

  /// Total number of cache hits
  final int cacheHits;

  /// Creates a new cache manager state
  const CacheManagerState({
    required this.isWarming,
    required this.isBackgroundRefreshing,
    required this.cacheHitRate,
    required this.totalRequests,
    required this.cacheHits,
  });

  /// Creates a copy of this state with the given fields replaced
  CacheManagerState copyWith({
    bool? isWarming,
    bool? isBackgroundRefreshing,
    double? cacheHitRate,
    int? totalRequests,
    int? cacheHits,
  }) {
    return CacheManagerState(
      isWarming: isWarming ?? this.isWarming,
      isBackgroundRefreshing: isBackgroundRefreshing ?? this.isBackgroundRefreshing,
      cacheHitRate: cacheHitRate ?? this.cacheHitRate,
      totalRequests: totalRequests ?? this.totalRequests,
      cacheHits: cacheHits ?? this.cacheHits,
    );
  }
}

/// Cache performance metrics
class CachePerformanceMetrics {
  /// Cache hit rate (0.0 to 1.0)
  final double hitRate;

  /// Total number of cache requests
  final int totalRequests;

  /// Total number of cache hits
  final int cacheHits;

  /// Total number of cache entries
  final int totalCacheEntries;

  /// Number of valid (non-expired) cache entries
  final int validCacheEntries;

  /// Number of critical cache keys that won't be auto-disposed
  final int criticalCacheKeys;

  /// Map of cache keys to their last access times
  final Map<String, DateTime> lastAccessTimes;

  /// Map of cache keys to their access counts
  final Map<String, int> accessCounts;

  /// Creates cache performance metrics
  CachePerformanceMetrics({
    required this.hitRate,
    required this.totalRequests,
    required this.cacheHits,
    required this.totalCacheEntries,
    required this.validCacheEntries,
    required this.criticalCacheKeys,
    required this.lastAccessTimes,
    required this.accessCounts,
  });

  @override
  String toString() {
    return '''
📊 Cache Performance Metrics:
  Hit Rate: ${(hitRate * 100).toStringAsFixed(1)}%
  Total Requests: $totalRequests
  Cache Hits: $cacheHits
  Total Cache Entries: $totalCacheEntries
  Valid Cache Entries: $validCacheEntries
  Critical Cache Keys: $criticalCacheKeys
  Most Accessed: ${_getMostAccessedKeys()}
''';
  }

  String _getMostAccessedKeys() {
    final sorted = accessCounts.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
    return sorted.take(3).map((e) => '${e.key}(${e.value})').join(', ');
  }
}
