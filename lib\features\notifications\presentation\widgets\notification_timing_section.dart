import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Context7 MCP: Use unified notification provider for consolidated settings management
import '../../../../core/notifications/providers/unified_notification_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/logger.dart';

/// Notification timing section widget
class NotificationTimingSection extends ConsumerWidget {
  /// Whether the current language is Arabic
  final bool isArabic;

  /// Constructor
  const NotificationTimingSection({super.key, required this.isArabic});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Context7 MCP: Get notification settings from unified provider
    final notificationSettingsAsync = ref.watch(unifiedNotificationSettingsProvider);

    return notificationSettingsAsync.when(
      loading: () => _buildLoadingState(),
      error: (error, stackTrace) {
        AppLogger.error('NotificationTimingSection: Failed to load notification settings - $error');
        return _buildErrorState(context, error);
      },
      data: (notificationSettings) => _buildTimingSection(context, ref, notificationSettings),
    );
  }

  /// Context7 MCP: Build loading state widget
  Widget _buildLoadingState() {
    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue[50]!, Colors.blue[100]!],
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [BoxShadow(color: Colors.black.withAlpha(15), blurRadius: 3, offset: const Offset(0, 2))],
      ),
      child: const Padding(
        padding: EdgeInsets.all(12.0),
        child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
      ),
    );
  }

  /// Context7 MCP: Build error state widget
  Widget _buildErrorState(BuildContext context, Object error) {
    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.red[50]!, Colors.red[100]!],
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [BoxShadow(color: Colors.black.withAlpha(15), blurRadius: 3, offset: const Offset(0, 2))],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red[600], size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Error loading notification settings',
                style: TextStyle(color: Colors.red[700], fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Context7 MCP: Build the main timing section widget
  Widget _buildTimingSection(BuildContext context, WidgetRef ref, NotificationSettingsState notificationSettings) {
    // For now, we'll use a simple boolean to represent timing preference
    // This can be enhanced later to support more complex timing options
    final isExactTime =
        notificationSettings.prayerSettings.defaultReminderMinutes.isEmpty ||
        notificationSettings.prayerSettings.defaultReminderMinutes.first == 0;

    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue[50]!, Colors.blue[100]!], // Match sidebar gradient
        ),
        borderRadius: BorderRadius.circular(8), // Match sidebar border radius
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15), // Match sidebar shadow
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0), // Reduced from 16.0
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.timer_outlined,
                  color: Colors.blue[600]!, // Match sidebar icon color
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  isArabic ? 'توقيت الإشعار' : 'Notification Timing',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimaryLight, // Match sidebar text color
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12), // Reduced from 16
            const Divider(height: 1),
            const SizedBox(height: 6), // Reduced from 8
            // Context7 MCP: Standard Flutter radio buttons for notification timing options
            Column(
              children: [
                // At exact prayer time option (placed first)
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: isExactTime
                        ? Colors.blue[200]!.withAlpha(100) // Slightly darker blue for selected
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListTile(
                    leading: Icon(
                      isExactTime ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                      color: Colors.blue[600]!, // Match sidebar color
                    ),
                    title: Text(
                      isArabic ? 'في وقت الصلاة بالضبط' : 'At exact prayer time',
                      style: TextStyle(
                        color: AppColors.textPrimaryLight, // Use consistent dark gray
                        fontWeight: isExactTime ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    onTap: () async {
                      // Context7 MCP: Update to exact time (0 minutes before)
                      await _updateTimingSetting(context, ref, notificationSettings, 0);
                    },
                  ),
                ),

                const SizedBox(height: 2), // Reduced from 4
                // Before prayer time option
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: !isExactTime
                        ? Colors.blue[200]!.withAlpha(100) // Slightly darker blue for selected
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListTile(
                    leading: Icon(
                      !isExactTime ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                      color: Colors.blue[600]!, // Match sidebar color
                    ),
                    title: Text(
                      isArabic ? 'قبل وقت الصلاة' : 'Before prayer time',
                      style: TextStyle(
                        color: AppColors.textPrimaryLight, // Use consistent dark gray
                        fontWeight: !isExactTime ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    onTap: () async {
                      // Context7 MCP: Update to before prayer time (use first available reminder minute or default to 10)
                      const reminderMinutes = 10; // Use default value to avoid dynamic access
                      await _updateTimingSetting(context, ref, notificationSettings, reminderMinutes);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Context7 MCP: Update timing setting through unified provider
  Future<void> _updateTimingSetting(
    BuildContext context,
    WidgetRef ref,
    NotificationSettingsState notificationSettings,
    int reminderMinutes,
  ) async {
    try {
      AppLogger.debug('NotificationTimingSection: Updating timing setting to $reminderMinutes minutes');

      final currentSettings = notificationSettings.prayerSettings;
      final updatedSettings = currentSettings.copyWith(defaultReminderMinutes: [reminderMinutes]);

      await ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(updatedSettings);

      AppLogger.info('NotificationTimingSection: Successfully updated timing setting');
    } on Exception catch (error) {
      AppLogger.error('NotificationTimingSection: Failed to update timing setting - $error');
      // Context7 MCP: Show user-friendly error feedback
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to update notification timing setting'),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
