import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../services/progress_tracking_service.dart';

/// Progress Tracking Widget
///
/// A comprehensive widget for displaying background task progress following
/// Context7 MCP best practices. This widget provides real-time progress updates,
/// status information, and user interaction capabilities.
///
/// Key Features:
/// - Real-time progress visualization with animated progress bars
/// - Status indicators with appropriate colors and icons
/// - Expandable details view with metadata and timing information
/// - User interaction capabilities (cancel, retry)
/// - Context7 MCP Material Design compliance
/// - Accessibility support with semantic labels
///
/// Usage:
/// ```dart
/// ProgressTrackingWidget(
///   progressService: progressService,
///   showCompleted: true,
///   maxItems: 5,
/// )
/// ```
class ProgressTrackingWidget extends StatefulWidget {
  /// The progress tracking service
  final ProgressTrackingService progressService;

  /// Whether to show completed operations
  final bool showCompleted;

  /// Maximum number of items to display
  final int maxItems;

  /// Whether to show as a compact view
  final bool compact;

  /// Optional callback for when user taps on a progress item
  final void Function(ProgressOperation)? onProgressTap;

  /// Optional callback for when user cancels a progress operation
  final void Function(String progressId)? onProgressCancel;

  const ProgressTrackingWidget({
    super.key,
    required this.progressService,
    this.showCompleted = false,
    this.maxItems = 10,
    this.compact = false,
    this.onProgressTap,
    this.onProgressCancel,
  });

  @override
  State<ProgressTrackingWidget> createState() => _ProgressTrackingWidgetState();
}

class _ProgressTrackingWidgetState extends State<ProgressTrackingWidget> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<ProgressUpdate>(
      stream: widget.progressService.progressUpdates,
      builder: (context, snapshot) {
        return FadeTransition(opacity: _fadeAnimation, child: _buildProgressList(context));
      },
    );
  }

  Widget _buildProgressList(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    List<ProgressOperation> operations = widget.progressService.activeProgress;

    if (widget.showCompleted) {
      operations = [
        ...operations,
        ...widget.progressService.progressHistory.where((p) => p.isCompleted).take(widget.maxItems - operations.length),
      ];
    }

    operations = operations.take(widget.maxItems).toList();

    if (operations.isEmpty) {
      return _buildEmptyState(context, l10n, theme);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!widget.compact) _buildHeader(context, l10n, theme),
        ...operations.map((operation) => _buildProgressItem(context, operation, l10n, theme)),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context, AppLocalizations l10n, ThemeData theme) {
    if (widget.compact) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 48,
            color: theme.colorScheme.primary.withValues(alpha: (0.6 * 255).round().toDouble()),
          ),
          const SizedBox(height: 16),
          Text(
            l10n.noActiveOperations,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: (0.6 * 255).round().toDouble()),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, AppLocalizations l10n, ThemeData theme) {
    final activeCount = widget.progressService.activeProgress.length;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(Icons.trending_up, color: theme.colorScheme.primary),
          const SizedBox(width: 8),
          Text(l10n.backgroundOperations, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
          const Spacer(),
          if (activeCount > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: (0.1 * 255).round().toDouble()),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$activeCount ${l10n.active}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProgressItem(BuildContext context, ProgressOperation operation, AppLocalizations l10n, ThemeData theme) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: InkWell(
        onTap: widget.onProgressTap != null ? () => widget.onProgressTap!(operation) : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProgressHeader(context, operation, l10n, theme),
              const SizedBox(height: 12),
              _buildProgressBar(context, operation, theme),
              if (operation.statusMessage != null) ...[
                const SizedBox(height: 8),
                _buildStatusMessage(context, operation, theme),
              ],
              if (!widget.compact && operation.isCompleted) ...[
                const SizedBox(height: 8),
                _buildCompletionInfo(context, operation, l10n, theme),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressHeader(
    BuildContext context,
    ProgressOperation operation,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Row(
      children: [
        _buildStatusIcon(operation, theme),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                operation.title,
                style: theme.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (operation.description != null) ...[
                const SizedBox(height: 2),
                Text(
                  operation.description!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: (0.7 * 255).round().toDouble()),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
        Text(
          '${operation.percentage.toStringAsFixed(0)}%',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: _getStatusColor(operation.status, theme),
          ),
        ),
        if (!operation.isCompleted && widget.onProgressCancel != null) ...[
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.close, size: 20),
            onPressed: () => widget.onProgressCancel!(operation.id),
            tooltip: l10n.cancel,
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
        ],
      ],
    );
  }

  Widget _buildStatusIcon(ProgressOperation operation, ThemeData theme) {
    IconData iconData;
    Color iconColor;

    switch (operation.status) {
      case ProgressStatus.running:
        iconData = _getTypeIcon(operation.type);
        iconColor = theme.colorScheme.primary;
        break;
      case ProgressStatus.paused:
        iconData = Icons.pause_circle_outline;
        iconColor = theme.colorScheme.secondary;
        break;
      case ProgressStatus.completed:
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case ProgressStatus.failed:
        iconData = Icons.error;
        iconColor = theme.colorScheme.error;
        break;
      case ProgressStatus.cancelled:
        iconData = Icons.cancel;
        iconColor = theme.colorScheme.outline;
        break;
    }

    return Icon(iconData, color: iconColor, size: 24);
  }

  IconData _getTypeIcon(ProgressType type) {
    switch (type) {
      case ProgressType.sync:
        return Icons.sync;
      case ProgressType.download:
        return Icons.download;
      case ProgressType.upload:
        return Icons.upload;
      case ProgressType.processing:
        return Icons.settings;
      case ProgressType.backup:
        return Icons.backup;
      case ProgressType.restore:
        return Icons.restore;
    }
  }

  Widget _buildProgressBar(BuildContext context, ProgressOperation operation, ThemeData theme) {
    return Column(
      children: [
        LinearProgressIndicator(
          value: operation.percentage / 100,
          backgroundColor: theme.colorScheme.surfaceVariant,
          valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor(operation.status, theme)),
          minHeight: 6,
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${operation.currentStep} / ${operation.totalSteps}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: (0.6 * 255).round().toDouble()),
              ),
            ),
            Text(
              _formatDuration(operation.duration),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: (0.6 * 255).round().toDouble()),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusMessage(BuildContext context, ProgressOperation operation, ThemeData theme) {
    return Text(
      operation.statusMessage!,
      style: theme.textTheme.bodySmall?.copyWith(
        color: theme.colorScheme.onSurface.withValues(alpha: (0.8 * 255).round().toDouble()),
        fontStyle: FontStyle.italic,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildCompletionInfo(
    BuildContext context,
    ProgressOperation operation,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    final completionTime = operation.endTime != null ? _formatDateTime(operation.endTime!) : '';

    return Row(
      children: [
        Icon(
          Icons.schedule,
          size: 16,
          color: theme.colorScheme.onSurface.withValues(alpha: (0.6 * 255).round().toDouble()),
        ),
        const SizedBox(width: 4),
        Text(
          '${l10n.completed}: $completionTime',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: (0.6 * 255).round().toDouble()),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(ProgressStatus status, ThemeData theme) {
    switch (status) {
      case ProgressStatus.running:
        return theme.colorScheme.primary;
      case ProgressStatus.paused:
        return theme.colorScheme.secondary;
      case ProgressStatus.completed:
        return Colors.green;
      case ProgressStatus.failed:
        return theme.colorScheme.error;
      case ProgressStatus.cancelled:
        return theme.colorScheme.outline;
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// Progress Summary Widget
///
/// A compact widget showing overall progress statistics.
class ProgressSummaryWidget extends StatelessWidget {
  final ProgressTrackingService progressService;

  const ProgressSummaryWidget({super.key, required this.progressService});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final analytics = progressService.analytics;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.progressSummary, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    l10n.active,
                    progressService.activeProgress.length.toString(),
                    Icons.trending_up,
                    theme.colorScheme.primary,
                    theme,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    l10n.completed,
                    analytics.operationsCompleted.toString(),
                    Icons.check_circle,
                    Colors.green,
                    theme,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    l10n.successRate,
                    '${(analytics.successRate * 100).toStringAsFixed(0)}%',
                    Icons.analytics,
                    theme.colorScheme.secondary,
                    theme,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color, ThemeData theme) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold, color: color),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: (0.7 * 255).round().toDouble()),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
