import 'package:flutter/material.dart';

import '../../../generated/l10n/app_localizations.dart';

/// Consistent loading states for lazy-loaded features.
///
/// This provides a unified set of loading UI components for
/// deferred features to ensure consistent user experience.
///
/// Based on Flutter 2025 best practices for loading states.

/// Generic lazy loading widget with customizable states
class LazyLoadingWidget extends StatelessWidget {
  final LazyLoadingState state;
  final String? loadingMessage;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final Widget? child;
  final Widget? customLoadingWidget;
  final Widget? customErrorWidget;

  const LazyLoadingWidget({
    super.key,
    required this.state,
    this.loadingMessage,
    this.errorMessage,
    this.onRetry,
    this.child,
    this.customLoadingWidget,
    this.customErrorWidget,
  });

  @override
  Widget build(BuildContext context) {
    switch (state) {
      case LazyLoadingState.loading:
        return customLoadingWidget ?? _buildLoadingWidget(context);
      case LazyLoadingState.loaded:
        return child ?? const SizedBox.shrink();
      case LazyLoadingState.error:
        return customErrorWidget ?? _buildErrorWidget(context);
      case LazyLoadingState.notLoaded:
        return const SizedBox.shrink();
    }
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              loadingMessage ?? AppLocalizations.of(context).loading,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: (0.7 * 255).round().toDouble()),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, size: 48, color: Theme.of(context).colorScheme.error),
            const SizedBox(height: 16),
            Text(
              errorMessage ?? 'Failed to load feature',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.error),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 16),
              ElevatedButton(onPressed: onRetry, child: const Text('Retry')),
            ],
          ],
        ),
      ),
    );
  }
}

/// Compact loading widget for smaller spaces
class CompactLazyLoadingWidget extends StatelessWidget {
  final LazyLoadingState state;
  final String? loadingMessage;
  final Widget? child;

  const CompactLazyLoadingWidget({super.key, required this.state, this.loadingMessage, this.child});

  @override
  Widget build(BuildContext context) {
    switch (state) {
      case LazyLoadingState.loading:
        return Container(
          padding: const EdgeInsets.all(8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)),
              const SizedBox(width: 8),
              Text(loadingMessage ?? 'Loading...', style: Theme.of(context).textTheme.bodySmall),
            ],
          ),
        );
      case LazyLoadingState.loaded:
        return child ?? const SizedBox.shrink();
      case LazyLoadingState.error:
        return Container(
          padding: const EdgeInsets.all(8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline, size: 16, color: Theme.of(context).colorScheme.error),
              const SizedBox(width: 8),
              Text(
                'Failed to load',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.error),
              ),
            ],
          ),
        );
      case LazyLoadingState.notLoaded:
        return const SizedBox.shrink();
    }
  }
}

/// Skeleton loading widget for content placeholders
class SkeletonLoadingWidget extends StatefulWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final int lineCount;
  final double lineHeight;
  final double lineSpacing;

  const SkeletonLoadingWidget({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    this.lineCount = 3,
    this.lineHeight = 16,
    this.lineSpacing = 8,
  });

  @override
  State<SkeletonLoadingWidget> createState() => _SkeletonLoadingWidgetState();
}

class _SkeletonLoadingWidgetState extends State<SkeletonLoadingWidget> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 1500), vsync: this);
    _animation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: List.generate(widget.lineCount, (index) {
              final isLast = index == widget.lineCount - 1;
              final lineWidth = isLast ? 0.7 : 1.0;

              return Container(
                margin: EdgeInsets.only(bottom: isLast ? 0 : widget.lineSpacing),
                child: Container(
                  width: widget.width != null ? widget.width! * lineWidth : null,
                  height: widget.lineHeight,
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: (0.1 * _animation.value * 255).round().toDouble()),
                    borderRadius: widget.borderRadius ?? BorderRadius.circular(4),
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }
}

/// Shimmer loading effect widget
class ShimmerLoadingWidget extends StatefulWidget {
  final Widget child;
  final Color? baseColor;
  final Color? highlightColor;

  const ShimmerLoadingWidget({super.key, required this.child, this.baseColor, this.highlightColor});

  @override
  State<ShimmerLoadingWidget> createState() => _ShimmerLoadingWidgetState();
}

class _ShimmerLoadingWidgetState extends State<ShimmerLoadingWidget> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 1500), vsync: this);
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final baseColor = widget.baseColor ?? Theme.of(context).colorScheme.surfaceVariant;
    final highlightColor = widget.highlightColor ?? Theme.of(context).colorScheme.surface;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [baseColor, highlightColor, baseColor],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

/// Loading states enum
enum LazyLoadingState { notLoaded, loading, loaded, error }

/// Predefined loading widgets for common use cases
class LazyLoadingWidgets {
  /// Map loading widget
  static Widget mapLoading({String? message}) {
    return Container(
      decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface, borderRadius: BorderRadius.circular(8)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.map_outlined, size: 48, color: Colors.grey),
          const SizedBox(height: 16),
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(message ?? 'Loading map...', style: const TextStyle(fontSize: 16, color: Colors.grey)),
        ],
      ),
    );
  }

  /// Audio loading widget
  static Widget audioLoading({String? message}) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Icon(Icons.audio_file_outlined, size: 24, color: Colors.grey),
          const SizedBox(width: 12),
          const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)),
          const SizedBox(width: 12),
          Text(message ?? 'Loading audio...', style: const TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  /// Feature loading widget
  static Widget featureLoading({required String featureName, String? message}) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              message ?? 'Loading $featureName...',
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
