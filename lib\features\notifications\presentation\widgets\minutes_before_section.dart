import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Context7 MCP: Use unified notification provider for consolidated settings management
import '../../../../core/notifications/providers/unified_notification_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/l10n.dart';
import '../../../../core/utils/logger.dart';

/// Minutes before section widget
class MinutesBeforeSection extends ConsumerWidget {
  /// Whether the current language is Arabic
  final bool isArabic;

  /// Whether dark mode is enabled
  final bool isDarkMode;

  /// Constructor
  const MinutesBeforeSection({super.key, required this.isArabic, required this.isDarkMode});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Context7 MCP: Get notification settings from unified provider
    final notificationSettingsAsync = ref.watch(unifiedNotificationSettingsProvider);

    return notificationSettingsAsync.when(
      loading: () => _buildLoadingState(),
      error: (error, stackTrace) {
        AppLogger.error('MinutesBeforeSection: Failed to load notification settings - $error');
        return _buildErrorState(context, error);
      },
      data: (notificationSettings) => _buildMinutesSection(context, ref, notificationSettings),
    );
  }

  /// Context7 MCP: Build loading state widget
  Widget _buildLoadingState() {
    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue[50]!, Colors.blue[100]!],
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [BoxShadow(color: Colors.black.withAlpha(15), blurRadius: 3, offset: const Offset(0, 2))],
      ),
      child: const Padding(
        padding: EdgeInsets.all(12.0),
        child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
      ),
    );
  }

  /// Context7 MCP: Build error state widget
  Widget _buildErrorState(BuildContext context, Object error) {
    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.red[50]!, Colors.red[100]!],
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [BoxShadow(color: Colors.black.withAlpha(15), blurRadius: 3, offset: const Offset(0, 2))],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red[600], size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Error loading notification settings',
                style: TextStyle(color: Colors.red[700], fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Context7 MCP: Build the main minutes section widget
  Widget _buildMinutesSection(BuildContext context, WidgetRef ref, NotificationSettingsState notificationSettings) {
    // Get the default reminder minutes from prayer settings
    final defaultReminderMinutes = notificationSettings.prayerSettings.defaultReminderMinutes.isNotEmpty
        ? notificationSettings.prayerSettings.defaultReminderMinutes.first
        : 10;

    return DecoratedBox(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue[50]!, Colors.blue[100]!], // Match sidebar gradient
        ),
        borderRadius: BorderRadius.circular(8), // Match sidebar border radius
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15), // Match sidebar shadow
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0), // Reduced from 16.0
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: Colors.blue[600]!, // Match sidebar icon color
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  context.l10n?.notifyBeforePrayer ?? 'Notify before prayer',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimaryLight, // Match sidebar text color
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              initialValue: defaultReminderMinutes,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: AppColors.primary.withValues(
                      red: AppColors.primary.r,
                      green: AppColors.primary.g,
                      blue: AppColors.primary.b,
                      alpha: 77, // 0.3 * 255 = ~77
                    ),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.grey(300)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: Colors.blue[600]!, // Match sidebar color
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: isDarkMode ? AppColors.surfaceDarkGray : AppColors.surfaceLightGray,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              icon: Icon(
                Icons.arrow_drop_down,
                color: Colors.blue[600]!, // Match sidebar color
              ),
              dropdownColor: isDarkMode ? AppColors.surfaceDarkGray : AppColors.surfaceLightGray,
              items: [5, 10, 15, 20, 30].map((value) {
                return DropdownMenuItem<int>(
                  value: value,
                  child: Text(
                    isArabic ? 'قبل $value دقائق' : '$value minutes before',
                    style: TextStyle(fontWeight: value == defaultReminderMinutes ? FontWeight.w600 : FontWeight.normal),
                  ),
                );
              }).toList(),
              onChanged: (newValue) async {
                if (newValue != null) {
                  // Context7 MCP: Update default reminder minutes through unified provider
                  try {
                    AppLogger.debug('MinutesBeforeSection: Updating default reminder minutes to $newValue');

                    final currentSettings = notificationSettings.prayerSettings;
                    final updatedSettings = currentSettings.copyWith(defaultReminderMinutes: [newValue]);

                    await ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(updatedSettings);

                    AppLogger.info('MinutesBeforeSection: Successfully updated default reminder minutes');
                  } on Exception catch (error) {
                    AppLogger.error('MinutesBeforeSection: Failed to update reminder minutes - $error');
                    // Context7 MCP: Show user-friendly error feedback
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('Failed to update reminder time setting'),
                          backgroundColor: Colors.red[600],
                          duration: const Duration(seconds: 3),
                        ),
                      );
                    }
                  }
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
