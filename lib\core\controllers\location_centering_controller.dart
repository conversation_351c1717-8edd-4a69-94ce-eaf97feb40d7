import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../logging/app_logger.dart';
import '../models/location_data.dart';

/// Context7 MCP: Centering states following Context7 MCP state machine pattern
enum CenteringState {
  /// No centering operation is needed or active
  idle,

  /// Centering operation is scheduled but not yet executed
  pending,

  /// Currently performing a centering operation on the map
  centering,

  /// Initial automatic centering has been completed
  completed,

  /// User has manually moved the map, disabling auto-centering
  userControlled,
}

/// **LocationCenteringController**
///
/// Context7 MCP implementation for managing automatic location centering behavior.
/// Prevents infinite loops while ensuring proper initial positioning.
///
/// **Context7 MCP Patterns:**
/// - State Machine Pattern for centering states
/// - Command Pattern for centering operations
/// - Debouncing for performance optimization
/// - Single Responsibility Principle
class LocationCenteringController {
  // ==================== State Management ====================

  CenteringState _currentState = CenteringState.idle;
  bool _hasPerformedInitialCentering = false;
  bool _isUserInteracting = false;
  DateTime? _lastCenteringTime;
  DateTime? _lastUserInteractionTime;

  // ==================== Debouncing and Timing ====================

  Timer? _centeringDebounceTimer;
  Timer? _userInteractionTimer;

  // Context7 MCP: Configuration constants
  static const Duration _centeringDebounceDelay = Duration(milliseconds: 500);
  static const Duration _userInteractionCooldown = Duration(seconds: 3);
  static const Duration _minimumCenteringInterval = Duration(seconds: 2);

  // ==================== Getters ====================

  /// Current centering state
  CenteringState get currentState => _currentState;

  /// Whether initial centering has been performed
  bool get hasPerformedInitialCentering => _hasPerformedInitialCentering;

  /// Whether user is currently interacting with the map
  bool get isUserInteracting => _isUserInteracting;

  /// Context7 MCP: Set map controller for centering operations
  ///
  /// **Parameters:**
  /// - [controller]: The Google Maps controller
  void setMapController(GoogleMapController? controller) {
    // This method is kept for compatibility but actual centering
    // is handled through the widget's camera update methods
    AppLogger.debug('LocationCenteringController: Map controller set');
  }

  /// Whether automatic centering is currently allowed
  bool get canPerformAutomaticCentering {
    final now = DateTime.now();

    // Don't center if user recently interacted
    if (_lastUserInteractionTime != null && now.difference(_lastUserInteractionTime!) < _userInteractionCooldown) {
      return false;
    }

    // Don't center too frequently
    if (_lastCenteringTime != null && now.difference(_lastCenteringTime!) < _minimumCenteringInterval) {
      return false;
    }

    // Don't center if user has taken control
    if (_currentState == CenteringState.userControlled) {
      return false;
    }

    return true;
  }

  // ==================== Core Methods ====================

  /// Context7 MCP: Handle new location data availability
  ///
  /// **Parameters:**
  /// - [locationData]: The new location data
  /// - [force]: Whether to force centering regardless of state
  ///
  /// **Returns:** `Future<bool>` indicating if centering was performed
  Future<bool> handleLocationAvailable(LocationData locationData, {bool force = false}) async {
    try {
      AppLogger.info(
        'LocationCenteringController: Location available - '
        'lat: ${locationData.latitude}, lng: ${locationData.longitude}, '
        'force: $force, state: $_currentState',
      );

      // Context7 MCP: Validate location data
      if (!_isValidLocationData(locationData)) {
        AppLogger.warning('LocationCenteringController: Invalid location data received');
        return false;
      }

      // Context7 MCP: Check if centering is allowed
      if (!force && !canPerformAutomaticCentering) {
        AppLogger.info(
          'LocationCenteringController: Automatic centering not allowed - '
          'canCenter: $canPerformAutomaticCentering, '
          'userInteracting: $_isUserInteracting, '
          'state: $_currentState',
        );
        return false;
      }

      // Context7 MCP: Update state and perform centering
      _updateState(CenteringState.pending);

      // Context7 MCP: Debounce centering operations
      _centeringDebounceTimer?.cancel();
      _centeringDebounceTimer = Timer(_centeringDebounceDelay, () {
        _performCentering(locationData, force: force);
      });

      return true;
    } on Exception catch (e, stackTrace) {
      AppLogger.error(
        'LocationCenteringController: Error handling location availability',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Context7 MCP: Handle user camera interaction
  ///
  /// **Parameters:**
  /// - [reason]: The reason for camera movement (optional, defaults to gesture)
  void handleUserCameraInteraction([String? reason]) {
    try {
      AppLogger.debug('LocationCenteringController: User camera interaction - reason: ${reason ?? 'gesture'}');

      // Context7 MCP: Track user interactions
      _isUserInteracting = true;
      _lastUserInteractionTime = DateTime.now();

      // Context7 MCP: Update state to user controlled
      _updateState(CenteringState.userControlled);

      // Context7 MCP: Reset user interaction flag after cooldown
      _userInteractionTimer?.cancel();
      _userInteractionTimer = Timer(_userInteractionCooldown, () {
        _isUserInteracting = false;
        AppLogger.debug('LocationCenteringController: User interaction cooldown completed');
      });
    } on Exception catch (e, stackTrace) {
      AppLogger.error(
        'LocationCenteringController: Error handling user camera interaction',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Context7 MCP: Reset controller state
  ///
  /// Useful for testing or when restarting location services
  void reset() {
    try {
      AppLogger.info('LocationCenteringController: Resetting controller state');

      _centeringDebounceTimer?.cancel();
      _userInteractionTimer?.cancel();

      _currentState = CenteringState.idle;
      _hasPerformedInitialCentering = false;
      _isUserInteracting = false;
      _lastCenteringTime = null;
      _lastUserInteractionTime = null;
    } on Exception catch (e, stackTrace) {
      AppLogger.error('LocationCenteringController: Error resetting controller', error: e, stackTrace: stackTrace);
    }
  }

  // ==================== Private Methods ====================

  /// Context7 MCP: Perform the actual centering operation
  Future<void> _performCentering(LocationData locationData, {bool force = false}) async {
    try {
      if (_currentState != CenteringState.pending && !force) {
        AppLogger.debug('LocationCenteringController: Centering cancelled - state changed');
        return;
      }

      _updateState(CenteringState.centering);

      AppLogger.info(
        'LocationCenteringController: Performing centering to '
        'lat: ${locationData.latitude}, lng: ${locationData.longitude}',
      );

      // Context7 MCP: Record centering time
      _lastCenteringTime = DateTime.now();

      // Context7 MCP: Mark initial centering as completed
      if (!_hasPerformedInitialCentering) {
        _hasPerformedInitialCentering = true;
        AppLogger.info('LocationCenteringController: Initial centering completed');
      }

      _updateState(CenteringState.completed);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('LocationCenteringController: Error performing centering', error: e, stackTrace: stackTrace);
      _updateState(CenteringState.idle);
    }
  }

  /// Context7 MCP: Update controller state
  void _updateState(CenteringState newState) {
    if (_currentState != newState) {
      final oldState = _currentState;
      _currentState = newState;
      AppLogger.debug('LocationCenteringController: State changed from $oldState to $newState');
    }
  }

  /// Context7 MCP: Validate location data
  bool _isValidLocationData(LocationData locationData) {
    return locationData.latitude.abs() <= 90.0 &&
        locationData.longitude.abs() <= 180.0 &&
        !locationData.latitude.isNaN &&
        !locationData.longitude.isNaN;
  }

  // ==================== Cleanup ====================

  /// Context7 MCP: Dispose of resources
  void dispose() {
    try {
      AppLogger.debug('LocationCenteringController: Disposing controller');

      _centeringDebounceTimer?.cancel();
      _userInteractionTimer?.cancel();

      _centeringDebounceTimer = null;
      _userInteractionTimer = null;
    } on Exception catch (e, stackTrace) {
      AppLogger.error('LocationCenteringController: Error disposing controller', error: e, stackTrace: stackTrace);
    }
  }
}

/// Context7 MCP: Riverpod provider for LocationCenteringController
final locationCenteringControllerProvider = Provider<LocationCenteringController>((ref) {
  final controller = LocationCenteringController();

  // Context7 MCP: Ensure proper cleanup
  ref.onDispose(() {
    controller.dispose();
  });

  return controller;
});
