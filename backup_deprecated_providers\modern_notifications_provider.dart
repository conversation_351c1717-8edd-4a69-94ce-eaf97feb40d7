import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:masajid_albahrain/core/utils/result.dart';
import 'package:masajid_albahrain/features/notifications/data/datasources/notifications_local_datasource.dart';
import 'package:masajid_albahrain/features/notifications/data/datasources/notifications_local_datasource_impl.dart';
import 'package:masajid_albahrain/features/notifications/data/repositories/notifications_repository_impl.dart';

import 'package:masajid_albahrain/features/notifications/domain/entities/notification_settings.dart';
import 'package:masajid_albahrain/features/notifications/domain/repositories/notifications_repository.dart';
import 'package:masajid_albahrain/features/notifications/domain/usecases/get_notification_settings_usecase.dart';
import 'package:masajid_albahrain/features/notifications/domain/usecases/manage_notification_permissions_usecase.dart';
import 'package:masajid_albahrain/features/notifications/domain/usecases/save_notification_settings_usecase.dart';

part 'modern_notifications_provider.g.dart';

/// Provider for notifications local data source
@riverpod
NotificationsLocalDataSource notificationsLocalDataSource(Ref ref) {
  return NotificationsLocalDataSourceImpl();
}

/// Provider for notifications repository
@riverpod
NotificationsRepository notificationsRepository(Ref ref) {
  final localDataSource = ref.watch(notificationsLocalDataSourceProvider);
  return NotificationsRepositoryImpl(localDataSource: localDataSource);
}

/// Provider for get notification settings use case
@riverpod
GetNotificationSettingsUseCase getNotificationSettingsUseCase(Ref ref) {
  final repository = ref.watch(notificationsRepositoryProvider);
  return GetNotificationSettingsUseCase(repository: repository);
}

/// Provider for save notification settings use case
@riverpod
SaveNotificationSettingsUseCase saveNotificationSettingsUseCase(Ref ref) {
  final repository = ref.watch(notificationsRepositoryProvider);
  return SaveNotificationSettingsUseCase(repository: repository);
}

/// Provider for manage notification permissions use case
@riverpod
ManageNotificationPermissionsUseCase manageNotificationPermissionsUseCase(Ref ref) {
  final repository = ref.watch(notificationsRepositoryProvider);
  return ManageNotificationPermissionsUseCase(repository: repository);
}

/// Modern AsyncNotifier for managing notification settings
///
/// This provider follows the latest Riverpod patterns using AsyncNotifier
/// for reactive state management with automatic loading states, error handling,
/// and optimistic updates.
///
/// **Features:**
/// - Automatic loading and error states
/// - Reactive updates from repository
/// - Type-safe state management
/// - Built-in retry mechanisms
/// - Performance optimizations
/// - Permission management integration
@riverpod
class ModernNotificationSettings extends _$ModernNotificationSettings {
  /// Initialize the notification settings state
  ///
  /// This method is called automatically when the provider is first accessed.
  /// It loads the current notification settings and returns the initial state.
  @override
  Future<NotificationSettings> build() async {
    try {
      final useCase = ref.read(getNotificationSettingsUseCaseProvider);
      final result = await useCase.call();

      if (result.isSuccess) {
        final settings = result.valueOrNull!;
        debugPrint('ModernNotificationSettingsProvider: Loaded settings successfully');
        return settings;
      } else {
        debugPrint('ModernNotificationSettingsProvider: Error loading settings: ${result.errorOrNull}');
        throw Exception(result.errorOrNull!.message);
      }
    } on Exception catch (e) {
      debugPrint('ModernNotificationSettingsProvider: Exception in build: $e');
      throw Exception('Failed to load notification settings: $e');
    }
  }

  /// Update notification settings
  ///
  /// [settings] - The new notification settings to save
  /// [rescheduleNotifications] - Whether to reschedule notifications after saving
  ///
  /// Returns true if successful, false otherwise
  Future<bool> updateSettings(NotificationSettings settings, {bool rescheduleNotifications = true}) async {
    try {
      final useCase = ref.read(saveNotificationSettingsUseCaseProvider);
      final result = await useCase.call(settings, rescheduleNotifications: rescheduleNotifications);

      if (result.isSuccess) {
        // Optimistically update the state
        state = AsyncData(settings);

        debugPrint('ModernNotificationSettingsProvider: Updated settings successfully');
        return true;
      } else {
        debugPrint('ModernNotificationSettingsProvider: Error updating settings: ${result.errorOrNull}');
        return false;
      }
    } on Exception catch (e) {
      debugPrint('ModernNotificationSettingsProvider: Exception updating settings: $e');
      return false;
    }
  }

  /// Toggle master notification setting
  ///
  /// Returns the new notification enabled state
  Future<bool> toggleNotifications() async {
    final currentSettings = state.when(
      data: (settings) => settings,
      loading: () => null,
      error: (error, stackTrace) => null,
    );
    if (currentSettings == null) return false;

    final newSettings = currentSettings.copyWith(notificationsEnabled: !currentSettings.notificationsEnabled);

    final success = await updateSettings(newSettings);
    return success ? newSettings.notificationsEnabled : currentSettings.notificationsEnabled;
  }

  /// Toggle prayer notification for a specific prayer
  ///
  /// [prayerName] - The name of the prayer to toggle
  ///
  /// Returns the new enabled state for the prayer
  Future<bool> togglePrayerNotification(String prayerName) async {
    final currentSettings = state.when(
      data: (settings) => settings,
      loading: () => null,
      error: (error, stackTrace) => null,
    );
    if (currentSettings == null) return false;

    final currentPrayerSettings = Map<String, bool>.from(currentSettings.prayerNotificationsEnabled);
    final currentEnabled = currentPrayerSettings[prayerName] ?? false;
    currentPrayerSettings[prayerName] = !currentEnabled;

    final newSettings = currentSettings.copyWith(prayerNotificationsEnabled: currentPrayerSettings);

    final success = await updateSettings(newSettings);
    return success ? !currentEnabled : currentEnabled;
  }

  /// Update minutes before prayer time
  ///
  /// [minutes] - Number of minutes before prayer time to notify
  ///
  /// Returns true if successful
  Future<bool> updateMinutesBefore(int minutes) async {
    final currentSettings = state.when(
      data: (settings) => settings,
      loading: () => null,
      error: (error, stackTrace) => null,
    );
    if (currentSettings == null) return false;

    // Validate minutes
    if (minutes < 0 || minutes > 120) {
      debugPrint('ModernNotificationSettingsProvider: Invalid minutes value: $minutes');
      return false;
    }

    final newSettings = currentSettings.copyWith(minutesBefore: minutes);
    return updateSettings(newSettings);
  }

  /// Update notification sound
  ///
  /// [sound] - The new notification sound preference
  ///
  /// Returns true if successful
  Future<bool> updateNotificationSound(NotificationSound sound) async {
    final currentSettings = state.when(
      data: (settings) => settings,
      loading: () => null,
      error: (error, stackTrace) => null,
    );
    if (currentSettings == null) return false;

    final newSettings = currentSettings.copyWith(notificationSound: sound);
    return updateSettings(newSettings);
  }

  /// Toggle vibration setting
  ///
  /// Returns the new vibration enabled state
  Future<bool> toggleVibration() async {
    final currentSettings = state.when(
      data: (settings) => settings,
      loading: () => null,
      error: (error, stackTrace) => null,
    );
    if (currentSettings == null) return false;

    final newSettings = currentSettings.copyWith(useVibration: !currentSettings.useVibration);

    final success = await updateSettings(newSettings);
    return success ? newSettings.useVibration : currentSettings.useVibration;
  }

  /// Update Do Not Disturb settings
  ///
  /// [enabled] - Whether Do Not Disturb is enabled
  /// [startTime] - Start time for Do Not Disturb (24-hour format)
  /// [endTime] - End time for Do Not Disturb (24-hour format)
  /// [days] - Days when Do Not Disturb is active
  ///
  /// Returns true if successful
  Future<bool> updateDoNotDisturb({bool? enabled, String? startTime, String? endTime, List<int>? days}) async {
    final currentSettings = state.when(
      data: (settings) => settings,
      loading: () => null,
      error: (error, stackTrace) => null,
    );
    if (currentSettings == null) return false;

    final newSettings = currentSettings.copyWith(
      enableDoNotDisturb: enabled ?? currentSettings.enableDoNotDisturb,
      doNotDisturbStart: startTime ?? currentSettings.doNotDisturbStart,
      doNotDisturbEnd: endTime ?? currentSettings.doNotDisturbEnd,
      doNotDisturbDays: days ?? currentSettings.doNotDisturbDays,
    );

    return updateSettings(newSettings);
  }

  /// Reset settings to defaults
  ///
  /// Returns true if successful
  Future<bool> resetToDefaults() async {
    try {
      final repository = ref.read(notificationsRepositoryProvider);
      final result = await repository.resetNotificationSettings();

      if (result.isSuccess) {
        final defaultSettings = result.valueOrNull!;
        state = AsyncData(defaultSettings);

        debugPrint('ModernNotificationSettingsProvider: Reset to defaults successfully');
        return true;
      } else {
        debugPrint('ModernNotificationSettingsProvider: Error resetting to defaults: ${result.errorOrNull}');
        return false;
      }
    } on Exception catch (e) {
      debugPrint('ModernNotificationSettingsProvider: Exception resetting to defaults: $e');
      return false;
    }
  }

  /// Refresh settings from storage
  ///
  /// This method reloads settings from the repository
  Future<void> refresh() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      final useCase = ref.read(getNotificationSettingsUseCaseProvider);
      final result = await useCase.call();

      if (result.isSuccess) {
        return result.valueOrNull!;
      } else {
        throw Exception(result.errorOrNull!.message);
      }
    });
  }

  /// Apply preset configuration
  ///
  /// [preset] - The preset configuration to apply
  ///
  /// Returns true if successful
  Future<bool> applyPreset(NotificationPreset preset) async {
    NotificationSettings newSettings;

    switch (preset) {
      case NotificationPreset.minimal:
        newSettings = NotificationSettings.minimalSettings;
        break;
      case NotificationPreset.maximum:
        newSettings = NotificationSettings.maximumSettings;
        break;
      case NotificationPreset.defaultPreset:
        newSettings = NotificationSettings.defaultSettings;
        break;
    }

    return updateSettings(newSettings);
  }
}

/// Modern AsyncNotifier for managing notification permissions
///
/// This provider manages notification permissions and provides reactive
/// updates when permission status changes.
@riverpod
class ModernNotificationPermissions extends _$ModernNotificationPermissions {
  @override
  Future<PermissionStatus> build() async {
    try {
      final useCase = ref.read(manageNotificationPermissionsUseCaseProvider);
      final result = await useCase.checkAllPermissions();

      if (result.isSuccess) {
        return result.valueOrNull!;
      } else {
        debugPrint('ModernNotificationPermissionsProvider: Error checking permissions: ${result.errorOrNull}');
        throw Exception(result.errorOrNull!.message);
      }
    } on Exception catch (e) {
      debugPrint('ModernNotificationPermissionsProvider: Exception in build: $e');
      throw Exception('Failed to check notification permissions: $e');
    }
  }

  /// Request all necessary permissions
  ///
  /// Returns the final permission status after requesting
  Future<PermissionStatus> requestAllPermissions() async {
    try {
      final useCase = ref.read(manageNotificationPermissionsUseCaseProvider);
      final result = await useCase.requestAllPermissions();

      if (result.isSuccess) {
        final status = result.valueOrNull!;
        state = AsyncData(status);

        // Update settings with new permission status
        ref.invalidate(modernNotificationSettingsProvider);

        return status;
      } else {
        debugPrint('ModernNotificationPermissionsProvider: Error requesting permissions: ${result.errorOrNull}');
        throw Exception(result.errorOrNull!.message);
      }
    } on Exception catch (e) {
      debugPrint('ModernNotificationPermissionsProvider: Exception requesting permissions: $e');
      throw Exception('Failed to request permissions: $e');
    }
  }

  /// Request only notification permissions
  ///
  /// Returns true if permissions were granted
  Future<bool> requestNotificationPermissions() async {
    try {
      final useCase = ref.read(manageNotificationPermissionsUseCaseProvider);
      final result = await useCase.requestNotificationPermissions();

      if (result.isSuccess) {
        final granted = result.valueOrNull!;

        // Refresh permission status
        await refresh();

        return granted;
      } else {
        debugPrint(
          'ModernNotificationPermissionsProvider: Error requesting notification permissions: ${result.errorOrNull}',
        );
        return false;
      }
    } on Exception catch (e) {
      debugPrint('ModernNotificationPermissionsProvider: Exception requesting notification permissions: $e');
      return false;
    }
  }

  /// Refresh permission status
  Future<void> refresh() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      final useCase = ref.read(manageNotificationPermissionsUseCaseProvider);
      final result = await useCase.checkAllPermissions();

      if (result.isSuccess) {
        return result.valueOrNull!;
      } else {
        throw Exception(result.errorOrNull!.message);
      }
    });
  }
}

/// Notification preset options
enum NotificationPreset {
  /// Minimal notifications (only essential prayers)
  minimal,

  /// Maximum notifications (all prayers with extras)
  maximum,

  /// Default balanced configuration
  defaultPreset,
}
