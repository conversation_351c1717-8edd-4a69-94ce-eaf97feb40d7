import 'package:freezed_annotation/freezed_annotation.dart';

part 'mfa_models.freezed.dart';

/// Multi-Factor Authentication models following Context7 MCP best practices
///
/// Provides comprehensive MFA support including TOTP, SMS, Email, and Biometric
/// authentication factors with enterprise-grade security features.

/// MFA Factor types supported by the system
@freezed
abstract class MfaFactor with _$MfaFactor {
  /// Time-based One-Time Password (TOTP) factor
  const factory MfaFactor.totp({
    required String id,
    required String secret,
    required String qrCodeUrl,
    required String issuer,
    required String accountName,
    @Default(30) int period,
    @Default(6) int digits,
    @Default('SHA1') String algorithm,
    DateTime? enrolledAt,
    DateTime? lastUsed,
    @Default(true) bool isActive,
  }) = MfaFactorTotp;

  /// SMS-based factor
  const factory MfaFactor.sms({
    required String id,
    required String phoneNumber,
    String? maskedPhoneNumber,
    DateTime? enrolledAt,
    DateTime? lastUsed,
    @Default(true) bool isActive,
    @Default(300) int codeExpirySeconds,
  }) = MfaFactorSms;

  /// Email-based factor
  const factory MfaFactor.email({
    required String id,
    required String email,
    String? maskedEmail,
    DateTime? enrolledAt,
    DateTime? lastUsed,
    @Default(true) bool isActive,
    @Default(300) int codeExpirySeconds,
  }) = MfaFactorEmail;

  /// Biometric authentication factor
  const factory MfaFactor.biometric({
    required String id,
    required BiometricType type,
    required String deviceId,
    String? displayName,
    DateTime? enrolledAt,
    DateTime? lastUsed,
    @Default(true) bool isActive,
  }) = MfaFactorBiometric;

  /// Hardware security key factor (FIDO2/WebAuthn)
  const factory MfaFactor.securityKey({
    required String id,
    required String credentialId,
    required String publicKey,
    String? displayName,
    DateTime? enrolledAt,
    DateTime? lastUsed,
    @Default(true) bool isActive,
  }) = MfaFactorSecurityKey;

  /// Backup codes factor
  const factory MfaFactor.backupCodes({
    required String id,
    required List<String> codes,
    required int remainingCodes,
    DateTime? enrolledAt,
    DateTime? lastUsed,
    @Default(true) bool isActive,
  }) = MfaFactorBackupCodes;
}

/// Biometric authentication types
enum BiometricType {
  /// Fingerprint authentication
  fingerprint,

  /// Face ID authentication
  faceId,

  /// Voice recognition authentication
  voiceRecognition,

  /// Iris scan authentication
  iris,

  /// Palm print authentication
  palm,
}

/// MFA Challenge for verification
@freezed
abstract class MfaChallenge with _$MfaChallenge {
  /// Creates an MFA challenge for user verification
  ///
  /// Contains all necessary information for conducting multi-factor
  /// authentication challenges including available factors and timing.
  const factory MfaChallenge({
    required String id,
    required String userId,
    required List<MfaFactor> availableFactors,
    required DateTime createdAt,
    required DateTime expiresAt,
    int? attemptsRemaining,
    String? sessionId,
    Map<String, dynamic>? metadata,
  }) = _MfaChallenge;
}

/// MFA verification request
@freezed
abstract class MfaVerificationRequest with _$MfaVerificationRequest {
  /// Creates an MFA verification request
  ///
  /// Used to verify a user's response to an MFA challenge with the
  /// provided verification code and factor information.
  const factory MfaVerificationRequest({
    required String challengeId,
    required String factorId,
    required String code,
    String? deviceId,
    Map<String, dynamic>? metadata,
  }) = _MfaVerificationRequest;
}

/// MFA enrollment request
@freezed
class MfaEnrollmentRequest with _$MfaEnrollmentRequest {
  /// Creates a TOTP enrollment request
  const factory MfaEnrollmentRequest.totp({required String secret, required String code, String? displayName}) =
      MfaEnrollmentRequestTotp;

  /// Creates an SMS enrollment request
  const factory MfaEnrollmentRequest.sms({required String phoneNumber, required String verificationCode}) =
      MfaEnrollmentRequestSms;

  /// Creates an email enrollment request
  const factory MfaEnrollmentRequest.email({required String email, required String verificationCode}) =
      MfaEnrollmentRequestEmail;

  /// Creates a biometric enrollment request
  const factory MfaEnrollmentRequest.biometric({
    required BiometricType type,
    required String deviceId,
    required String biometricData,
    String? displayName,
  }) = MfaEnrollmentRequestBiometric;

  /// Creates a security key enrollment request
  const factory MfaEnrollmentRequest.securityKey({
    required String credentialId,
    required String publicKey,
    required String attestation,
    String? displayName,
  }) = MfaEnrollmentRequestSecurityKey;
}

/// MFA configuration settings
@freezed
/// Configuration settings for Multi-Factor Authentication (MFA) system.
///
/// This class defines the comprehensive configuration for MFA including
/// enabled factors, security policies, and user experience settings.
///
/// Example usage:
/// ```dart
/// final mfaConfig = MfaConfiguration(
///   isEnabled: true,
///   allowedFactors: [MfaFactorType.totp, MfaFactorType.sms],
///   minimumFactors: 2,
/// );
/// ```
abstract class MfaConfiguration with _$MfaConfiguration {
  /// Creates an MFA configuration with the specified settings.
  ///
  /// [isEnabled] - Whether MFA is enabled for the system
  /// [isRequired] - Whether MFA is mandatory for all users
  /// [allowedFactors] - List of permitted MFA factor types
  /// [minimumFactors] - Minimum number of factors required
  /// [maxFactors] - Maximum number of factors allowed per user
  /// [challengeExpirySeconds] - Time in seconds before MFA challenge expires
  /// [maxAttempts] - Maximum failed attempts before lockout
  /// [lockoutDurationSeconds] - Duration of lockout in seconds
  /// [allowBackupCodes] - Whether backup recovery codes are permitted
  const factory MfaConfiguration({
    @Default(true) bool isEnabled,
    @Default(true) bool isRequired,
    @Default([MfaFactorType.totp, MfaFactorType.sms]) List<MfaFactorType> allowedFactors,
    @Default(1) int minimumFactors,
    @Default(5) int maxFactors,
    @Default(300) int challengeExpirySeconds,
    @Default(3) int maxAttempts,
    @Default(900) int lockoutDurationSeconds,
    @Default(true) bool allowBackupCodes,
    @Default(10) int backupCodesCount,
  }) = _MfaConfiguration;
}

/// Multi-Factor Authentication factor types.
///
/// Defines the various authentication methods that can be used
/// as factors in a multi-factor authentication system.
enum MfaFactorType {
  /// Time-based One-Time Password (TOTP) using authenticator apps
  totp,

  /// SMS-based verification codes sent to mobile phone
  sms,

  /// Email-based verification codes
  email,

  /// Biometric authentication (fingerprint, face recognition, etc.)
  biometric,

  /// Hardware security keys (FIDO2/WebAuthn)
  securityKey,

  /// Backup recovery codes for account recovery
  backupCodes,
}

/// Result of an MFA verification attempt.
///
/// Represents the outcome of verifying a multi-factor authentication
/// challenge, including success, failure, and expiration scenarios.
@freezed
class MfaVerificationResult with _$MfaVerificationResult {
  /// Creates a successful MFA verification result.
  ///
  /// [factorId] - Unique identifier of the verified factor
  /// [verifiedAt] - Timestamp when verification was completed
  /// [metadata] - Optional additional verification metadata
  const factory MfaVerificationResult.success({
    required String factorId,
    required DateTime verifiedAt,
    Map<String, dynamic>? metadata,
  }) = MfaVerificationSuccess;

  /// Creates a failed MFA verification result.
  ///
  /// [reason] - Human-readable reason for verification failure
  /// [factorId] - Unique identifier of the factor that failed
  /// [attemptsRemaining] - Number of attempts remaining before lockout
  /// [lockedUntil] - Timestamp when account will be unlocked (if locked)
  const factory MfaVerificationResult.failure({
    required String reason,
    required String factorId,
    int? attemptsRemaining,
    DateTime? lockedUntil,
  }) = MfaVerificationFailure;

  /// Creates an expired MFA verification result.
  ///
  /// [challengeId] - Unique identifier of the expired challenge
  /// [expiredAt] - Timestamp when the challenge expired
  const factory MfaVerificationResult.expired({required String challengeId, required DateTime expiredAt}) =
      MfaVerificationExpired;
}

/// Result of an MFA factor enrollment attempt.
///
/// Represents the outcome of enrolling a new MFA factor,
/// including success and failure scenarios with relevant data.
@freezed
class MfaEnrollmentResult with _$MfaEnrollmentResult {
  /// Creates a successful MFA enrollment result.
  ///
  /// [factor] - The successfully enrolled MFA factor
  /// [backupCodes] - Optional backup recovery codes generated
  /// [qrCodeUrl] - Optional QR code URL for TOTP setup
  const factory MfaEnrollmentResult.success({required MfaFactor factor, List<String>? backupCodes, String? qrCodeUrl}) =
      MfaEnrollmentSuccess;

  /// Creates a failed MFA enrollment result.
  ///
  /// [reason] - Human-readable reason for enrollment failure
  /// [details] - Optional additional failure details
  const factory MfaEnrollmentResult.failure({required String reason, String? details}) = MfaEnrollmentFailure;
}

/// Current MFA status for a user account.
///
/// Provides comprehensive information about the user's MFA configuration,
/// enrolled factors, and current security status.
@freezed
abstract class MfaStatus with _$MfaStatus {
  /// Creates an MFA status object.
  ///
  /// [isEnabled] - Whether MFA is enabled for this user
  /// [isRequired] - Whether MFA is required for this user
  /// [enrolledFactors] - List of currently enrolled MFA factors
  /// [availableFactors] - List of MFA factor types available for enrollment
  /// [lastVerification] - Timestamp of last successful MFA verification
  /// [hasBackupCodes] - Whether user has backup codes configured
  /// [remainingBackupCodes] - Number of unused backup codes remaining
  const factory MfaStatus({
    required bool isEnabled,
    required bool isRequired,
    required List<MfaFactor> enrolledFactors,
    required List<MfaFactorType> availableFactors,
    DateTime? lastVerification,
    bool? hasBackupCodes,
    int? remainingBackupCodes,
  }) = _MfaStatus;
}

/// Audit event for MFA-related activities.
///
/// Records security-relevant events in the MFA system for
/// compliance, monitoring, and forensic analysis.
@freezed
abstract class MfaAuditEvent with _$MfaAuditEvent {
  /// Creates an MFA audit event.
  ///
  /// [id] - Unique identifier for this audit event
  /// [userId] - ID of the user associated with this event
  /// [eventType] - Type of MFA event that occurred
  /// [timestamp] - When the event occurred
  /// [factorId] - ID of the MFA factor involved (if applicable)
  /// [challengeId] - ID of the MFA challenge (if applicable)
  /// [deviceId] - ID of the device used (if available)
  /// [ipAddress] - IP address of the client
  /// [userAgent] - User agent string of the client
  /// [success] - Whether the operation was successful
  /// [failureReason] - Reason for failure (if applicable)
  /// [metadata] - Additional event-specific metadata
  const factory MfaAuditEvent({
    required String id,
    required String userId,
    required MfaEventType eventType,
    required DateTime timestamp,
    String? factorId,
    String? challengeId,
    String? deviceId,
    String? ipAddress,
    String? userAgent,
    bool? success,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) = _MfaAuditEvent;
}

/// Event types for MFA audit logging.
///
/// Defines the various types of events that can occur
/// in the MFA system for security monitoring and compliance.
enum MfaEventType {
  /// MFA challenge was created for verification
  challengeCreated,

  /// MFA challenge expired without completion
  challengeExpired,

  /// User attempted to verify an MFA challenge
  verificationAttempt,

  /// MFA verification was successful
  verificationSuccess,

  /// MFA verification failed
  verificationFailure,

  /// New MFA factor was enrolled
  factorEnrolled,

  /// MFA factor was removed from account
  factorRemoved,

  /// MFA factor was disabled
  factorDisabled,

  /// Backup recovery code was used
  backupCodeUsed,

  /// Account was locked due to failed attempts
  accountLocked,

  /// Account was unlocked after lockout period
  accountUnlocked,
}

/// Extensions for MfaFactor
extension MfaFactorExtensions on MfaFactor {
  /// Get the factor type
  MfaFactorType get type => when(
    totp: (_, _, _, _, _, _, _, _, _, _, _) => MfaFactorType.totp,
    sms: (_, _, _, _, _, _, _) => MfaFactorType.sms,
    email: (_, _, _, _, _, _, _) => MfaFactorType.email,
    biometric: (_, _, _, _, _, _, _) => MfaFactorType.biometric,
    securityKey: (_, _, _, _, _, _, _) => MfaFactorType.securityKey,
    backupCodes: (_, _, _, _, _, _) => MfaFactorType.backupCodes,
  );

  /// Get display name for the factor
  String get displayName => when(
    totp: (_, _, _, issuer, accountName, _, _, _, _, _, _) => '$issuer ($accountName)',
    sms: (_, _, maskedPhoneNumber, _, _, _, _) => maskedPhoneNumber ?? 'SMS Authentication',
    email: (_, _, maskedEmail, _, _, _, _) => maskedEmail ?? 'Email Authentication',
    biometric: (_, type, _, displayName, _, _, _) => displayName ?? type.name,
    securityKey: (_, _, _, displayName, _, _, _) => displayName ?? 'Security Key',
    backupCodes: (_, _, _, _, _, _) => 'Backup Codes',
  );

  /// Check if factor is currently usable
  bool get isUsable =>
      isActive &&
      when(
        totp: (_, _, _, _, _, _, _, _, _, _, _) => true,
        sms: (_, _, _, _, _, _, _) => true,
        email: (_, _, _, _, _, _, _) => true,
        biometric: (_, _, _, _, _, _, _) => true,
        securityKey: (_, _, _, _, _, _, _) => true,
        backupCodes: (_, _, remainingCodes, _, _, _) => remainingCodes > 0,
      );

  /// Get factor ID
  String get factorId => when(
    totp: (id, _, _, _, _, _, _, _, _, _, _) => id,
    sms: (id, _, _, _, _, _, _) => id,
    email: (id, _, _, _, _, _, _) => id,
    biometric: (id, _, _, _, _, _, _) => id,
    securityKey: (id, _, _, _, _, _, _) => id,
    backupCodes: (id, _, _, _, _, _) => id,
  );
}

/// Extensions for MfaFactorType
extension MfaFactorTypeExtensions on MfaFactorType {
  /// Get human-readable name
  String get displayName {
    switch (this) {
      case MfaFactorType.totp:
        return 'Authenticator App';
      case MfaFactorType.sms:
        return 'SMS';
      case MfaFactorType.email:
        return 'Email';
      case MfaFactorType.biometric:
        return 'Biometric';
      case MfaFactorType.securityKey:
        return 'Security Key';
      case MfaFactorType.backupCodes:
        return 'Backup Codes';
    }
  }

  /// Get factor description
  String get description {
    switch (this) {
      case MfaFactorType.totp:
        return 'Use an authenticator app to generate time-based codes';
      case MfaFactorType.sms:
        return 'Receive verification codes via SMS';
      case MfaFactorType.email:
        return 'Receive verification codes via email';
      case MfaFactorType.biometric:
        return 'Use biometric authentication (fingerprint, face, etc.)';
      case MfaFactorType.securityKey:
        return 'Use a hardware security key (FIDO2/WebAuthn)';
      case MfaFactorType.backupCodes:
        return 'Use single-use backup codes';
    }
  }

  /// Check if factor requires device enrollment
  bool get requiresDevice {
    switch (this) {
      case MfaFactorType.totp:
        return true;
      case MfaFactorType.sms:
        return false;
      case MfaFactorType.email:
        return false;
      case MfaFactorType.biometric:
        return true;
      case MfaFactorType.securityKey:
        return true;
      case MfaFactorType.backupCodes:
        return false;
    }
  }
}
