import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide User;

import '../../../features/auth/domain/entities/user.dart';
import '../../errors/app_error.dart';
import 'mfa_models.dart';
import 'security_context.dart';

part 'auth_state.freezed.dart';

/// Unified authentication state following Context7 MCP best practices
///
/// This replaces all 19 duplicate authentication providers with a single
/// source of truth for authentication state management.
@freezed
abstract class AuthState with _$AuthState {
  /// Initial state when authentication system is starting up
  const factory AuthState.initial() = AuthInitial;

  /// Loading state during authentication operations
  const factory AuthState.loading({String? operation, double? progress}) = AuthLoading;

  /// Authenticated state with complete user context
  const factory AuthState.authenticated({
    required User user,
    required Session session,
    required SecurityContext securityContext,
    required DateTime lastActivity,
    @Default(false) bool mfaEnabled,
    List<MfaFactor>? enrolledFactors,
  }) = AuthAuthenticated;

  /// Unauthenticated state - user needs to sign in
  const factory AuthState.unauthenticated({String? reason, DateTime? lastSignOut}) = AuthUnauthenticated;

  /// MFA required state - user needs to complete MFA challenge
  const factory AuthState.mfaRequired({
    required String challengeId,
    required List<MfaFactor> availableFactors,
    required String userId,
    DateTime? challengeExpiry,
  }) = AuthMfaRequired;

  /// Error state with detailed error information
  const factory AuthState.error({required AppError error, String? operation, DateTime? timestamp, bool? canRetry}) =
      AuthError;

  /// Session expired state - requires re-authentication
  const factory AuthState.sessionExpired({required DateTime expiredAt, String? reason, bool? canRefresh}) =
      AuthSessionExpired;

  /// Account locked state - security measure
  const factory AuthState.accountLocked({required String reason, DateTime? unlockAt, List<String>? unlockMethods}) =
      AuthAccountLocked;
}

/// Authentication operation types for tracking
enum AuthOperation {
  /// User sign-in operation
  signIn,

  /// User registration operation
  signUp,

  /// User sign-out operation
  signOut,

  /// Token refresh operation
  refreshToken,

  /// Multi-factor authentication verification
  mfaVerification,

  /// Password reset operation
  passwordReset,

  /// Session validation operation
  sessionValidation,

  /// Device registration for authentication
  deviceRegistration,
}

/// Authentication result wrapper with detailed information
@freezed
class AuthResult<T> with _$AuthResult<T> {
  /// Creates a successful authentication result
  const factory AuthResult.success({required T data, String? message, Map<String, dynamic>? metadata}) = AuthSuccess<T>;

  /// Creates a failed authentication result
  const factory AuthResult.failure({required AppError error, String? operation, Map<String, dynamic>? context}) =
      AuthFailure<T>;

  /// Creates a result indicating MFA is required
  const factory AuthResult.mfaRequired({
    required String challengeId,
    required List<MfaFactor> availableFactors,
    Map<String, dynamic>? context,
  }) = AuthMfaRequiredResult<T>;
}

/// Authentication request models
@freezed
abstract class SignInRequest with _$SignInRequest {
  /// Creates a sign-in request
  const factory SignInRequest({
    required String email,
    required String password,
    String? deviceId,
    String? deviceName,
    Map<String, dynamic>? metadata,
  }) = _SignInRequest;
}

/// Request model for user registration
///
/// Contains all necessary information for creating a new user account
/// including optional metadata for device tracking and user preferences.
@freezed
abstract class SignUpRequest with _$SignUpRequest {
  /// Creates a sign-up request
  const factory SignUpRequest({
    required String email,
    required String password,
    String? fullName,
    String? phoneNumber,
    String? deviceId,
    String? deviceName,
    Map<String, dynamic>? metadata,
  }) = _SignUpRequest;
}

/// Request model for password reset operations
///
/// Contains the user's email and optional redirect URL for password reset flow.
/// Metadata can be used for tracking and analytics purposes.
@freezed
abstract class PasswordResetRequest with _$PasswordResetRequest {
  /// Creates a password reset request
  const factory PasswordResetRequest({required String email, String? redirectUrl, Map<String, dynamic>? metadata}) =
      _PasswordResetRequest;
}

/// Authentication response models
@freezed
abstract class AuthResponse with _$AuthResponse {
  /// Creates an authentication response
  const factory AuthResponse({
    required User user,
    required Session session,
    required SecurityContext securityContext,
    bool? requiresMfa,
    String? mfaChallengeId,
    List<MfaFactor>? availableFactors,
  }) = _AuthResponse;
}

/// Token management models
@freezed
abstract class AuthTokens with _$AuthTokens {
  /// Creates authentication tokens with access and refresh tokens
  ///
  /// [accessToken] - JWT token for API authentication
  /// [refreshToken] - Token used to refresh expired access tokens
  /// [expiresAt] - When the access token expires
  /// [tokenType] - Type of token (usually 'Bearer')
  /// [scopes] - List of permissions granted to this token
  const factory AuthTokens({
    required String accessToken,
    required String refreshToken,
    required DateTime expiresAt,
    String? tokenType,
    List<String>? scopes,
  }) = _AuthTokens;
}

/// Session information
@freezed
abstract class SessionInfo with _$SessionInfo {
  /// Creates session information with tracking details
  ///
  /// Contains all necessary information for session management including
  /// device tracking, activity monitoring, and security metadata.
  const factory SessionInfo({
    required String sessionId,
    required String userId,
    required DateTime createdAt,
    required DateTime lastActivity,
    required DateTime expiresAt,
    String? deviceId,
    String? deviceName,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic>? metadata,
  }) = _SessionInfo;
}

/// Device trust information
@freezed
abstract class DeviceTrust with _$DeviceTrust {
  /// Creates device trust information for security tracking
  ///
  /// Used to track device trustworthiness and security status
  /// for enhanced authentication security.
  const factory DeviceTrust({
    required String deviceId,
    required String deviceFingerprint,
    required TrustLevel trustLevel,
    required DateTime lastSeen,
    DateTime? registeredAt,
    List<String>? capabilities,
    Map<String, dynamic>? metadata,
  }) = _DeviceTrust;
}

/// Trust levels for device authentication
enum TrustLevel {
  /// Trust level is unknown
  unknown,

  /// Device is not trusted
  untrusted,

  /// Basic trust level
  basic,

  /// Device is trusted
  trusted,

  /// Device is verified and highly trusted
  verified,
}

/// Assurance levels for authentication strength
enum AssuranceLevel {
  /// No assurance
  none,

  /// Low assurance level
  low,

  /// Medium assurance level
  medium,

  /// High assurance level
  high,

  /// Very high assurance level
  veryHigh,
}

/// Authentication event types for audit logging
enum AuthEventType {
  /// User attempted to sign in
  signInAttempt,

  /// User successfully signed in
  signInSuccess,

  /// User sign in attempt failed
  signInFailure,

  /// User attempted to sign up
  signUpAttempt,

  /// User successfully signed up
  signUpSuccess,

  /// User sign up attempt failed
  signUpFailure,

  /// User signed out
  signOut,

  /// Access token was refreshed
  tokenRefresh,

  /// MFA challenge was initiated
  mfaChallenge,

  /// MFA challenge completed successfully
  mfaSuccess,

  /// MFA challenge failed
  mfaFailure,

  /// Password reset was requested
  passwordReset,

  /// User session expired
  sessionExpired,

  /// User account was locked
  accountLocked,

  /// New device was registered
  deviceRegistered,

  /// Security alert was triggered
  securityAlert,
}

/// Authentication event for audit trail
@freezed
abstract class AuthEvent with _$AuthEvent {
  /// Creates an authentication event for audit logging
  ///
  /// Used to track all authentication-related activities for security
  /// monitoring and compliance purposes.
  const factory AuthEvent({
    required String id,
    required AuthEventType type,
    required DateTime timestamp,
    String? userId,
    String? sessionId,
    String? deviceId,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic>? metadata,
    String? description,
  }) = _AuthEvent;
}

/// Extensions for AuthState
extension AuthStateExtensions on AuthState {
  /// Check if user is authenticated
  bool get isAuthenticated => when(
    initial: () => false,
    loading: (_, _) => false,
    authenticated: (_, _, _, _, _, _) => true,
    unauthenticated: (_, _) => false,
    mfaRequired: (_, _, _, _) => false,
    error: (_, _, _, _) => false,
    sessionExpired: (_, _, _) => false,
    accountLocked: (_, _, _) => false,
  );

  /// Check if authentication is in progress
  bool get isLoading => when(
    initial: () => false,
    loading: (_, _) => true,
    authenticated: (_, _, _, _, _, _) => false,
    unauthenticated: (_, _) => false,
    mfaRequired: (_, _, _, _) => false,
    error: (_, _, _, _) => false,
    sessionExpired: (_, _, _) => false,
    accountLocked: (_, _, _) => false,
  );

  /// Get current user if authenticated
  User? get currentUser => whenOrNull(authenticated: (user, _, _, _, _, _) => user);

  /// Get current session if authenticated
  Session? get currentSession => whenOrNull(authenticated: (_, session, _, _, _, _) => session);

  /// Get security context if authenticated
  SecurityContext? get securityContext =>
      whenOrNull(authenticated: (_, _, securityContext, _, _, _) => securityContext);

  /// Check if MFA is enabled
  bool get isMfaEnabled => whenOrNull(authenticated: (_, _, _, _, mfaEnabled, _) => mfaEnabled) ?? false;

  /// Check if MFA is required
  bool get requiresMfa => when(
    initial: () => false,
    loading: (_, _) => false,
    authenticated: (_, _, _, _, _, _) => false,
    unauthenticated: (_, _) => false,
    mfaRequired: (_, _, _, _) => true,
    error: (_, _, _, _) => false,
    sessionExpired: (_, _, _) => false,
    accountLocked: (_, _, _) => false,
  );

  /// Check if there's an error
  bool get hasError => when(
    initial: () => false,
    loading: (_, _) => false,
    authenticated: (_, _, _, _, _, _) => false,
    unauthenticated: (_, _) => false,
    mfaRequired: (_, _, _, _) => false,
    error: (_, _, _, _) => true,
    sessionExpired: (_, _, _) => true,
    accountLocked: (_, _, _) => true,
  );

  /// Get error message if any
  String? get errorMessage => whenOrNull(
    error: (error, _, _, _) => error.message,
    sessionExpired: (_, reason, _) => reason ?? 'Session expired',
    accountLocked: (reason, _, _) => reason,
  );
}
