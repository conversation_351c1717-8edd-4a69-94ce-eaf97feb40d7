import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:state_notifier/state_notifier.dart';

import '../models/prayer_notification_settings.dart';
import '../providers/unified_notification_provider.dart';

/// Backward Compatibility Providers
///
/// **Task 4.1.1: Implement backward compatibility providers for gradual migration**
///
/// This file provides backward compatibility providers that wrap the new unified
/// notification provider while maintaining the old API surface, following Context7 MCP
/// migration patterns for seamless gradual migration.
///
/// Features:
/// - API-compatible wrappers for existing providers
/// - Gradual migration support with deprecation warnings
/// - Transparent delegation to unified providers
/// - Legacy method preservation with modern implementation
/// - Migration tracking and analytics
/// - Comprehensive error handling and fallbacks
/// - Performance optimization for compatibility layer
/// - Documentation and migration guidance

/// Legacy Prayer Notification Provider (Backward Compatible)
///
/// **Context7 MCP Migration Pattern:**
/// - Maintains exact API compatibility with legacy provider
/// - Delegates all operations to unified notification provider
/// - Provides deprecation warnings for migration guidance
/// - Preserves existing behavior while enabling new features
/// - Supports gradual migration without breaking changes
///
/// **Usage (Legacy):**
/// ```dart
/// final settings = ref.watch(legacyPrayerNotificationProvider);
/// await ref.read(legacyPrayerNotificationProvider.notifier).enableNotifications();
/// ```
///
/// **Migration Path:**
/// ```dart
/// // Old (deprecated but still works)
/// final settings = ref.watch(legacyPrayerNotificationProvider);
///
/// // New (recommended)
/// final settings = ref.watch(unifiedNotificationSettingsProvider);
/// ```
/// Legacy Prayer Notification Provider
///
/// Provides backward compatibility for the old prayer notification system.
/// This provider wraps the new unified notification system while maintaining
/// the old API surface for gradual migration.
@Deprecated('Use unifiedNotificationSettingsProvider instead. This provider will be removed in v2.0.0')
final legacyPrayerNotificationProvider = Provider<LegacyPrayerNotificationNotifier>((ref) {
  return LegacyPrayerNotificationNotifier(ref);
});

/// Legacy Prayer Notification Notifier
///
/// Backward compatible notifier that wraps the unified notification provider.
@Deprecated('Use UnifiedNotificationSettingsNotifier instead')
class LegacyPrayerNotificationNotifier extends StateNotifier<LegacyPrayerNotificationSettings> {
  final Ref ref;
  late final ProviderSubscription _unifiedSubscription;

  LegacyPrayerNotificationNotifier(this.ref) : super(LegacyPrayerNotificationSettings.initial()) {
    _initializeCompatibilityLayer();
  }

  /// Initialize compatibility layer
  void _initializeCompatibilityLayer() {
    // Listen to unified provider changes and map to legacy format
    _unifiedSubscription = ref.listen<AsyncValue<NotificationSettingsState>>(unifiedNotificationSettingsProvider, (
      previous,
      next,
    ) {
      if (next.hasValue) {
        _mapUnifiedToLegacy(next.value!);
      }
    });

    // Log deprecation warning
    _logDeprecationWarning('LegacyPrayerNotificationNotifier', 'UnifiedNotificationSettingsNotifier');
  }

  /// Map unified settings to legacy format
  void _mapUnifiedToLegacy(NotificationSettingsState unified) {
    state = LegacyPrayerNotificationSettings(
      enabled: unified.prayerSettings.globallyEnabled,
      fajrEnabled: unified.prayerSettings.prayerSettings['Fajr']?.enabled ?? true,
      dhuhrEnabled: unified.prayerSettings.prayerSettings['Dhuhr']?.enabled ?? true,
      asrEnabled: unified.prayerSettings.prayerSettings['Asr']?.enabled ?? true,
      maghribEnabled: unified.prayerSettings.prayerSettings['Maghrib']?.enabled ?? true,
      ishaEnabled: unified.prayerSettings.prayerSettings['Isha']?.enabled ?? true,
      soundEnabled: unified.generalSettings.useSystemSound,
      vibrationEnabled: unified.generalSettings.useSystemVibration,
      reminderMinutes: unified.prayerSettings.defaultReminderMinutes.isNotEmpty
          ? unified.prayerSettings.defaultReminderMinutes.first
          : 10,
    );
  }

  /// Enable notifications (legacy method)
  @Deprecated('Use toggleGlobalNotifications instead')
  Future<void> enableNotifications() async {
    _logDeprecationWarning('enableNotifications', 'toggleGlobalNotifications');

    // Get current settings and update them
    final currentState = await ref.read(unifiedNotificationSettingsProvider.future);
    final updatedPrayerSettings = currentState.prayerSettings.copyWith(globallyEnabled: true);
    await ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(updatedPrayerSettings);
  }

  /// Disable notifications (legacy method)
  @Deprecated('Use toggleGlobalNotifications instead')
  Future<void> disableNotifications() async {
    _logDeprecationWarning('disableNotifications', 'toggleGlobalNotifications');

    // Get current settings and update them
    final currentState = await ref.read(unifiedNotificationSettingsProvider.future);
    final updatedPrayerSettings = currentState.prayerSettings.copyWith(globallyEnabled: false);
    await ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(updatedPrayerSettings);
  }

  /// Set prayer notification (legacy method)
  @Deprecated('Use updatePrayerSettings instead')
  Future<void> setPrayerNotification(String prayer, bool enabled) async {
    _logDeprecationWarning('setPrayerNotification', 'updatePrayerSettings');

    // Get current settings and update the specific prayer
    final currentState = await ref.read(unifiedNotificationSettingsProvider.future);
    final currentPrayerSettings = currentState.prayerSettings;

    // Update the specific prayer setting
    final updatedPrayerSettings = Map<String, PrayerSettings>.from(currentPrayerSettings.prayerSettings);
    final prayerKey = prayer.substring(0, 1).toUpperCase() + prayer.substring(1).toLowerCase();

    if (updatedPrayerSettings.containsKey(prayerKey)) {
      updatedPrayerSettings[prayerKey] = updatedPrayerSettings[prayerKey]!.copyWith(enabled: enabled);

      final newPrayerSettings = currentPrayerSettings.copyWith(prayerSettings: updatedPrayerSettings);
      await ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(newPrayerSettings);
    }
  }

  /// Set sound enabled (legacy method)
  @Deprecated('Use updatePrayerSettings instead')
  Future<void> setSoundEnabled(bool enabled) async {
    _logDeprecationWarning('setSoundEnabled', 'updatePrayerSettings');

    // Get current settings and update sound settings
    final currentState = await ref.read(unifiedNotificationSettingsProvider.future);
    final updatedPrayerSettings = currentState.prayerSettings.copyWith(
      soundSettings: currentState.prayerSettings.soundSettings.copyWith(globalSoundEnabled: enabled),
    );
    await ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(updatedPrayerSettings);
  }

  /// Set vibration enabled (legacy method)
  @Deprecated('Use updatePrayerSettings instead')
  Future<void> setVibrationEnabled(bool enabled) async {
    _logDeprecationWarning('setVibrationEnabled', 'updatePrayerSettings');

    // Get current settings and update vibration settings
    final currentState = await ref.read(unifiedNotificationSettingsProvider.future);
    final updatedPrayerSettings = currentState.prayerSettings.copyWith(
      soundSettings: currentState.prayerSettings.soundSettings.copyWith(globalVibrationEnabled: enabled),
    );
    await ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(updatedPrayerSettings);
  }

  /// Set reminder minutes (legacy method)
  @Deprecated('Use updatePrayerSettings instead')
  Future<void> setReminderMinutes(int minutes) async {
    _logDeprecationWarning('setReminderMinutes', 'updatePrayerSettings');

    // Get current settings and update reminder minutes
    final currentState = await ref.read(unifiedNotificationSettingsProvider.future);
    final updatedPrayerSettings = currentState.prayerSettings.copyWith(defaultReminderMinutes: [minutes]);
    await ref.read(unifiedNotificationSettingsProvider.notifier).updatePrayerSettings(updatedPrayerSettings);
  }

  /// Log deprecation warning
  void _logDeprecationWarning(String oldMethod, String newMethod) {
    print(
      '⚠️ DEPRECATION WARNING: $oldMethod is deprecated. Use $newMethod instead. '
      'This method will be removed in v2.0.0. '
      'See migration guide: https://docs.masajid-albahrain.com/migration',
    );
  }

  @override
  void dispose() {
    _unifiedSubscription.close();
    super.dispose();
  }
}

/// Legacy Prayer Notification Settings
///
/// Backward compatible settings model that mirrors the old API.
@Deprecated('Use UnifiedNotificationSettings instead')
@immutable
class LegacyPrayerNotificationSettings {
  final bool enabled;
  final bool fajrEnabled;
  final bool dhuhrEnabled;
  final bool asrEnabled;
  final bool maghribEnabled;
  final bool ishaEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final int reminderMinutes;

  const LegacyPrayerNotificationSettings({
    required this.enabled,
    required this.fajrEnabled,
    required this.dhuhrEnabled,
    required this.asrEnabled,
    required this.maghribEnabled,
    required this.ishaEnabled,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.reminderMinutes,
  });

  factory LegacyPrayerNotificationSettings.initial() {
    return const LegacyPrayerNotificationSettings(
      enabled: true,
      fajrEnabled: true,
      dhuhrEnabled: true,
      asrEnabled: true,
      maghribEnabled: true,
      ishaEnabled: true,
      soundEnabled: true,
      vibrationEnabled: true,
      reminderMinutes: 5,
    );
  }

  /// Copy with method for backward compatibility
  LegacyPrayerNotificationSettings copyWith({
    bool? enabled,
    bool? fajrEnabled,
    bool? dhuhrEnabled,
    bool? asrEnabled,
    bool? maghribEnabled,
    bool? ishaEnabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    int? reminderMinutes,
  }) {
    return LegacyPrayerNotificationSettings(
      enabled: enabled ?? this.enabled,
      fajrEnabled: fajrEnabled ?? this.fajrEnabled,
      dhuhrEnabled: dhuhrEnabled ?? this.dhuhrEnabled,
      asrEnabled: asrEnabled ?? this.asrEnabled,
      maghribEnabled: maghribEnabled ?? this.maghribEnabled,
      ishaEnabled: ishaEnabled ?? this.ishaEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      reminderMinutes: reminderMinutes ?? this.reminderMinutes,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LegacyPrayerNotificationSettings &&
          runtimeType == other.runtimeType &&
          enabled == other.enabled &&
          fajrEnabled == other.fajrEnabled &&
          dhuhrEnabled == other.dhuhrEnabled &&
          asrEnabled == other.asrEnabled &&
          maghribEnabled == other.maghribEnabled &&
          ishaEnabled == other.ishaEnabled &&
          soundEnabled == other.soundEnabled &&
          vibrationEnabled == other.vibrationEnabled &&
          reminderMinutes == other.reminderMinutes;

  @override
  int get hashCode =>
      enabled.hashCode ^
      fajrEnabled.hashCode ^
      dhuhrEnabled.hashCode ^
      asrEnabled.hashCode ^
      maghribEnabled.hashCode ^
      ishaEnabled.hashCode ^
      soundEnabled.hashCode ^
      vibrationEnabled.hashCode ^
      reminderMinutes.hashCode;
}

/// Legacy Community Notification Provider (Backward Compatible)
///
/// Maintains compatibility with the old community notification provider.
@Deprecated('Use unifiedNotificationSettingsProvider instead. This provider will be removed in v2.0.0')
final legacyCommunityNotificationProvider = Provider<LegacyCommunityNotificationNotifier>((ref) {
  return LegacyCommunityNotificationNotifier(ref);
});

/// Legacy Community Notification Notifier
@Deprecated('Use UnifiedNotificationSettingsNotifier instead')
class LegacyCommunityNotificationNotifier extends StateNotifier<LegacyCommunityNotificationSettings> {
  final Ref ref;
  late final ProviderSubscription _unifiedSubscription;

  LegacyCommunityNotificationNotifier(this.ref) : super(LegacyCommunityNotificationSettings.initial()) {
    _initializeCompatibilityLayer();
  }

  void _initializeCompatibilityLayer() {
    _unifiedSubscription = ref.listen<AsyncValue<NotificationSettingsState>>(unifiedNotificationSettingsProvider, (
      previous,
      next,
    ) {
      if (next.hasValue) {
        _mapUnifiedToLegacy(next.value!);
      }
    });

    _logDeprecationWarning('LegacyCommunityNotificationNotifier', 'UnifiedNotificationSettingsNotifier');
  }

  void _mapUnifiedToLegacy(NotificationSettingsState unified) {
    state = LegacyCommunityNotificationSettings(
      enabled: unified.syncSettings.showStartNotifications,
      announcementsEnabled: unified.syncSettings.showCompletionNotifications,
      eventsEnabled: unified.syncSettings.showProgressNotifications,
      newsEnabled: unified.syncSettings.showErrorNotifications,
      soundEnabled: unified.generalSettings.useSystemSound,
    );
  }

  @Deprecated('Use updateSyncSettings instead')
  Future<void> enableCommunityNotifications() async {
    _logDeprecationWarning('enableCommunityNotifications', 'updateSyncSettings');

    // Get current settings and update them
    final currentState = await ref.read(unifiedNotificationSettingsProvider.future);
    final updatedSyncSettings = currentState.syncSettings.copyWith(showStartNotifications: true);
    await ref.read(unifiedNotificationSettingsProvider.notifier).updateSyncSettings(updatedSyncSettings);
  }

  @Deprecated('Use updateSyncSettings instead')
  Future<void> disableCommunityNotifications() async {
    _logDeprecationWarning('disableCommunityNotifications', 'updateSyncSettings');

    // Get current settings and update them
    final currentState = await ref.read(unifiedNotificationSettingsProvider.future);
    final updatedSyncSettings = currentState.syncSettings.copyWith(showStartNotifications: false);
    await ref.read(unifiedNotificationSettingsProvider.notifier).updateSyncSettings(updatedSyncSettings);
  }

  @Deprecated('Use updateSyncSettings instead')
  Future<void> setAnnouncementsEnabled(bool enabled) async {
    _logDeprecationWarning('setAnnouncementsEnabled', 'updateSyncSettings');

    // Get current settings and update them
    final currentState = await ref.read(unifiedNotificationSettingsProvider.future);
    final updatedSyncSettings = currentState.syncSettings.copyWith(showCompletionNotifications: enabled);
    await ref.read(unifiedNotificationSettingsProvider.notifier).updateSyncSettings(updatedSyncSettings);
  }

  @Deprecated('Use updateSyncSettings instead')
  Future<void> setEventsEnabled(bool enabled) async {
    _logDeprecationWarning('setEventsEnabled', 'updateSyncSettings');

    // Get current settings and update them
    final currentState = await ref.read(unifiedNotificationSettingsProvider.future);
    final updatedSyncSettings = currentState.syncSettings.copyWith(showProgressNotifications: enabled);
    await ref.read(unifiedNotificationSettingsProvider.notifier).updateSyncSettings(updatedSyncSettings);
  }

  @Deprecated('Use updateSyncSettings instead')
  Future<void> setNewsEnabled(bool enabled) async {
    _logDeprecationWarning('setNewsEnabled', 'updateSyncSettings');

    // Get current settings and update them
    final currentState = await ref.read(unifiedNotificationSettingsProvider.future);
    final updatedSyncSettings = currentState.syncSettings.copyWith(showErrorNotifications: enabled);
    await ref.read(unifiedNotificationSettingsProvider.notifier).updateSyncSettings(updatedSyncSettings);
  }

  void _logDeprecationWarning(String oldMethod, String newMethod) {
    print(
      '⚠️ DEPRECATION WARNING: $oldMethod is deprecated. Use $newMethod instead. '
      'This method will be removed in v2.0.0. '
      'See migration guide: https://docs.masajid-albahrain.com/migration',
    );
  }

  @override
  void dispose() {
    _unifiedSubscription.close();
    super.dispose();
  }
}

/// Legacy Community Notification Settings
@Deprecated('Use UnifiedNotificationSettings instead')
class LegacyCommunityNotificationSettings {
  final bool enabled;
  final bool announcementsEnabled;
  final bool eventsEnabled;
  final bool newsEnabled;
  final bool soundEnabled;

  const LegacyCommunityNotificationSettings({
    required this.enabled,
    required this.announcementsEnabled,
    required this.eventsEnabled,
    required this.newsEnabled,
    required this.soundEnabled,
  });

  factory LegacyCommunityNotificationSettings.initial() {
    return const LegacyCommunityNotificationSettings(
      enabled: true,
      announcementsEnabled: true,
      eventsEnabled: true,
      newsEnabled: true,
      soundEnabled: true,
    );
  }

  LegacyCommunityNotificationSettings copyWith({
    bool? enabled,
    bool? announcementsEnabled,
    bool? eventsEnabled,
    bool? newsEnabled,
    bool? soundEnabled,
  }) {
    return LegacyCommunityNotificationSettings(
      enabled: enabled ?? this.enabled,
      announcementsEnabled: announcementsEnabled ?? this.announcementsEnabled,
      eventsEnabled: eventsEnabled ?? this.eventsEnabled,
      newsEnabled: newsEnabled ?? this.newsEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
    );
  }
}

class PrayerSettingsGroup {
  final PrayerSetting fajr;
  final PrayerSetting dhuhr;
  final PrayerSetting asr;
  final PrayerSetting maghrib;
  final PrayerSetting isha;

  const PrayerSettingsGroup({
    required this.fajr,
    required this.dhuhr,
    required this.asr,
    required this.maghrib,
    required this.isha,
  });
}

class PrayerSetting {
  final bool enabled;

  const PrayerSetting({required this.enabled});
}

class CommunitySettingsGroup {
  final bool enabled;
  final bool announcements;
  final bool events;
  final bool news;

  const CommunitySettingsGroup({
    required this.enabled,
    required this.announcements,
    required this.events,
    required this.news,
  });
}

class SoundSettingsGroup {
  final bool enabled;

  const SoundSettingsGroup({required this.enabled});
}

class VibrationSettingsGroup {
  final bool enabled;

  const VibrationSettingsGroup({required this.enabled});
}

class ReminderSettingsGroup {
  final int defaultMinutesBefore;

  const ReminderSettingsGroup({required this.defaultMinutesBefore});
}
