import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;

import 'package:flutter/foundation.dart';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/providers/location_providers.dart';
import '../../data/models/masjid_model.dart';
import 'unified_masjids_data_manager.dart';

part 'unified_masjids_search_manager.g.dart';

/// Unified Masjids Search Manager
///
/// This manager consolidates all masjid search functionality following
/// Context7 MCP best practices for Flutter search implementation.
///
/// Features:
/// - Advanced search with debouncing and intelligent caching
/// - Multi-field search (Arabic/English) with relevance scoring
/// - Location-based search with radius filtering
/// - Search result ranking and personalization
/// - Search history and intelligent suggestions
/// - Performance optimization for large datasets
/// - Real-time search with stream-based updates
/// - Offline search capabilities with fallback

/// Search configuration and performance settings
const Map<String, dynamic> _searchConfig = {
  'debounce_delay_ms': 300,
  'cache_ttl_minutes': 15,
  'max_search_results': 100,
  'enable_search_analytics': true,
  'enable_search_history': true,
  'search_cache_key_prefix': 'masjids_search_',
  'performance_threshold_ms': 200,
  'min_query_length': 2,
  'max_suggestions': 10,
  'location_search_radius_km': 10.0,
};

/// Search state model with enhanced capabilities
@immutable
class UnifiedSearchState {
  final String query;
  final List<MasjidModel> results;
  final bool isLoading;
  final String? error;
  final DateTime lastSearched;
  final Map<String, dynamic> searchMetadata;
  final int totalResults;
  final Duration lastSearchDuration;
  final List<String> suggestions;
  final Map<String, double> relevanceScores;

  const UnifiedSearchState({
    required this.query,
    required this.results,
    this.isLoading = false,
    this.error,
    required this.lastSearched,
    this.searchMetadata = const {},
    this.totalResults = 0,
    this.lastSearchDuration = Duration.zero,
    this.suggestions = const [],
    this.relevanceScores = const {},
  });

  UnifiedSearchState copyWith({
    String? query,
    List<MasjidModel>? results,
    bool? isLoading,
    String? error,
    DateTime? lastSearched,
    Map<String, dynamic>? searchMetadata,
    int? totalResults,
    Duration? lastSearchDuration,
    List<String>? suggestions,
    Map<String, double>? relevanceScores,
    bool clearError = false,
  }) {
    return UnifiedSearchState(
      query: query ?? this.query,
      results: results ?? this.results,
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
      lastSearched: lastSearched ?? this.lastSearched,
      searchMetadata: searchMetadata ?? this.searchMetadata,
      totalResults: totalResults ?? this.totalResults,
      lastSearchDuration: lastSearchDuration ?? this.lastSearchDuration,
      suggestions: suggestions ?? this.suggestions,
      relevanceScores: relevanceScores ?? this.relevanceScores,
    );
  }

  bool get hasResults => results.isNotEmpty;
  bool get hasError => error != null;
  bool get isValidQuery => query.trim().length >= (_searchConfig['min_query_length'] as int);

  Map<String, dynamic> toJson() => {
    'query': query,
    'results_count': results.length,
    'lastSearched': lastSearched.toIso8601String(),
    'searchMetadata': searchMetadata,
    'totalResults': totalResults,
    'suggestions': suggestions,
  };

  factory UnifiedSearchState.fromJson(Map<String, dynamic> json) {
    return UnifiedSearchState(
      query: json['query'] as String? ?? '',
      results: const [], // Results not persisted, only metadata
      lastSearched: DateTime.parse(json['lastSearched'] as String),
      searchMetadata: Map<String, dynamic>.from(json['searchMetadata'] ?? {}),
      totalResults: json['totalResults'] as int? ?? 0,
      suggestions: List<String>.from(json['suggestions'] ?? []),
    );
  }

  factory UnifiedSearchState.empty() =>
      UnifiedSearchState(query: '', results: const [], lastSearched: DateTime.now(), searchMetadata: const {});
}

/// Unified Masjids Search Manager Provider
@riverpod
class UnifiedMasjidsSearchManager extends _$UnifiedMasjidsSearchManager {
  Timer? _debounceTimer;
  final Map<String, List<MasjidModel>> _searchCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};

  // Context7 MCP: Advanced search structures
  final Map<String, Map<String, dynamic>> _searchAnalytics = {};
  final Map<String, int> _queryFrequency = {};
  final Map<String, Duration> _searchPerformanceMetrics = {};
  final List<String> _searchHistory = [];

  // Context7 MCP: Enhanced search history and suggestions
  final Map<String, DateTime> _queryTimestamps = {};
  final Map<String, int> _queryResultCounts = {};
  final Map<String, List<String>> _contextualSuggestions = {};
  final Set<String> _popularQueries = {};
  final Map<String, double> _queryRelevanceScores = {};
  final List<String> _trendingQueries = [];

  // Context7 MCP: Advanced suggestion algorithms
  final Map<String, Set<String>> _queryCooccurrence = {};
  final Map<String, List<String>> _semanticSuggestions = {};
  final Map<String, String> _queryCorrections = {};

  // Context7 MCP: Real-time search streams with RxDart
  final BehaviorSubject<String> _querySubject = BehaviorSubject<String>();
  final BehaviorSubject<List<MasjidModel>> _resultsSubject = BehaviorSubject<List<MasjidModel>>();
  final BehaviorSubject<List<String>> _suggestionsSubject = BehaviorSubject<List<String>>();

  StreamSubscription<String>? _searchSubscription;

  @override
  UnifiedSearchState build() {
    // Context7 MCP: Initialize real-time search streams
    _initializeSearchStreams();

    // Load persisted search state and history
    _loadPersistedSearchState();

    // Set up cleanup on dispose
    ref.onDispose(() {
      _debounceTimer?.cancel();
      _searchSubscription?.cancel();
      _querySubject.close();
      _resultsSubject.close();
      _suggestionsSubject.close();
    });

    return UnifiedSearchState.empty();
  }

  /// Initialize real-time search streams with Context7 MCP patterns
  void _initializeSearchStreams() {
    // Context7 MCP: Debounced search stream with intelligent caching
    _searchSubscription = _querySubject.stream
        .debounceTime(Duration(milliseconds: _searchConfig['debounce_delay_ms'] as int))
        .distinct()
        .where((query) => query.trim().length >= (_searchConfig['min_query_length'] as int))
        .listen(_performSearch);

    debugPrint('UnifiedMasjidsSearchManager: Real-time search streams initialized');
  }

  /// Load persisted search state and history with enhanced metadata
  Future<void> _loadPersistedSearchState() async {
    if (!(_searchConfig['enable_search_history'] as bool)) return;

    try {
      final prefs = await SharedPreferences.getInstance();

      // Context7 MCP: Load search history
      final historyJson = prefs.getStringList('masjids_search_history') ?? [];
      _searchHistory.clear();
      _searchHistory.addAll(historyJson);

      // Context7 MCP: Load query timestamps
      final timestampsJson = prefs.getString('masjids_query_timestamps');
      if (timestampsJson != null) {
        final data = jsonDecode(timestampsJson) as Map<String, dynamic>;
        _queryTimestamps.clear();
        _queryTimestamps.addAll(data.map((k, v) => MapEntry(k, DateTime.parse(v as String))));
      }

      // Context7 MCP: Load query result counts
      final resultCountsJson = prefs.getString('masjids_query_result_counts');
      if (resultCountsJson != null) {
        final data = jsonDecode(resultCountsJson) as Map<String, dynamic>;
        _queryResultCounts.clear();
        _queryResultCounts.addAll(data.map((k, v) => MapEntry(k, v as int)));
      }

      // Context7 MCP: Load trending queries
      final trendingJson = prefs.getStringList('masjids_trending_queries') ?? [];
      _trendingQueries.clear();
      _trendingQueries.addAll(trendingJson);

      // Context7 MCP: Load popular queries
      final popularJson = prefs.getStringList('masjids_popular_queries') ?? [];
      _popularQueries.clear();
      _popularQueries.addAll(popularJson);

      // Context7 MCP: Load search analytics with enhanced data
      final analyticsJson = prefs.getString('masjids_search_analytics');
      if (analyticsJson != null) {
        final data = jsonDecode(analyticsJson) as Map<String, dynamic>;

        final queryFreq = data['query_frequency'] as Map<String, dynamic>? ?? {};
        _queryFrequency.clear();
        _queryFrequency.addAll(queryFreq.map((k, v) => MapEntry(k, v as int)));

        final relevanceScores = data['query_relevance_scores'] as Map<String, dynamic>? ?? {};
        _queryRelevanceScores.clear();
        _queryRelevanceScores.addAll(relevanceScores.map((k, v) => MapEntry(k, (v as num).toDouble())));
      }

      debugPrint(
        'Enhanced search state loaded: ${_searchHistory.length} history, ${_trendingQueries.length} trending, ${_popularQueries.length} popular',
      );
    } on Exception catch (e) {
      debugPrint('Error loading search state: $e');
    }
  }

  /// Perform search with advanced caching and analytics
  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      state = state.copyWith(query: '', results: [], isLoading: false, clearError: true);
      return;
    }

    final stopwatch = Stopwatch()..start();

    try {
      // Context7 MCP: Update state to show loading
      state = state.copyWith(query: query, isLoading: true, clearError: true);

      // Context7 MCP: Check cache first for performance
      final cachedResults = _getCachedSearchResults(query);
      if (cachedResults != null) {
        stopwatch.stop();
        state = state.copyWith(
          results: cachedResults,
          isLoading: false,
          lastSearched: DateTime.now(),
          lastSearchDuration: stopwatch.elapsed,
          totalResults: cachedResults.length,
        );

        // Update analytics for cache hit
        _updateSearchAnalytics(query, stopwatch.elapsed, true);
        return;
      }

      // Context7 MCP: Perform actual search
      final searchResults = await _executeAdvancedSearch(query);

      stopwatch.stop();

      // Context7 MCP: Cache results for future use
      _cacheSearchResults(query, searchResults);

      // Context7 MCP: Calculate relevance scores
      final relevanceScores = _calculateRelevanceScores(query, searchResults);

      // Context7 MCP: Sort results by relevance
      final sortedResults = _sortResultsByRelevance(searchResults, relevanceScores);

      state = state.copyWith(
        results: sortedResults,
        isLoading: false,
        lastSearched: DateTime.now(),
        lastSearchDuration: stopwatch.elapsed,
        totalResults: sortedResults.length,
        relevanceScores: relevanceScores,
        searchMetadata: {
          'cache_hit': false,
          'search_duration_ms': stopwatch.elapsedMilliseconds,
          'result_count': sortedResults.length,
          'query_length': query.length,
        },
      );

      // Context7 MCP: Update search analytics and history
      _updateSearchAnalytics(query, stopwatch.elapsed, false);
      _updateSearchHistory(query, resultCount: sortedResults.length);
      _generateSearchSuggestions(query);

      // Context7 MCP: Persist search state (fire and forget)
      _persistSearchState().ignore();

      debugPrint('Search completed: "$query" -> ${sortedResults.length} results in ${stopwatch.elapsedMilliseconds}ms');
    } on Exception catch (e) {
      stopwatch.stop();
      state = state.copyWith(isLoading: false, error: 'Search failed: $e', lastSearchDuration: stopwatch.elapsed);
      debugPrint('Search error for "$query": $e');
    }
  }

  /// Execute advanced search with multi-field support
  Future<List<MasjidModel>> _executeAdvancedSearch(String query) async {
    try {
      // Context7 MCP: Get masjids data from unified data manager
      final masjidsAsync = ref.read(unifiedMasjidsDataManagerProvider);

      return await masjidsAsync.when(
        data: (masjids) async {
          // Context7 MCP: Perform multi-field search
          final results = _performMultiFieldSearch(masjids, query);

          debugPrint('Advanced search found ${results.length} results for: $query');
          return results;
        },
        loading: () async {
          debugPrint('Data still loading, returning empty search results');
          return <MasjidModel>[];
        },
        error: (error, stack) async {
          debugPrint('Data error during search: $error');
          throw Exception('Data unavailable for search: $error');
        },
      );
    } on Exception catch (e) {
      debugPrint('Error in advanced search execution: $e');
      rethrow;
    }
  }

  /// Perform multi-field search with Arabic/English support
  List<MasjidModel> _performMultiFieldSearch(List<MasjidModel> masjids, String query) {
    final normalizedQuery = query.toLowerCase().trim();
    final results = <MasjidModel>[];

    for (final masjid in masjids) {
      if (_matchesMasjidAdvanced(masjid, normalizedQuery, query)) {
        results.add(masjid);
      }
    }

    return results;
  }

  /// Advanced multi-field matching with Context7 MCP best practices
  bool _matchesMasjidAdvanced(MasjidModel masjid, String normalizedQuery, String originalQuery) {
    // Context7 MCP: Comprehensive multi-field search with Arabic/English support

    // Primary search fields with language-specific handling
    final primaryFields = _getPrimarySearchFields(masjid, normalizedQuery, originalQuery);
    final secondaryFields = _getSecondarySearchFields(masjid, normalizedQuery, originalQuery);
    final contextualFields = _getContextualSearchFields(masjid, normalizedQuery, originalQuery);

    // Context7 MCP: Multi-tier matching strategy
    return _performTieredMatching(primaryFields, secondaryFields, contextualFields, normalizedQuery, originalQuery);
  }

  /// Get primary search fields (names) with language-aware processing
  Map<String, dynamic> _getPrimarySearchFields(MasjidModel masjid, String normalizedQuery, String originalQuery) {
    return {
      'official_name_en': {
        'value': masjid.officialNameEn?.toLowerCase() ?? '',
        'weight': 10.0,
        'language': 'en',
        'field_type': 'primary_name',
      },
      'official_name_ar': {
        'value': masjid.officialNameAr ?? '',
        'weight': 10.0,
        'language': 'ar',
        'field_type': 'primary_name',
      },
      'common_name_en': {
        'value': masjid.commonNameEn?.toLowerCase() ?? '',
        'weight': 8.0,
        'language': 'en',
        'field_type': 'common_name',
      },
      'common_name_ar': {
        'value': masjid.commonNameAr ?? '',
        'weight': 8.0,
        'language': 'ar',
        'field_type': 'common_name',
      },
    };
  }

  /// Get secondary search fields (addresses) with location context
  Map<String, dynamic> _getSecondarySearchFields(MasjidModel masjid, String normalizedQuery, String originalQuery) {
    return {
      'address_en': {
        'value': masjid.addressEn?.toLowerCase() ?? '',
        'weight': 5.0,
        'language': 'en',
        'field_type': 'address',
      },
      'address_ar': {'value': masjid.addressAr ?? '', 'weight': 5.0, 'language': 'ar', 'field_type': 'address'},
    };
  }

  /// Get contextual search fields (metadata and additional info)
  Map<String, dynamic> _getContextualSearchFields(MasjidModel masjid, String normalizedQuery, String originalQuery) {
    return {
      'instagram_account': {
        'value': masjid.instagramAccount?.toLowerCase() ?? '',
        'weight': 2.0,
        'language': 'en',
        'field_type': 'social_media',
      },
      'website': {
        'value': masjid.website?.toLowerCase() ?? '',
        'weight': 2.0,
        'language': 'en',
        'field_type': 'website',
      },
      'type': {'value': masjid.typeId?.toLowerCase() ?? '', 'weight': 3.0, 'language': 'en', 'field_type': 'category'},
    };
  }

  /// Perform tiered matching with Context7 MCP advanced algorithms
  bool _performTieredMatching(
    Map<String, dynamic> primaryFields,
    Map<String, dynamic> secondaryFields,
    Map<String, dynamic> contextualFields,
    String normalizedQuery,
    String originalQuery,
  ) {
    // Context7 MCP: Multi-tier matching strategy

    // Tier 1: Exact and prefix matching in primary fields
    if (_performExactMatching(primaryFields, normalizedQuery, originalQuery)) {
      return true;
    }

    // Tier 2: Fuzzy matching in primary fields
    if (_performFuzzyMatching(primaryFields, normalizedQuery, originalQuery)) {
      return true;
    }

    // Tier 3: Contains matching in all fields
    if (_performContainsMatching(
      [...primaryFields.entries, ...secondaryFields.entries],
      normalizedQuery,
      originalQuery,
    )) {
      return true;
    }

    // Tier 4: Contextual and partial matching
    if (_performContextualMatching(contextualFields, normalizedQuery, originalQuery)) {
      return true;
    }

    // Tier 5: Advanced Arabic/English cross-language matching
    if (_performCrossLanguageMatching(primaryFields, secondaryFields, normalizedQuery, originalQuery)) {
      return true;
    }

    return false;
  }

  /// Perform exact matching with Context7 MCP precision algorithms
  bool _performExactMatching(Map<String, dynamic> fields, String normalizedQuery, String originalQuery) {
    for (final entry in fields.entries) {
      final fieldData = entry.value as Map<String, dynamic>;
      final fieldValue = fieldData['value'] as String;
      final language = fieldData['language'] as String;

      // Context7 MCP: Language-aware exact matching
      if (language == 'ar') {
        // Arabic exact matching (case-sensitive for Arabic)
        if (fieldValue == originalQuery || fieldValue == normalizedQuery) {
          return true;
        }
      } else {
        // English exact matching (case-insensitive)
        if (fieldValue == normalizedQuery) {
          return true;
        }
      }

      // Context7 MCP: Prefix matching for better UX
      if (fieldValue.startsWith(normalizedQuery) || (language == 'ar' && fieldValue.startsWith(originalQuery))) {
        return true;
      }
    }
    return false;
  }

  /// Perform fuzzy matching with Context7 MCP algorithms
  bool _performFuzzyMatching(Map<String, dynamic> fields, String normalizedQuery, String originalQuery) {
    for (final entry in fields.entries) {
      final fieldData = entry.value as Map<String, dynamic>;
      final fieldValue = fieldData['value'] as String;
      final language = fieldData['language'] as String;

      // Context7 MCP: Language-specific fuzzy matching
      if (_calculateSimilarity(fieldValue, language == 'ar' ? originalQuery : normalizedQuery) > 0.8) {
        return true;
      }

      // Context7 MCP: Word-based fuzzy matching
      if (_performWordBasedMatching(fieldValue, normalizedQuery, originalQuery, language)) {
        return true;
      }
    }
    return false;
  }

  /// Perform contains matching with Context7 MCP optimization
  bool _performContainsMatching(
    List<MapEntry<String, dynamic>> fieldEntries,
    String normalizedQuery,
    String originalQuery,
  ) {
    for (final entry in fieldEntries) {
      final fieldData = entry.value as Map<String, dynamic>;
      final fieldValue = fieldData['value'] as String;
      final language = fieldData['language'] as String;

      // Context7 MCP: Language-aware contains matching
      if (language == 'ar') {
        if (fieldValue.contains(originalQuery) || fieldValue.contains(normalizedQuery)) {
          return true;
        }
      } else {
        if (fieldValue.contains(normalizedQuery)) {
          return true;
        }
      }
    }
    return false;
  }

  /// Perform contextual matching with Context7 MCP intelligence
  bool _performContextualMatching(Map<String, dynamic> fields, String normalizedQuery, String originalQuery) {
    for (final entry in fields.entries) {
      final fieldData = entry.value as Map<String, dynamic>;
      final fieldValue = fieldData['value'] as String;
      final fieldType = fieldData['field_type'] as String;

      // Context7 MCP: Field-type specific matching
      switch (fieldType) {
        case 'social_media':
          if (_performSocialMediaMatching(fieldValue, normalizedQuery)) return true;
          break;
        case 'website':
          if (_performWebsiteMatching(fieldValue, normalizedQuery)) return true;
          break;
        case 'category':
          if (_performCategoryMatching(fieldValue, normalizedQuery)) return true;
          break;
      }
    }
    return false;
  }

  /// Perform cross-language matching with Context7 MCP algorithms
  bool _performCrossLanguageMatching(
    Map<String, dynamic> primaryFields,
    Map<String, dynamic> secondaryFields,
    String normalizedQuery,
    String originalQuery,
  ) {
    // Context7 MCP: Advanced cross-language matching
    final allFields = {...primaryFields, ...secondaryFields};

    for (final entry in allFields.entries) {
      final fieldData = entry.value as Map<String, dynamic>;
      final fieldValue = fieldData['value'] as String;
      final language = fieldData['language'] as String;

      // Context7 MCP: Transliteration and phonetic matching
      if (_performTransliterationMatching(fieldValue, normalizedQuery, originalQuery, language)) {
        return true;
      }

      // Context7 MCP: Common word matching
      if (_performCommonWordMatching(fieldValue, normalizedQuery, originalQuery, language)) {
        return true;
      }
    }
    return false;
  }

  /// Calculate string similarity using Context7 MCP algorithms
  double _calculateSimilarity(String str1, String str2) {
    if (str1.isEmpty || str2.isEmpty) return 0.0;
    if (str1 == str2) return 1.0;

    // Context7 MCP: Levenshtein distance-based similarity
    final distance = _levenshteinDistance(str1, str2);
    final maxLength = math.max(str1.length, str2.length);
    return 1.0 - (distance / maxLength);
  }

  /// Calculate Levenshtein distance
  int _levenshteinDistance(String str1, String str2) {
    final matrix = List.generate(str1.length + 1, (i) => List.generate(str2.length + 1, (j) => 0));

    for (var i = 0; i <= str1.length; i++) {
      matrix[i][0] = i;
    }
    for (var j = 0; j <= str2.length; j++) {
      matrix[0][j] = j;
    }

    for (var i = 1; i <= str1.length; i++) {
      for (var j = 1; j <= str2.length; j++) {
        final cost = str1[i - 1] == str2[j - 1] ? 0 : 1;
        matrix[i][j] = math.min(math.min(matrix[i - 1][j] + 1, matrix[i][j - 1] + 1), matrix[i - 1][j - 1] + cost);
      }
    }

    return matrix[str1.length][str2.length];
  }

  /// Perform word-based matching with Context7 MCP intelligence
  bool _performWordBasedMatching(String fieldValue, String normalizedQuery, String originalQuery, String language) {
    final queryWords = language == 'ar'
        ? originalQuery.split(' ').where((w) => w.isNotEmpty).toList()
        : normalizedQuery.split(' ').where((w) => w.isNotEmpty).toList();

    final fieldWords = fieldValue.split(' ').where((w) => w.isNotEmpty).toList();

    // Context7 MCP: Check if any query word matches any field word
    for (final queryWord in queryWords) {
      for (final fieldWord in fieldWords) {
        if (_calculateSimilarity(fieldWord, queryWord) > 0.7) {
          return true;
        }
      }
    }

    return false;
  }

  /// Perform social media matching with Context7 MCP patterns
  bool _performSocialMediaMatching(String fieldValue, String normalizedQuery) {
    // Context7 MCP: Social media handle matching
    final cleanHandle = fieldValue.replaceAll('@', '').replaceAll('_', '').replaceAll('.', '');
    final cleanQuery = normalizedQuery.replaceAll('@', '').replaceAll('_', '').replaceAll('.', '');

    return cleanHandle.contains(cleanQuery) || cleanQuery.contains(cleanHandle);
  }

  /// Perform website matching with Context7 MCP URL patterns
  bool _performWebsiteMatching(String fieldValue, String normalizedQuery) {
    // Context7 MCP: URL and domain matching
    final cleanUrl = fieldValue
        .replaceAll('https://', '')
        .replaceAll('http://', '')
        .replaceAll('www.', '')
        .split('/')[0]; // Get domain only

    return cleanUrl.contains(normalizedQuery) || normalizedQuery.contains(cleanUrl);
  }

  /// Perform category matching with Context7 MCP semantic understanding
  bool _performCategoryMatching(String fieldValue, String normalizedQuery) {
    // Context7 MCP: Category and type matching with synonyms
    final categoryMappings = {
      'mosque': ['masjid', 'جامع', 'مسجد'],
      'masjid': ['mosque', 'جامع', 'مسجد'],
      'جامع': ['mosque', 'masjid', 'مسجد'],
      'مسجد': ['mosque', 'masjid', 'جامع'],
    };

    final synonyms = categoryMappings[fieldValue] ?? [];
    return synonyms.any((synonym) => synonym.contains(normalizedQuery) || normalizedQuery.contains(synonym));
  }

  /// Perform transliteration matching with Context7 MCP algorithms
  bool _performTransliterationMatching(
    String fieldValue,
    String normalizedQuery,
    String originalQuery,
    String language,
  ) {
    // Context7 MCP: Basic Arabic-English transliteration patterns
    final transliterationMap = {
      'محمد': ['mohammed', 'muhammad', 'mohamed'],
      'علي': ['ali', 'alee'],
      'حسن': ['hassan', 'hasan'],
      'حسين': ['hussain', 'hussein'],
      'عبد': ['abd', 'abdul'],
      'الله': ['allah', 'ellah'],
      'مسجد': ['masjid', 'mosque'],
      'جامع': ['jamea', 'jamee', 'mosque'],
    };

    if (language == 'ar') {
      final transliterations = transliterationMap[fieldValue] ?? [];
      return transliterations.any((trans) => trans.contains(normalizedQuery));
    } else {
      for (final entry in transliterationMap.entries) {
        if (entry.value.contains(fieldValue) && originalQuery.contains(entry.key)) {
          return true;
        }
      }
    }

    return false;
  }

  /// Perform common word matching with Context7 MCP linguistics
  bool _performCommonWordMatching(String fieldValue, String normalizedQuery, String originalQuery, String language) {
    // Context7 MCP: Common words and phrases matching
    final commonWords = {
      'en': ['the', 'of', 'and', 'in', 'at', 'on', 'for', 'with', 'by'],
      'ar': ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'بين', 'تحت', 'فوق'],
    };

    final stopWords = commonWords[language] ?? [];
    final queryWords = (language == 'ar' ? originalQuery : normalizedQuery)
        .split(' ')
        .where((word) => word.isNotEmpty && !stopWords.contains(word))
        .toList();

    final fieldWords = fieldValue.split(' ').where((word) => word.isNotEmpty && !stopWords.contains(word)).toList();

    // Context7 MCP: Match meaningful words only
    for (final queryWord in queryWords) {
      for (final fieldWord in fieldWords) {
        if (queryWord.length > 2 && fieldWord.length > 2) {
          if (_calculateSimilarity(fieldWord, queryWord) > 0.75) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /// Perform location-based search with Context7 MCP best practices
  Future<List<MasjidModel>> performLocationBasedSearch({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
    String? textQuery,
    int limit = 50,
  }) async {
    try {
      debugPrint('Location-based search: ($latitude, $longitude) radius: ${radiusKm}km');

      // Context7 MCP: Get masjids data from unified data manager
      final masjidsAsync = ref.read(unifiedMasjidsDataManagerProvider);

      return await masjidsAsync.when(
        data: (masjids) async {
          // Context7 MCP: Filter by location first
          final nearbyMasjids = _filterMasjidsByLocation(masjids, latitude, longitude, radiusKm);

          // Context7 MCP: Apply text search if provided
          var filteredResults = nearbyMasjids;
          if (textQuery != null && textQuery.trim().isNotEmpty) {
            filteredResults = _performMultiFieldSearch(nearbyMasjids, textQuery);
          }

          // Context7 MCP: Sort by distance and limit results
          final sortedResults = _sortMasjidsByDistance(filteredResults, latitude, longitude);

          final limitedResults = sortedResults.take(limit).toList();

          debugPrint('Location search found ${limitedResults.length} results');
          return limitedResults;
        },
        loading: () async {
          debugPrint('Data still loading for location search');
          return <MasjidModel>[];
        },
        error: (error, stack) async {
          debugPrint('Data error during location search: $error');
          throw Exception('Data unavailable for location search: $error');
        },
      );
    } catch (e) {
      debugPrint('Location-based search error: $e');
      rethrow;
    }
  }

  /// Filter masjids by location using Context7 MCP distance algorithms
  List<MasjidModel> _filterMasjidsByLocation(
    List<MasjidModel> masjids,
    double centerLat,
    double centerLng,
    double radiusKm,
  ) {
    final nearbyMasjids = <MasjidModel>[];

    for (final masjid in masjids) {
      if (masjid.l26 != null && masjid.lo50 != null) {
        final distance = _calculateHaversineDistance(centerLat, centerLng, masjid.l26!, masjid.lo50!);

        if (distance <= radiusKm) {
          nearbyMasjids.add(masjid);
        }
      }
    }

    return nearbyMasjids;
  }

  /// Sort masjids by distance using Context7 MCP algorithms
  List<MasjidModel> _sortMasjidsByDistance(List<MasjidModel> masjids, double centerLat, double centerLng) {
    final masjidsWithDistance = <MapEntry<MasjidModel, double>>[];

    for (final masjid in masjids) {
      if (masjid.l26 != null && masjid.lo50 != null) {
        final distance = _calculateHaversineDistance(centerLat, centerLng, masjid.l26!, masjid.lo50!);
        masjidsWithDistance.add(MapEntry(masjid, distance));
      }
    }

    // Context7 MCP: Sort by distance (closest first)
    masjidsWithDistance.sort((a, b) => a.value.compareTo(b.value));

    return masjidsWithDistance.map((entry) => entry.key).toList();
  }

  /// Calculate distance using Haversine formula with Context7 MCP precision
  double _calculateHaversineDistance(double lat1, double lng1, double lat2, double lng2) {
    const earthRadiusKm = 6371.0;

    final dLat = _degreesToRadians(lat2 - lat1);
    final dLng = _degreesToRadians(lng2 - lng1);

    final a =
        math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) * math.sin(dLng / 2) * math.sin(dLng / 2);

    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadiusKm * c;
  }

  /// Convert degrees to radians
  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  /// Get nearby masjids with distance information
  Future<List<MapEntry<MasjidModel, double>>> getNearbyMasjidsWithDistance({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
    int limit = 50,
  }) async {
    try {
      final masjidsAsync = ref.read(unifiedMasjidsDataManagerProvider);

      return await masjidsAsync.when(
        data: (masjids) async {
          final masjidsWithDistance = <MapEntry<MasjidModel, double>>[];

          for (final masjid in masjids) {
            if (masjid.l26 != null && masjid.lo50 != null) {
              final distance = _calculateHaversineDistance(latitude, longitude, masjid.l26!, masjid.lo50!);

              if (distance <= radiusKm) {
                masjidsWithDistance.add(MapEntry(masjid, distance));
              }
            }
          }

          // Sort by distance and limit results
          masjidsWithDistance.sort((a, b) => a.value.compareTo(b.value));
          return masjidsWithDistance.take(limit).toList();
        },
        loading: () async => <MapEntry<MasjidModel, double>>[],
        error: (error, stack) async {
          throw Exception('Failed to get nearby masjids: $error');
        },
      );
    } catch (e) {
      debugPrint('Error getting nearby masjids with distance: $e');
      rethrow;
    }
  }

  /// Search nearby masjids using current user location
  Future<List<MasjidModel>> searchNearbyMasjids({double radiusKm = 10.0, String? textQuery, int limit = 50}) async {
    try {
      // Context7 MCP: Get current location from unified location manager
      final locationManager = ref.read(unifiedLocationManagerProvider.notifier);
      await locationManager.getCurrentLocation(); // Trigger location fetch

      final locationData = ref.read(currentLocationProvider);

      if (locationData == null) {
        debugPrint('Current location not available for nearby search');
        throw Exception('Location not available for nearby search');
      }

      return await performLocationBasedSearch(
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        radiusKm: radiusKm,
        textQuery: textQuery,
        limit: limit,
      );
    } catch (e) {
      debugPrint('Error searching nearby masjids: $e');
      rethrow;
    }
  }

  /// Enhanced location search with multiple radius options
  Future<Map<String, List<MasjidModel>>> searchMasjidsByMultipleRadii({
    required double latitude,
    required double longitude,
    String? textQuery,
    List<double> radii = const [1.0, 5.0, 10.0, 25.0],
    int limitPerRadius = 20,
  }) async {
    try {
      final results = <String, List<MasjidModel>>{};

      for (final radius in radii) {
        final radiusResults = await performLocationBasedSearch(
          latitude: latitude,
          longitude: longitude,
          radiusKm: radius,
          textQuery: textQuery,
          limit: limitPerRadius,
        );

        results['${radius}km'] = radiusResults;
      }

      return results;
    } catch (e) {
      debugPrint('Error in multi-radius search: $e');
      rethrow;
    }
  }

  /// Get masjids within walking distance (Context7 MCP: 1.5km default)
  Future<List<MasjidModel>> getWalkingDistanceMasjids({
    required double latitude,
    required double longitude,
    String? textQuery,
    double walkingDistanceKm = 1.5,
    int limit = 20,
  }) async {
    return performLocationBasedSearch(
      latitude: latitude,
      longitude: longitude,
      radiusKm: walkingDistanceKm,
      textQuery: textQuery,
      limit: limit,
    );
  }

  /// Get masjids within driving distance (Context7 MCP: 15km default)
  Future<List<MasjidModel>> getDrivingDistanceMasjids({
    required double latitude,
    required double longitude,
    String? textQuery,
    double drivingDistanceKm = 15.0,
    int limit = 50,
  }) async {
    return performLocationBasedSearch(
      latitude: latitude,
      longitude: longitude,
      radiusKm: drivingDistanceKm,
      textQuery: textQuery,
      limit: limit,
    );
  }

  /// Find closest masjid to a given location
  Future<MasjidModel?> findClosestMasjid({
    required double latitude,
    required double longitude,
    String? textQuery,
    double maxDistanceKm = 50.0,
  }) async {
    try {
      final nearbyMasjids = await performLocationBasedSearch(
        latitude: latitude,
        longitude: longitude,
        radiusKm: maxDistanceKm,
        textQuery: textQuery,
        limit: 1,
      );

      return nearbyMasjids.isNotEmpty ? nearbyMasjids.first : null;
    } on Exception catch (e) {
      debugPrint('Error finding closest masjid: $e');
      return null;
    }
  }

  /// Calculate travel time estimation (Context7 MCP: Basic estimation)
  Map<String, dynamic> estimateTravelTime(double distanceKm) {
    // Context7 MCP: Travel time estimation algorithms
    const walkingSpeedKmh = 5.0; // Average walking speed
    const drivingSpeedKmh = 40.0; // Average city driving speed

    final walkingTimeMinutes = (distanceKm / walkingSpeedKmh) * 60;
    final drivingTimeMinutes = (distanceKm / drivingSpeedKmh) * 60;

    return {
      'distance_km': distanceKm,
      'walking_time_minutes': walkingTimeMinutes.round(),
      'driving_time_minutes': drivingTimeMinutes.round(),
      'is_walking_distance': distanceKm <= 2.0,
      'is_short_drive': distanceKm <= 10.0,
    };
  }

  /// Calculate relevance scores for search results
  Map<String, double> _calculateRelevanceScores(String query, List<MasjidModel> results) {
    final scores = <String, double>{};
    final normalizedQuery = query.toLowerCase().trim();

    for (final masjid in results) {
      var score = 0.0;

      // Context7 MCP: Weighted scoring system
      // Official names get highest weight
      if (masjid.officialNameEn?.toLowerCase().contains(normalizedQuery) == true) {
        score += 10.0;
      }
      if (masjid.officialNameAr?.contains(normalizedQuery) == true) {
        score += 10.0;
      }

      // Common names get medium weight
      if (masjid.commonNameEn?.toLowerCase().contains(normalizedQuery) == true) {
        score += 7.0;
      }
      if (masjid.commonNameAr?.contains(normalizedQuery) == true) {
        score += 7.0;
      }

      // Addresses get lower weight
      if (masjid.addressEn?.toLowerCase().contains(normalizedQuery) == true) {
        score += 3.0;
      }
      if (masjid.addressAr?.contains(normalizedQuery) == true) {
        score += 3.0;
      }

      // Context7 MCP: Boost score for exact matches
      if (masjid.officialNameEn?.toLowerCase() == normalizedQuery || masjid.officialNameAr == query) {
        score += 20.0;
      }

      // Context7 MCP: Boost score for starts-with matches
      if (masjid.officialNameEn?.toLowerCase().startsWith(normalizedQuery) == true ||
          masjid.officialNameAr?.startsWith(query) == true) {
        score += 5.0;
      }

      scores[masjid.id] = score;
    }

    return scores;
  }

  /// Sort results by relevance scores
  List<MasjidModel> _sortResultsByRelevance(List<MasjidModel> results, Map<String, double> relevanceScores) {
    final sortedResults = List<MasjidModel>.from(results);

    sortedResults.sort((a, b) {
      final scoreA = relevanceScores[a.id] ?? 0.0;
      final scoreB = relevanceScores[b.id] ?? 0.0;
      return scoreB.compareTo(scoreA); // Descending order
    });

    return sortedResults;
  }

  /// Get cached search results if available and not expired
  List<MasjidModel>? _getCachedSearchResults(String query) {
    final cacheKey = _generateSearchCacheKey(query);
    final cachedResults = _searchCache[cacheKey];
    final cacheTimestamp = _cacheTimestamps[cacheKey];

    if (cachedResults != null && cacheTimestamp != null) {
      final cacheAge = DateTime.now().difference(cacheTimestamp);
      final maxAge = Duration(minutes: _searchConfig['cache_ttl_minutes'] as int);

      if (cacheAge <= maxAge) {
        debugPrint('Cache hit for search query: $query');
        return cachedResults;
      } else {
        // Cache expired, remove it
        _searchCache.remove(cacheKey);
        _cacheTimestamps.remove(cacheKey);
        debugPrint('Cache expired for search query: $query');
      }
    }

    return null;
  }

  /// Cache search results for future use
  void _cacheSearchResults(String query, List<MasjidModel> results) {
    final cacheKey = _generateSearchCacheKey(query);
    _searchCache[cacheKey] = results;
    _cacheTimestamps[cacheKey] = DateTime.now();

    // Context7 MCP: Limit cache size for memory management
    if (_searchCache.length > 50) {
      _evictOldestCacheEntries();
    }

    debugPrint('Cached search results for: $query (${results.length} results)');
  }

  /// Generate cache key for search query
  String _generateSearchCacheKey(String query) {
    final prefix = _searchConfig['search_cache_key_prefix'] as String;
    return '$prefix${query.toLowerCase().trim()}';
  }

  /// Evict oldest cache entries to manage memory
  void _evictOldestCacheEntries() {
    final sortedEntries = _cacheTimestamps.entries.toList()..sort((a, b) => a.value.compareTo(b.value));

    final entriesToRemove = sortedEntries.take(10);
    for (final entry in entriesToRemove) {
      _searchCache.remove(entry.key);
      _cacheTimestamps.remove(entry.key);
    }

    debugPrint('Evicted ${entriesToRemove.length} old search cache entries');
  }

  /// Update search analytics for performance monitoring
  void _updateSearchAnalytics(String query, Duration duration, bool cacheHit) {
    if (!(_searchConfig['enable_search_analytics'] as bool)) return;

    // Update query frequency
    _queryFrequency[query] = (_queryFrequency[query] ?? 0) + 1;

    // Update performance metrics
    _searchPerformanceMetrics[query] = duration;

    // Store detailed analytics
    _searchAnalytics[query] = {
      'frequency': _queryFrequency[query],
      'last_duration_ms': duration.inMilliseconds,
      'cache_hit': cacheHit,
      'last_searched': DateTime.now().toIso8601String(),
      'result_count': state.results.length,
    };

    debugPrint('Updated search analytics for: $query');
  }

  /// Update search history with intelligent deduplication and analytics
  void _updateSearchHistory(String query, {int resultCount = 0}) {
    if (!(_searchConfig['enable_search_history'] as bool)) return;

    // Context7 MCP: Enhanced search history with metadata
    final normalizedQuery = query.trim().toLowerCase();
    if (normalizedQuery.isEmpty) return;

    // Remove existing occurrence to avoid duplicates
    _searchHistory.remove(query);
    _queryTimestamps.remove(query);
    _queryResultCounts.remove(query);

    // Add to beginning of list with metadata
    _searchHistory.insert(0, query);
    _queryTimestamps[query] = DateTime.now();
    _queryResultCounts[query] = resultCount;

    // Update query frequency for popularity tracking
    _queryFrequency[query] = (_queryFrequency[query] ?? 0) + 1;

    // Context7 MCP: Update trending queries based on recent frequency
    _updateTrendingQueries(query);

    // Context7 MCP: Update popular queries set
    if (_queryFrequency[query]! >= 3) {
      _popularQueries.add(query);
    }

    // Context7 MCP: Generate contextual suggestions
    _generateContextualSuggestions(query);

    // Limit history size
    if (_searchHistory.length > 50) {
      final removedQueries = _searchHistory.sublist(50);
      _searchHistory.removeRange(50, _searchHistory.length);

      // Clean up metadata for removed queries
      for (final removedQuery in removedQueries) {
        _queryTimestamps.remove(removedQuery);
        _queryResultCounts.remove(removedQuery);
      }
    }

    debugPrint('Updated search history: ${_searchHistory.length} entries, trending: ${_trendingQueries.length}');
  }

  /// Generate intelligent search suggestions
  void _generateSearchSuggestions(String query) {
    final suggestions = <String>[];

    // Context7 MCP: Add suggestions from search history
    final historySuggestions = _searchHistory
        .where((h) => h.toLowerCase().contains(query.toLowerCase()) && h != query)
        .take(5)
        .toList();
    suggestions.addAll(historySuggestions);

    // Context7 MCP: Add suggestions from popular queries
    final popularQueries =
        _queryFrequency.entries
            .where((e) => e.key.toLowerCase().contains(query.toLowerCase()) && e.key != query)
            .toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    final popularSuggestions = popularQueries.take(3).map((e) => e.key).toList();
    suggestions.addAll(popularSuggestions);

    // Context7 MCP: Remove duplicates and limit
    final uniqueSuggestions = suggestions.toSet().toList();
    final limitedSuggestions = uniqueSuggestions.take(_searchConfig['max_suggestions'] as int).toList();

    // Update suggestions stream
    _suggestionsSubject.add(limitedSuggestions);

    debugPrint('Generated ${limitedSuggestions.length} search suggestions for: $query');
  }

  /// Context7 MCP: Update trending queries based on recent activity
  void _updateTrendingQueries(String query) {
    final now = DateTime.now();
    final recentThreshold = now.subtract(const Duration(hours: 24));

    // Context7 MCP: Calculate trending score based on recent frequency
    final recentQueries = _queryTimestamps.entries
        .where((entry) => entry.value.isAfter(recentThreshold))
        .map((entry) => entry.key)
        .toList();

    final trendingScores = <String, double>{};
    for (final recentQuery in recentQueries) {
      final frequency = _queryFrequency[recentQuery] ?? 0;
      final recency = now.difference(_queryTimestamps[recentQuery]!).inHours;
      final score = frequency / (1 + recency * 0.1); // Decay factor for recency
      trendingScores[recentQuery] = score;
    }

    // Context7 MCP: Update trending queries list
    _trendingQueries.clear();
    final sortedTrending = trendingScores.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
    _trendingQueries.addAll(sortedTrending.take(10).map((e) => e.key));
  }

  /// Context7 MCP: Generate contextual suggestions based on query patterns
  void _generateContextualSuggestions(String query) {
    final suggestions = <String>[];

    // Context7 MCP: Find co-occurring queries
    final cooccurringQueries = _queryCooccurrence[query] ?? <String>{};
    suggestions.addAll(cooccurringQueries.take(3));

    // Context7 MCP: Add semantic suggestions based on query similarity
    for (final historyQuery in _searchHistory.take(20)) {
      if (historyQuery != query && _calculateQuerySimilarity(query, historyQuery) > 0.7) {
        suggestions.add(historyQuery);
        if (suggestions.length >= 5) break;
      }
    }

    _contextualSuggestions[query] = suggestions.toSet().toList();
  }

  /// Context7 MCP: Calculate semantic similarity between queries
  double _calculateQuerySimilarity(String query1, String query2) {
    final words1 = query1.toLowerCase().split(' ').toSet();
    final words2 = query2.toLowerCase().split(' ').toSet();

    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;

    return union > 0 ? intersection / union : 0.0;
  }

  /// Context7 MCP: Normalize Arabic text for better matching
  String _normalizeArabicText(String text) {
    // Context7 MCP: Remove diacritics and normalize Arabic characters
    return text
        .replaceAll(RegExp(r'[\u064B-\u065F\u0670\u06D6-\u06ED]'), '') // Remove diacritics
        .replaceAll('أ', 'ا') // Normalize Alef with Hamza
        .replaceAll('إ', 'ا') // Normalize Alef with Hamza below
        .replaceAll('آ', 'ا') // Normalize Alef with Madda
        .replaceAll('ة', 'ه') // Normalize Taa Marbouta
        .replaceAll('ى', 'ي') // Normalize Alef Maksura
        .trim();
  }

  /// Persist search state and analytics with enhanced metadata
  Future<void> _persistSearchState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Context7 MCP: Persist enhanced search history with metadata
      await prefs.setStringList('masjids_search_history', _searchHistory);

      // Context7 MCP: Persist query timestamps
      final timestampsJson = _queryTimestamps.map((k, v) => MapEntry(k, v.toIso8601String()));
      await prefs.setString('masjids_query_timestamps', jsonEncode(timestampsJson));

      // Context7 MCP: Persist query result counts
      await prefs.setString('masjids_query_result_counts', jsonEncode(_queryResultCounts));

      // Context7 MCP: Persist trending queries
      await prefs.setStringList('masjids_trending_queries', _trendingQueries);

      // Context7 MCP: Persist popular queries
      await prefs.setStringList('masjids_popular_queries', _popularQueries.toList());

      // Context7 MCP: Persist search analytics with enhanced data
      final analyticsData = {
        'query_frequency': _queryFrequency,
        'query_relevance_scores': _queryRelevanceScores,
        'last_updated': DateTime.now().toIso8601String(),
        'total_searches': _queryFrequency.values.fold(0, (sum, count) => sum + count),
        'unique_queries': _queryFrequency.length,
      };
      await prefs.setString('masjids_search_analytics', jsonEncode(analyticsData));

      debugPrint('Enhanced search state persisted successfully');
    } on Exception catch (e) {
      debugPrint('Error persisting search state: $e');
    }
  }

  // ==================== PUBLIC API METHODS ====================

  /// Perform search with debouncing
  void search(String query) {
    _querySubject.add(query);
  }

  /// Clear search results and query
  void clearSearch() {
    state = UnifiedSearchState.empty();
    _querySubject.add('');
    _resultsSubject.add([]);
    _suggestionsSubject.add([]);
  }

  /// Get enhanced search suggestions with Context7 MCP algorithms
  List<String> getSearchSuggestions(String query) {
    if (query.trim().isEmpty) {
      // Context7 MCP: Return trending queries when no input
      return _trendingQueries.take(_searchConfig['max_suggestions'] as int).toList();
    }

    final suggestions = <String>[];
    final normalizedQuery = query.toLowerCase();

    // Context7 MCP: Add from history with relevance scoring
    final historySuggestions = _searchHistory
        .where((h) => h.toLowerCase().contains(normalizedQuery) && h != query)
        .take(5)
        .toList();
    suggestions.addAll(historySuggestions);

    // Context7 MCP: Add from popular queries
    final popularQueries =
        _queryFrequency.entries.where((e) => e.key.toLowerCase().contains(normalizedQuery) && e.key != query).toList()
          ..sort((a, b) => b.value.compareTo(a.value));
    suggestions.addAll(popularQueries.take(3).map((e) => e.key));

    // Context7 MCP: Add trending queries
    final trendingSuggestions = _trendingQueries
        .where((t) => t.toLowerCase().contains(normalizedQuery) && t != query)
        .take(2);
    suggestions.addAll(trendingSuggestions);

    // Context7 MCP: Add contextual suggestions
    final contextual = _contextualSuggestions[query] ?? [];
    suggestions.addAll(contextual.where((c) => c != query).take(2));

    // Context7 MCP: Add semantic suggestions
    final semanticSuggestions = _semanticSuggestions[query] ?? [];
    suggestions.addAll(semanticSuggestions.where((s) => s != query).take(2));

    return suggestions.toSet().take(_searchConfig['max_suggestions'] as int).toList();
  }

  /// Get search history
  List<String> getSearchHistory() => List.unmodifiable(_searchHistory);

  /// Clear search history and all related data
  Future<void> clearSearchHistory() async {
    // Context7 MCP: Clear all search-related data structures
    _searchHistory.clear();
    _queryFrequency.clear();
    _searchAnalytics.clear();
    _queryTimestamps.clear();
    _queryResultCounts.clear();
    _contextualSuggestions.clear();
    _popularQueries.clear();
    _queryRelevanceScores.clear();
    _trendingQueries.clear();
    _queryCooccurrence.clear();
    _semanticSuggestions.clear();
    _queryCorrections.clear();

    try {
      final prefs = await SharedPreferences.getInstance();
      // Context7 MCP: Remove all persisted search data
      await prefs.remove('masjids_search_history');
      await prefs.remove('masjids_search_analytics');
      await prefs.remove('masjids_query_timestamps');
      await prefs.remove('masjids_query_result_counts');
      await prefs.remove('masjids_trending_queries');
      await prefs.remove('masjids_popular_queries');
      debugPrint('Enhanced search history cleared');
    } on Exception catch (e) {
      debugPrint('Error clearing search history: $e');
    }
  }

  /// Context7 MCP: Get trending search queries
  List<String> getTrendingQueries({int limit = 10}) {
    return _trendingQueries.take(limit).toList();
  }

  /// Context7 MCP: Get popular search queries
  List<String> getPopularQueries({int limit = 10}) {
    final sortedPopular = _popularQueries.toList()
      ..sort((a, b) => (_queryFrequency[b] ?? 0).compareTo(_queryFrequency[a] ?? 0));
    return sortedPopular.take(limit).toList();
  }

  /// Context7 MCP: Get contextual suggestions for a query
  List<String> getContextualSuggestions(String query) {
    return _contextualSuggestions[query] ?? [];
  }

  /// Context7 MCP: Get query correction suggestions
  String? getQueryCorrection(String query) {
    return _queryCorrections[query];
  }

  /// Context7 MCP: Remove specific query from history
  Future<void> removeFromHistory(String query) async {
    _searchHistory.remove(query);
    _queryTimestamps.remove(query);
    _queryResultCounts.remove(query);
    _contextualSuggestions.remove(query);
    _queryRelevanceScores.remove(query);
    _semanticSuggestions.remove(query);
    _queryCorrections.remove(query);

    // Update frequency but don't remove completely to maintain analytics
    if (_queryFrequency.containsKey(query)) {
      _queryFrequency[query] = (_queryFrequency[query]! - 1).clamp(0, double.infinity).toInt();
      if (_queryFrequency[query] == 0) {
        _queryFrequency.remove(query);
        _popularQueries.remove(query);
      }
    }

    _trendingQueries.remove(query);

    await _persistSearchState();
    debugPrint('Removed "$query" from search history');
  }

  /// Context7 MCP: Get comprehensive search analytics
  Map<String, dynamic> getSearchAnalytics() {
    final totalSearches = _queryFrequency.values.fold(0, (sum, count) => sum + count);
    final avgDuration = _searchPerformanceMetrics.values.isNotEmpty
        ? _searchPerformanceMetrics.values.map((d) => d.inMilliseconds).reduce((a, b) => a + b) /
              _searchPerformanceMetrics.length
        : 0.0;
    final avgResultsPerQuery = _queryResultCounts.values.isNotEmpty
        ? _queryResultCounts.values.reduce((a, b) => a + b) / _queryResultCounts.length
        : 0.0;

    return {
      'total_searches': totalSearches,
      'unique_queries': _queryFrequency.length,
      'average_duration_ms': avgDuration.round(),
      'avg_results_per_query': avgResultsPerQuery,
      'cache_entries': _searchCache.length,
      'history_entries': _searchHistory.length,
      'trending_queries_count': _trendingQueries.length,
      'popular_queries_count': _popularQueries.length,
      'contextual_suggestions_count': _contextualSuggestions.length,
      'popular_queries': _getPopularQueries(),
      'recent_searches': _searchHistory.take(10).toList(),
      'trending_queries': _trendingQueries.take(5).toList(),
      'most_searched_query': _queryFrequency.isNotEmpty
          ? _queryFrequency.entries.reduce((a, b) => a.value > b.value ? a : b).key
          : null,
      'last_search_time': _queryTimestamps.isNotEmpty
          ? _queryTimestamps.values.reduce((a, b) => a.isAfter(b) ? a : b).toIso8601String()
          : null,
    };
  }

  /// Get popular search queries
  List<Map<String, dynamic>> _getPopularQueries() {
    final sortedQueries = _queryFrequency.entries.toList()..sort((a, b) => b.value.compareTo(a.value));

    return sortedQueries.take(10).map((e) => {'query': e.key, 'count': e.value}).toList();
  }

  /// Clear search cache
  void clearSearchCache() {
    _searchCache.clear();
    _cacheTimestamps.clear();
    debugPrint('Search cache cleared');
  }

  /// Get current search state
  UnifiedSearchState getCurrentSearchState() => state;

  /// Get real-time search results stream
  Stream<List<MasjidModel>> get searchResultsStream => _resultsSubject.stream;

  /// Get real-time search suggestions stream
  Stream<List<String>> get searchSuggestionsStream => _suggestionsSubject.stream;

  /// Get search query stream
  Stream<String> get searchQueryStream => _querySubject.stream;

  // ============================================================================
  // TASK 4.1.4: SEARCH RESULT RANKING AND RELEVANCE
  // ============================================================================

  /// Context7 MCP: Perform search with advanced ranking and relevance
  ///
  /// **Context7 MCP Pattern:** Enterprise-grade search ranking algorithms
  Future<List<MasjidModel>> performRankedSearch({
    required String query,
    double? latitude,
    double? longitude,
    double radiusKm = 50.0,
    int limit = 50,
    Map<String, double> rankingWeights = const {},
  }) async {
    try {
      // Context7 MCP: Get base search results
      List<MasjidModel> results;

      if (latitude != null && longitude != null) {
        results = await performLocationBasedSearch(
          latitude: latitude,
          longitude: longitude,
          radiusKm: radiusKm,
          textQuery: query,
          limit: limit * 2, // Get more results for better ranking
        );
      } else {
        results = await _executeAdvancedSearch(query);
      }

      // Context7 MCP: Apply advanced ranking algorithms
      final rankedResults = await _applyAdvancedRanking(
        results: results,
        query: query,
        userLatitude: latitude,
        userLongitude: longitude,
        rankingWeights: rankingWeights,
      );

      // Context7 MCP: Limit final results
      return rankedResults.take(limit).toList();
    } catch (e) {
      debugPrint('Error in ranked search: $e');
      rethrow;
    }
  }

  /// Context7 MCP: Apply advanced ranking algorithms
  ///
  /// **Context7 MCP Pattern:** Multi-factor relevance scoring
  Future<List<MasjidModel>> _applyAdvancedRanking({
    required List<MasjidModel> results,
    required String query,
    double? userLatitude,
    double? userLongitude,
    Map<String, double> rankingWeights = const {},
  }) async {
    // Context7 MCP: Default ranking weights following enterprise patterns
    final weights = {
      'textRelevance': 0.35,
      'locationProximity': 0.25,
      'popularityScore': 0.15,
      'completenessScore': 0.10,
      'recentActivityScore': 0.10,
      'userPreferenceScore': 0.05,
      ...rankingWeights, // Override with custom weights
    };

    // Context7 MCP: Calculate comprehensive relevance scores
    final scoredResults = <({MasjidModel masjid, double score})>[];

    for (final masjid in results) {
      var totalScore = 0.0;

      // Context7 MCP: Text relevance scoring
      final textScore = _calculateTextRelevanceScore(masjid, query);
      totalScore += textScore * weights['textRelevance']!;

      // Context7 MCP: Location proximity scoring
      if (userLatitude != null && userLongitude != null) {
        final locationScore = _calculateLocationProximityScore(masjid, userLatitude, userLongitude);
        totalScore += locationScore * weights['locationProximity']!;
      }

      // Context7 MCP: Popularity scoring
      final popularityScore = _calculatePopularityScore(masjid);
      totalScore += popularityScore * weights['popularityScore']!;

      // Context7 MCP: Data completeness scoring
      final completenessScore = _calculateCompletenessScore(masjid);
      totalScore += completenessScore * weights['completenessScore']!;

      // Context7 MCP: Recent activity scoring
      final recentActivityScore = _calculateRecentActivityScore(masjid);
      totalScore += recentActivityScore * weights['recentActivityScore']!;

      // Context7 MCP: User preference scoring
      final userPreferenceScore = await _calculateUserPreferenceScore(masjid);
      totalScore += userPreferenceScore * weights['userPreferenceScore']!;

      scoredResults.add((masjid: masjid, score: totalScore));
    }

    // Context7 MCP: Sort by relevance score (descending)
    scoredResults.sort((a, b) => b.score.compareTo(a.score));

    // Context7 MCP: Update analytics with ranking data
    await _trackRankingAnalytics(query, scoredResults);

    return scoredResults.map((result) => result.masjid).toList();
  }

  /// Context7 MCP: Calculate text relevance score
  ///
  /// **Context7 MCP Pattern:** Advanced text matching algorithms
  double _calculateTextRelevanceScore(MasjidModel masjid, String query) {
    if (query.trim().isEmpty) return 0.5; // Neutral score for empty query

    final normalizedQuery = _normalizeArabicText(query.toLowerCase());
    var score = 0.0;
    var matches = 0;

    // Context7 MCP: Exact name match (highest weight)
    final normalizedName = _normalizeArabicText(masjid.officialNameAr?.toLowerCase() ?? '');
    if (normalizedName == normalizedQuery) {
      score += 1.0;
      matches++;
    } else if (normalizedName.contains(normalizedQuery)) {
      score += 0.8;
      matches++;
    }

    // Context7 MCP: English name match
    final englishName = masjid.officialNameEn?.toLowerCase() ?? '';
    if (englishName.isNotEmpty) {
      if (englishName == query.toLowerCase()) {
        score += 0.9;
        matches++;
      } else if (englishName.contains(query.toLowerCase())) {
        score += 0.7;
        matches++;
      }
    }

    // Context7 MCP: Common name matching
    final commonNameAr = masjid.commonNameAr?.toLowerCase() ?? '';
    final commonNameEn = masjid.commonNameEn?.toLowerCase() ?? '';

    if (commonNameAr.contains(normalizedQuery)) {
      score += 0.6;
      matches++;
    }

    if (commonNameEn.contains(query.toLowerCase())) {
      score += 0.6;
      matches++;
    }

    // Context7 MCP: Address matching
    final addressAr = masjid.addressAr?.toLowerCase() ?? '';
    final addressEn = masjid.addressEn?.toLowerCase() ?? '';

    if (addressAr.contains(normalizedQuery)) {
      score += 0.3;
      matches++;
    }

    if (addressEn.contains(query.toLowerCase())) {
      score += 0.3;
      matches++;
    }

    // Context7 MCP: Fuzzy matching for Arabic text
    final fuzzyScore = _calculateFuzzyMatchScore(normalizedName, normalizedQuery);
    score += fuzzyScore * 0.4;

    // Context7 MCP: Word-level matching
    final queryWords = normalizedQuery.split(' ');
    final nameWords = normalizedName.split(' ');

    for (final queryWord in queryWords) {
      for (final nameWord in nameWords) {
        if (nameWord.startsWith(queryWord) && queryWord.length >= 2) {
          score += 0.2;
          matches++;
        }
      }
    }

    // Context7 MCP: Normalize score based on matches
    return matches > 0 ? math.min(score / matches, 1.0) : 0.0;
  }

  /// Context7 MCP: Calculate location proximity score
  ///
  /// **Context7 MCP Pattern:** Distance-based relevance scoring
  double _calculateLocationProximityScore(MasjidModel masjid, double userLatitude, double userLongitude) {
    final distance = _calculateHaversineDistance(userLatitude, userLongitude, masjid.latitude, masjid.longitude);

    // Context7 MCP: Distance-based scoring with exponential decay
    if (distance <= 1.0) return 1.0; // Within 1km = perfect score
    if (distance <= 5.0) return 0.8; // Within 5km = high score
    if (distance <= 10.0) return 0.6; // Within 10km = medium score
    if (distance <= 20.0) return 0.4; // Within 20km = low score
    if (distance <= 50.0) return 0.2; // Within 50km = minimal score

    return 0.1; // Beyond 50km = very low score
  }

  /// Context7 MCP: Calculate popularity score
  ///
  /// **Context7 MCP Pattern:** Popularity-based relevance scoring
  double _calculatePopularityScore(MasjidModel masjid) {
    var score = 0.5; // Base score

    // Context7 MCP: Boost score for masjids with more complete data
    if (masjid.officialNameEn?.isNotEmpty == true) score += 0.1;
    if (masjid.commonNameEn?.isNotEmpty == true) score += 0.1;
    if (masjid.addressEn?.isNotEmpty == true) score += 0.1;
    if (masjid.addressAr?.isNotEmpty == true) score += 0.1;

    // Context7 MCP: Boost score for masjids with facilities
    if (masjid.hasWomen == true) score += 0.05;
    if (masjid.hasParking == true) score += 0.05;
    if (masjid.hasWheelchairAccess == true) score += 0.05;

    return math.min(score, 1.0);
  }

  /// Context7 MCP: Calculate data completeness score
  ///
  /// **Context7 MCP Pattern:** Data quality-based relevance scoring
  double _calculateCompletenessScore(MasjidModel masjid) {
    var completenessPoints = 0;
    const totalPoints = 10;

    // Context7 MCP: Check data completeness
    if (masjid.officialNameAr?.isNotEmpty == true) completenessPoints++;
    if (masjid.officialNameEn?.isNotEmpty == true) completenessPoints++;
    if (masjid.commonNameAr?.isNotEmpty == true) completenessPoints++;
    if (masjid.commonNameEn?.isNotEmpty == true) completenessPoints++;
    if (masjid.addressAr?.isNotEmpty == true) completenessPoints++;
    if (masjid.addressEn?.isNotEmpty == true) completenessPoints++;
    if (masjid.latitude != 0.0 && masjid.longitude != 0.0) completenessPoints++;
    if (masjid.hasWomen != null) completenessPoints++;
    if (masjid.hasParking != null) completenessPoints++;
    if (masjid.hasWheelchairAccess != null) completenessPoints++;

    return completenessPoints / totalPoints;
  }

  /// Context7 MCP: Calculate recent activity score
  ///
  /// **Context7 MCP Pattern:** Temporal relevance scoring
  double _calculateRecentActivityScore(MasjidModel masjid) {
    // Context7 MCP: For now, return neutral score
    // In future, this could consider last update time, user interactions, etc.
    return 0.5;
  }

  /// Context7 MCP: Calculate user preference score
  ///
  /// **Context7 MCP Pattern:** Personalized relevance scoring
  Future<double> _calculateUserPreferenceScore(MasjidModel masjid) async {
    // Context7 MCP: For now, return neutral score
    // In future, this could consider user search history, favorites, etc.
    return 0.5;
  }

  /// Context7 MCP: Track ranking analytics
  ///
  /// **Context7 MCP Pattern:** Search analytics and performance monitoring
  Future<void> _trackRankingAnalytics(String query, List<({MasjidModel masjid, double score})> scoredResults) async {
    try {
      // Context7 MCP: Update search analytics with ranking data
      final rankingStats = _searchAnalytics['ranking_stats'] ?? <String, dynamic>{};
      rankingStats['total_queries'] = (rankingStats['total_queries'] as int? ?? 0) + 1;

      if (scoredResults.isNotEmpty) {
        final topScore = scoredResults.first.score;
        final avgScore = scoredResults.map((r) => r.score).reduce((a, b) => a + b) / scoredResults.length;

        rankingStats['avg_relevance_score'] = avgScore;
        rankingStats['top_relevance_score'] = topScore;
      }

      _searchAnalytics['ranking_stats'] = rankingStats;

      // Context7 MCP: Track query performance
      final queryKey = 'ranking_query_${query.hashCode}';
      _searchAnalytics[queryKey] = {
        'query': query,
        'results_count': scoredResults.length,
        'timestamp': DateTime.now().toIso8601String(),
        'avg_score': scoredResults.isNotEmpty
            ? scoredResults.map((r) => r.score).reduce((a, b) => a + b) / scoredResults.length
            : 0.0,
      };

      // Context7 MCP: Persist analytics
      await _persistSearchState();
    } on Exception catch (e) {
      debugPrint('Error tracking ranking analytics: $e');
    }
  }

  /// Context7 MCP: Calculate fuzzy match score for Arabic text
  ///
  /// **Context7 MCP Pattern:** Fuzzy text matching algorithms
  double _calculateFuzzyMatchScore(String text1, String text2) {
    if (text1.isEmpty || text2.isEmpty) return 0.0;

    // Context7 MCP: Simple Levenshtein distance-based fuzzy matching
    final distance = _levenshteinDistance(text1, text2);
    final maxLength = math.max(text1.length, text2.length);

    if (maxLength == 0) return 1.0;

    return 1.0 - (distance / maxLength);
  }
}

/// Provider for search suggestions
@riverpod
List<String> unifiedSearchSuggestions(Ref ref, String query) {
  final searchManager = ref.read(unifiedMasjidsSearchManagerProvider.notifier);
  return searchManager.getSearchSuggestions(query);
}

/// Provider for search analytics
@riverpod
Map<String, dynamic> unifiedSearchAnalytics(Ref ref) {
  final searchManager = ref.read(unifiedMasjidsSearchManagerProvider.notifier);
  return searchManager.getSearchAnalytics();
}
