import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/settings/prayer_times/prayer_times_settings_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/prayer_name_utility.dart';
import '../../domain/enums/prayer_enum.dart';

part 'prayer_time_visibility_provider.g.dart';

/// Provider for prayer time visibility
///
/// This provider now delegates to the centralized PrayerTimesSettingsProvider
/// for consistent settings management across the app.
@riverpod
class PrayerTimeVisibility extends _$PrayerTimeVisibility {
  @override
  Map<String, bool> build() {
    // Get visibility from the centralized prayer times settings
    final prayerSettings = ref.watch(prayerTimesSettingsProvider);

    return prayerSettings.when(
      data: (settings) => PrayerNameUtility.convertToDisplayMap(settings.visiblePrayers),
      loading: () => PrayerNameUtility.getDefaultVisibilityMap(),
      error: (_, _) => PrayerNameUtility.getDefaultVisibilityMap(),
    );
  }

  /// Toggle visibility for a specific prayer
  ///
  /// This method delegates to the centralized PrayerTimesSettingsProvider
  /// for consistent state management across the app.
  Future<void> toggleVisibility(String prayer) async {
    final currentVisibility = state[prayer] ?? true;
    await _updateVisibility(prayer, !currentVisibility);
  }

  /// Set visibility for a specific prayer
  Future<void> setVisibility(String prayer, bool isVisible) async {
    await _updateVisibility(prayer, isVisible);
  }

  /// Show all prayers
  ///
  /// Sets all prayer types to visible in the UI. Uses comprehensive error
  /// handling following Context7 MCP best practices to ensure UI stability.
  Future<void> showAll() async {
    try {
      AppLogger.info('Prayer visibility: Showing all prayers');

      // Show all prayers by setting each one to visible
      for (final prayer in Prayer.values) {
        await ref.read(prayerTimesSettingsProvider.notifier).togglePrayerVisibility(prayer, true);
      }

      AppLogger.info('Prayer visibility: Successfully showed all prayers');
    } on Exception catch (e, stackTrace) {
      // Log error with comprehensive context following Context7 MCP patterns
      AppLogger.error('Prayer visibility: Failed to show all prayers', e, stackTrace, 'PrayerTimeVisibility');

      // Don't rethrow to maintain UI stability - user will see current state
    }
  }

  /// Hide all prayers
  ///
  /// Sets all prayer types to hidden in the UI. Uses comprehensive error
  /// handling following Context7 MCP best practices to ensure UI stability.
  Future<void> hideAll() async {
    try {
      AppLogger.info('Prayer visibility: Hiding all prayers');

      // Hide all prayers by setting each one to hidden
      for (final prayer in Prayer.values) {
        await ref.read(prayerTimesSettingsProvider.notifier).togglePrayerVisibility(prayer, false);
      }

      AppLogger.info('Prayer visibility: Successfully hid all prayers');
    } on Exception catch (e, stackTrace) {
      // Log error with comprehensive context following Context7 MCP patterns
      AppLogger.error('Prayer visibility: Failed to hide all prayers', e, stackTrace, 'PrayerTimeVisibility');

      // Don't rethrow to maintain UI stability - user will see current state
    }
  }

  /// Helper method to update visibility through centralized provider
  ///
  /// Updates the visibility of a specific prayer using the centralized settings
  /// provider. Includes comprehensive error handling and validation following
  /// Context7 MCP best practices.
  Future<void> _updateVisibility(String prayer, bool isVisible) async {
    try {
      AppLogger.debug('Prayer visibility: Updating $prayer to ${isVisible ? 'visible' : 'hidden'}');

      // Convert display name to Prayer enum using centralized utility
      final prayerEnum = PrayerNameUtility.getPrayerFromDisplayName(prayer);
      if (prayerEnum == null) {
        AppLogger.error('Prayer visibility: Invalid prayer name provided: $prayer', null, null, 'PrayerTimeVisibility');
        // Return early instead of throwing to maintain UI stability
        return;
      }

      // Update through the centralized provider
      await ref.read(prayerTimesSettingsProvider.notifier).togglePrayerVisibility(prayerEnum, isVisible);

      AppLogger.debug('Prayer visibility: Successfully updated $prayer visibility');
    } on Exception catch (e, stackTrace) {
      // Log error with comprehensive context following Context7 MCP patterns
      AppLogger.error(
        'Prayer visibility: Failed to update prayer visibility for $prayer',
        e,
        stackTrace,
        'PrayerTimeVisibility',
      );

      // Don't rethrow to maintain UI stability - user will see current state
    }
  }
}
