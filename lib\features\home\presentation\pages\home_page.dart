import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/mixins/error_handling_mixin.dart';
import '../../../../core/models/location_permission_models.dart' as models;
import '../../../../core/providers/first_launch_provider.dart';
import '../../../../core/providers/location_readiness_coordinator.dart';
// Context7 MCP: Use master location providers instead of separate permission providers
import '../../../../core/providers/master_location_provider.dart';
import '../../../../core/services/location/permission_state_change_handler.dart';
import '../../../../core/services/unified_app_lifecycle_manager.dart';
import '../../../../core/widgets/memory_safe_stateful_widget.dart';
import '../../../masjids/data/models/masjid_model.dart';
import '../../../tutorial/domain/config/tutorial_config.dart';
import '../../../tutorial/domain/providers/tutorial_keys_provider.dart';
import '../../../tutorial/domain/providers/tutorial_provider.dart';
import '../../../tutorial/domain/services/tutorial_service.dart';
import '../controllers/home_page_controller.dart';
import '../providers/home_navigation_provider.dart';
import '../providers/home_search_provider.dart';
import '../providers/unified_nearby_masjids_provider.dart';
import '../widgets/home_prayer_card.dart';
import '../widgets/home_top_bar.dart';
import '../widgets/optimized_map_widget.dart';

/// Home page with map and nearby mosques
/// Home page of the Masajid AlBahrain application.
///
/// This is the main landing page that provides:
/// - Interactive map showing nearby mosques with location markers
/// - Next prayer time display with countdown timer
/// - Quick access to mosque search and filtering
/// - Location-based mosque recommendations
/// - Prayer time notifications and reminders
///
/// The page automatically detects user location and displays relevant
/// mosques and prayer times for the current area in Bahrain.
class HomePage extends ConsumerStatefulWidget {
  /// Creates the home page widget.
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> with MemorySafetyMixin, ErrorHandlingMixin<HomePage> {
  // State for masjid counts
  final ValueNotifier<Map<String, int>> masjidCounts = ValueNotifier({'active': 0, 'inactive': 0});

  /// Text editing controller for the search field (memory-safe)
  late final TextEditingController _searchController;

  /// Selected masjid for displaying the card at the bottom
  MasjidModel? _selectedMasjid;

  /// ID of the nearest masjid to the user (used to determine if "Nearest Masjid" label should be shown)
  String? _nearestMasjidId;

  /// Flag to track if we've already selected a random masjid on initial load
  bool _hasSelectedRandomMasjid = false;

  /// Flag to track if user has manually closed the random masjid card
  bool _userClosedRandomCard = false;

  /// Track if tutorial check has already been performed for this widget instance
  bool _tutorialCheckPerformed = false;

  /// Home page controller for business logic
  late final HomePageController _controller;

  // Location lifecycle manager is now handled by UnifiedAppLifecycleManager

  // Enhanced location state manager is now handled by UnifiedAppLifecycleManager

  @override
  void initState() {
    super.initState();

    // Initialize the search controller (memory-safe)
    _searchController = managedTextController();

    // Initialize the controller
    _controller = HomePageController(ref: ref, searchController: _searchController);

    // Location lifecycle manager is now handled by UnifiedAppLifecycleManager

    // Enhanced location state manager is now handled by UnifiedAppLifecycleManager

    // Note: Location permission is now handled by the central permission system

    // Initialize the unified lifecycle manager
    UnifiedAppLifecycleManager.instance.initialize();

    // Initialize the controller
    _controller.initialize();

    // Context7 MCP: Request location permission immediately when homepage loads
    // This ensures users get the system permission dialog as soon as they see the map
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint('📍 LOCATION: PostFrameCallback executed, requesting permission immediately');
      _requestLocationPermissionImmediately();

      debugPrint('🎓 TUTORIAL: PostFrameCallback executed, calling _showTutorialIfNeeded()');
      _showTutorialIfNeeded();

      // Context7 MCP: Initialize permission state change handler
      PermissionStateChangeHandler.instance.initialize(ref);

      // Context7 MCP: Initialize location-dependent features
      // Note: ref.listen must be called in build method, not initState

      // Context7 MCP: Initialize location services with proper coordination
      _initializeLocationAwareHomePageWithCoordination();
    });
  }

  // Lifecycle management is now handled by UnifiedAppLifecycleManager

  /// Refresh location services when app resumes from background
  void _refreshLocationServicesOnResume() {
    try {
      debugPrint('📍 Refreshing location services on app resume');

      // Location lifecycle manager is now handled by UnifiedAppLifecycleManager

      // Context7 MCP: Force refresh of location service using Master location provider
      final locationManager = ref.read(masterLocationManagerProvider.notifier);

      // Trigger a fresh location request to wake up location services
      locationManager
          .getCurrentLocation(useCache: false)
          .then((_) {
            debugPrint('📍 Location services refreshed successfully on app resume');
            showInfoSnackBar('Location services refreshed');
          })
          .catchError((error) {
            handleError(error, null, 'Location refresh failed', false);
          });
    } on Exception catch (e, stackTrace) {
      handleError(e, stackTrace, 'Failed to refresh location services', false);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // No special handling needed
  }

  /// Show tutorial if this is the first time user opens the home page
  /// Only shows after user has completed onboarding (language and calculation method selection)
  Future<void> _showTutorialIfNeeded() async {
    try {
      debugPrint('🎓 TUTORIAL: _showTutorialIfNeeded() called');
      debugPrint('🎓 TUTORIAL: Widget mounted: $mounted');

      // Prevent multiple calls within the same widget lifecycle
      if (_tutorialCheckPerformed) {
        debugPrint('🎓 TUTORIAL: Tutorial check already performed for this widget instance, skipping');
        return;
      }
      _tutorialCheckPerformed = true;
      debugPrint('🎓 TUTORIAL: Starting tutorial check process...');

      // Wait for tutorial state to load before making decision
      final tutorialState = await ref.read(tutorialNotifierProvider.future);
      debugPrint(
        '🎓 TUTORIAL: Current state - isCompleted: ${tutorialState.isCompleted}, isSkipped: ${tutorialState.isSkipped}, lastStep: ${tutorialState.lastStepReached}',
      );

      // If tutorial is completed or skipped, never show it again
      if (tutorialState.isCompleted || tutorialState.isSkipped) {
        debugPrint('🎓 TUTORIAL: Tutorial already completed/skipped, skipping');
        return;
      }

      // Check if this is the first launch (user hasn't completed onboarding yet)
      final isFirstLaunch = await ref.read(firstLaunchProvider.future);
      debugPrint('🎓 TUTORIAL: First launch status: $isFirstLaunch');
      if (isFirstLaunch) {
        debugPrint('🎓 TUTORIAL: First launch detected, tutorial will show after onboarding');
        return;
      }

      // Check if tutorial has been started before (lastStepReached > 0)
      // If so, don't show it again automatically
      if (tutorialState.lastStepReached > 0) {
        debugPrint(
          '🎓 TUTORIAL: Tutorial was previously started (step ${tutorialState.lastStepReached}), skipping automatic show',
        );
        return;
      }

      // At this point:
      // - Tutorial is not completed/skipped
      // - User has completed onboarding (not first launch)
      // - Tutorial has never been started before
      // This means it's the first time they're seeing the home page after onboarding
      debugPrint('🎓 TUTORIAL: Showing tutorial for first-time user after onboarding');

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Get tutorial keys
      final tutorialKeys = ref.read(tutorialKeysProvider);

      // Create tutorial targets
      final targets = TutorialConfig.createHomePageTargets(context: context, targetKeys: tutorialKeys);

      // Add welcome target at the beginning
      final welcomeTarget = TutorialConfig.createWelcomeTarget(context);
      final allTargets = [welcomeTarget, ...targets];

      // Show tutorial
      final tutorialService = ref.read(tutorialServiceProvider);
      await tutorialService.showTutorial(context: context, targets: allTargets);
    } on Exception catch (e) {
      debugPrint('❌ TUTORIAL: Error showing tutorial: $e');
    }
  }

  /// Select the nearest masjid from the available nearby masjids when the page loads
  /// If no nearby masjids or location services disabled, don't show any card
  Future<void> _selectNearestMasjidIfNeeded() async {
    // Only select a nearest masjid if we haven't already done so and user hasn't closed it
    if (_hasSelectedRandomMasjid || _selectedMasjid != null || _userClosedRandomCard) {
      return;
    }

    try {
      // Check if search is currently active - don't show nearest masjid during search
      final searchQuery = ref.read(homeSearchQueryProvider);
      if (searchQuery.trim().isNotEmpty) {
        debugPrint('📍 Search is active, skipping nearest masjid selection');
        return;
      }

      // Get the nearby masjids list (this checks location permissions internally)
      final nearbyMasjidsAsync = await ref.read(nearbyMasjidsProvider.future);

      if (nearbyMasjidsAsync.isNotEmpty && mounted) {
        // Select the nearest masjid (first in the sorted list)
        final nearestMasjidDistance = nearbyMasjidsAsync.first;
        final nearestMasjid = nearestMasjidDistance.masjid;

        debugPrint(
          '📍 Selected nearest masjid: ${nearestMasjid.officialName} (${nearestMasjidDistance.getFormattedDistance()})',
        );

        // Set the selected masjid to show the card and track the nearest masjid ID
        setState(() {
          _selectedMasjid = nearestMasjid;
          _nearestMasjidId = nearestMasjid.id; // Track the nearest masjid ID
          _hasSelectedRandomMasjid = true; // Keep the same flag for consistency
        });
      } else {
        debugPrint('📍 No nearby masjids available or location services disabled');
      }
    } on Exception catch (e) {
      debugPrint('❌ Error selecting nearest masjid: $e');
    }
  }

  /// Initialize location services with Context7 MCP best practices
  /// Enhanced with Context7 location provider coordination
  Future<void> _initializeLocationServices() async {
    try {
      debugPrint('📍 LOCATION: Initializing Context7 location services...');

      // Context7 MCP: Use master location provider for initialization
      final locationManager = ref.read(masterLocationManagerProvider.notifier);

      // Check if location is available (initialization happens automatically)
      final isLocationAvailable = ref.read(isLocationServiceReadyProvider);

      if (isLocationAvailable) {
        debugPrint('📍 LOCATION: Master location services initialized successfully');

        // Trigger immediate location fetch for faster response
        await locationManager.getCurrentLocation(useCache: false);
      } else {
        debugPrint('📍 LOCATION: Master location services not available - checking permission status');
        final permissionStatus = ref.read(permissionStatusSelectorProvider);
        debugPrint('📍 LOCATION: Permission status: $permissionStatus');
      }
    } on Exception catch (e) {
      debugPrint('📍 LOCATION: Exception during location services initialization: $e');
    }
  }

  /// Initialize location-aware home page with Context7 MCP comprehensive coordination
  /// Context7 MCP: Comprehensive location initialization using StateNotifier coordination patterns
  Future<void> _initializeLocationAwareHomePageWithCoordination() async {
    try {
      debugPrint('📍 HOME: Starting Context7 MCP location-aware home page initialization...');

      // Context7 MCP: Initialize location readiness coordination
      initializeLocationReadinessCoordination(ref);
      debugPrint('📍 HOME: Location readiness coordination initialized');

      // Step 1: Initialize location services immediately
      await _initializeLocationServices();

      // Step 2: Trigger immediate location detection for faster response
      await _triggerImmediateLocationDetection();

      // Context7 MCP: Set up reactive listener for location readiness changes
      ref.listen<bool>(locationFullyReadyProvider, (previous, next) {
        if (mounted && (previous == false) && next) {
          debugPrint('📍 HOME: Location became fully ready, triggering automatic features');
          _onLocationFullyReady();
        }
      });

      // Context7 MCP: Check if location is already fully ready
      final isLocationFullyReady = ref.read(locationFullyReadyProvider);
      if (isLocationFullyReady) {
        debugPrint('📍 HOME: Location already fully ready, triggering features immediately');
        _onLocationFullyReady();
      } else {
        debugPrint('📍 HOME: Location not yet ready, waiting for readiness...');

        // Context7 MCP: Set up background monitoring for location readiness
        _setupLocationReadinessMonitoring();
      }

      debugPrint('📍 HOME: Context7 MCP location-aware home page initialization completed');
    } on Exception catch (e) {
      debugPrint('📍 HOME: Failed to initialize Context7 MCP location-aware home page: $e');
      // Context7 MCP: Fallback with coordinator error handling
      await _initializeWithLocationCoordinatorFallback(e);
    }
  }

  /// Trigger immediate location detection for faster response
  /// Context7 MCP: Proactive location initialization
  Future<void> _triggerImmediateLocationDetection() async {
    try {
      debugPrint('📍 HOME: Triggering immediate location detection...');

      final locationManager = ref.read(masterLocationManagerProvider.notifier);

      // Try to get current location immediately
      await locationManager.getCurrentLocation(useCache: false);

      debugPrint('📍 HOME: Immediate location detection triggered');
    } on Exception catch (e) {
      debugPrint('📍 HOME: Error triggering immediate location detection: $e');
    }
  }

  /// Initialize location-dependent features with enhanced coordination
  /// Context7 MCP: Coordinated feature initialization with automatic triggers
  Future<void> _initializeLocationDependentFeaturesWithCoordination() async {
    try {
      debugPrint('📍 HOME: Initializing location-dependent features with coordination...');

      // Initialize nearest masjid selection with await
      await _selectNearestMasjidIfNeeded();

      // Request location permission if needed (now that location is ready)
      await _requestLocationPermissionIfNeeded();

      // Context7 MCP: Trigger nearby masjids refresh to ensure immediate detection
      await _triggerNearbyMasjidsRefresh();

      debugPrint('📍 HOME: Location-dependent features with coordination initialized');
    } on Exception catch (e) {
      debugPrint('📍 HOME: Error initializing location-dependent features with coordination: $e');
    }
  }

  /// Initialize without location but keep trying in background
  /// Context7 MCP: Fallback with continuous location attempts
  Future<void> _initializeWithoutLocationButKeepTrying() async {
    try {
      debugPrint('📍 HOME: Initializing without location but keeping location attempts active...');

      // Initialize basic features
      await _initializeWithoutLocation();

      // Context7 MCP: Set up background location monitoring
      _setupBackgroundLocationMonitoring();

      debugPrint('📍 HOME: Initialized without location but background monitoring active');
    } on Exception catch (e) {
      debugPrint('📍 HOME: Error initializing without location: $e');
    }
  }

  /// Trigger nearby masjids refresh for immediate detection
  /// Context7 MCP: Proactive nearby masjids detection
  Future<void> _triggerNearbyMasjidsRefresh() async {
    try {
      debugPrint('📍 HOME: Triggering nearby masjids refresh for immediate detection...');

      // Invalidate nearby masjids provider to trigger refresh
      ref.invalidate(nearbyMasjidsProvider);

      debugPrint('📍 HOME: Nearby masjids refresh triggered');
    } on Exception catch (e) {
      debugPrint('📍 HOME: Error triggering nearby masjids refresh: $e');
    }
  }

  /// Set up background location monitoring for continuous attempts
  /// Context7 MCP: Continuous location monitoring
  void _setupBackgroundLocationMonitoring() {
    try {
      debugPrint('📍 HOME: Setting up background location monitoring...');

      // Listen for location availability changes
      ref.listen<bool>(isLocationServiceReadyProvider, (previous, next) {
        if (mounted && (previous == false) && next) {
          debugPrint('📍 HOME: Location became available in background, initializing features');

          // Initialize location-dependent features when location becomes available
          _initializeLocationDependentFeaturesWithCoordination();
        }
      });

      debugPrint('📍 HOME: Background location monitoring set up');
    } on Exception catch (e) {
      debugPrint('📍 HOME: Error setting up background location monitoring: $e');
    }
  }

  /// Handle when location becomes fully ready with Context7 MCP coordination
  /// Context7 MCP: Automatic feature triggering when location is ready
  void _onLocationFullyReady() {
    try {
      debugPrint('📍 HOME: Location is fully ready, triggering automatic features...');

      // Trigger automatic nearby masjids detection
      ref.invalidate(nearbyMasjidsProvider);

      // Initialize location-dependent features
      _initializeLocationDependentFeaturesWithCoordination();

      debugPrint('📍 HOME: Automatic features triggered successfully');
    } on Exception catch (e) {
      debugPrint('📍 HOME: Error triggering automatic features: $e');
    }
  }

  /// Set up location readiness monitoring with Context7 MCP patterns
  /// Context7 MCP: Continuous monitoring for location readiness changes
  void _setupLocationReadinessMonitoring() {
    try {
      debugPrint('📍 HOME: Setting up location readiness monitoring...');

      // Listen for readiness level changes
      ref.listen<double>(locationReadinessLevelProvider, (previous, next) {
        debugPrint('📍 HOME: Location readiness level changed: $previous -> $next');

        if (next >= 0.8 && (previous ?? 0.0) < 0.8) {
          debugPrint('📍 HOME: High readiness level reached, triggering features');
          _onLocationFullyReady();
        }
      });

      debugPrint('📍 HOME: Location readiness monitoring set up');
    } on Exception catch (e) {
      debugPrint('📍 HOME: Error setting up location readiness monitoring: $e');
    }
  }

  /// Initialize with location coordinator fallback with Context7 MCP error handling
  /// Context7 MCP: Comprehensive fallback with coordinator integration
  Future<void> _initializeWithLocationCoordinatorFallback(Exception error) async {
    try {
      debugPrint('📍 HOME: Initializing with location coordinator fallback...');

      // Reset coordinator state by re-initializing
      ref.read(locationReadinessCoordinatorProvider);

      // Try basic initialization
      await _initializeWithoutLocation();

      // Set up monitoring for future location availability
      _setupLocationReadinessMonitoring();

      debugPrint('📍 HOME: Location coordinator fallback initialization completed');
    } on Exception catch (e) {
      debugPrint('📍 HOME: Error in location coordinator fallback: $e');
      // Final fallback - just initialize without location
      await _initializeWithoutLocation();
    }
  }

  /// Wait for location readiness with timeout
  /// Context7 MCP: Proper coordination timing
  Future<bool> _waitForLocationReadiness() async {
    try {
      debugPrint('📍 HOME: Waiting for location readiness...');

      // Use master location readiness check with timeout
      final timeoutFuture = Future.delayed(const Duration(seconds: 10), () => false);

      // Check readiness periodically until ready or timeout
      final readinessFuture = () async {
        for (var i = 0; i < 100; i++) {
          // Check for up to 10 seconds
          final isReady = ref.read(isLocationServiceReadyProvider);
          if (isReady) return true;
          await Future.delayed(const Duration(milliseconds: 100));
        }
        return false;
      }();

      final isReady = await Future.any<bool>([readinessFuture, timeoutFuture]);

      debugPrint('📍 HOME: Location readiness result: $isReady');
      return isReady;
    } on Exception catch (e) {
      debugPrint('📍 HOME: Error waiting for location readiness: $e');
      return false;
    }
  }

  /// Initialize without location (fallback behavior)
  /// Context7 MCP: Graceful degradation
  Future<void> _initializeWithoutLocation() async {
    try {
      debugPrint('📍 HOME: Initializing without location (fallback mode)...');

      // Still try to request permission for future use
      await _requestLocationPermissionIfNeeded();

      debugPrint('📍 HOME: Fallback initialization completed');
    } on Exception catch (e) {
      debugPrint('📍 HOME: Error in fallback initialization: $e');
    }
  }

  /// Request location permission immediately when homepage loads
  /// Implements Context7 MCP best practices for immediate permission requests
  ///
  /// Context7 MCP Best Practices:
  /// - Request permission when the feature requiring it is displayed (map view)
  /// - Provide clear context for why permission is needed
  /// - Handle all permission states gracefully
  /// - Implement proper error handling and fallbacks
  Future<void> _requestLocationPermissionImmediately() async {
    try {
      debugPrint('📍 LOCATION: Requesting permission immediately on homepage load');

      // Check if permission is already granted to avoid unnecessary requests
      final currentStatus = await UnifiedAppLifecycleManager.instance.getLocationPermissionStatus();
      debugPrint('📍 LOCATION: Current permission status: $currentStatus');

      if (currentStatus.isLocationAvailable) {
        debugPrint('✅ LOCATION: Permission already granted, refreshing providers');
        await _refreshLocationProviders();
        return;
      }

      // If services are disabled, log but don't request permission
      if (currentStatus.areServicesDisabled) {
        debugPrint('⚠️ LOCATION: Location services are disabled, cannot request permission');
        return;
      }

      // If permanently denied, don't request again to avoid permission fatigue
      if (currentStatus.isPermanentlyDenied) {
        debugPrint('❌ LOCATION: Permission permanently denied, not requesting again');
        return;
      }

      // Request permission with proper context - user can see the map and understand why location is needed
      debugPrint('📍 LOCATION: Requesting location permission from system (immediate request)');
      final result = await UnifiedAppLifecycleManager.instance.requestLocationPermission();
      debugPrint('📍 LOCATION: Immediate permission request result: $result');

      // Handle the result and refresh providers if permission granted
      await _handlePermissionResult(result);
    } on Exception catch (e) {
      debugPrint('❌ LOCATION: Error in immediate permission request: $e');
      // Continue with app initialization even if permission request fails
    }
  }

  /// Request location permission proactively based on app state
  /// This is called after tutorial completion on first launch, or on subsequent launches
  ///
  /// Implements Context7 MCP best practices for permission UX:
  /// - Requests permission at appropriate times
  /// - Refreshes providers when permission is granted
  /// - Provides user feedback for different scenarios
  Future<void> _requestLocationPermissionIfNeeded() async {
    try {
      debugPrint('📍 LOCATION: Checking if location permission should be requested proactively');

      // Get first launch status
      final isFirstLaunch = await ref.read(firstLaunchProvider.future);
      debugPrint('📍 LOCATION: First launch status: $isFirstLaunch');

      // Get tutorial completion status
      final tutorialState = ref.read(tutorialNotifierProvider).value;
      final tutorialCompleted = tutorialState?.isCompleted == true || tutorialState?.isSkipped == true;
      debugPrint('📍 LOCATION: Tutorial completed: $tutorialCompleted');

      // Check current permission status first to avoid unnecessary requests
      final currentStatus = await UnifiedAppLifecycleManager.instance.getLocationPermissionStatus();
      debugPrint('📍 LOCATION: Current permission status: $currentStatus');

      // If permission is already granted, no need to request
      if (currentStatus.isLocationAvailable) {
        debugPrint('✅ LOCATION: Permission already granted, refreshing providers');
        await _refreshLocationProviders();
        return;
      }

      // If services are disabled, inform user but don't request permission
      if (currentStatus.areServicesDisabled) {
        debugPrint('⚠️ LOCATION: Location services are disabled');
        return;
      }

      // If permanently denied, don't request again
      if (currentStatus.isPermanentlyDenied) {
        debugPrint('❌ LOCATION: Permission permanently denied, not requesting again');
        return;
      }

      // Context7 MCP: Modified logic - permission should have been requested immediately
      // This function now serves as a fallback for cases where immediate request didn't happen
      // Only request permission if this is first launch and tutorial is completed
      // or if this is not first launch (subsequent app opens)
      final shouldRequestPermission = (!isFirstLaunch) || (isFirstLaunch && tutorialCompleted);

      if (!shouldRequestPermission) {
        debugPrint('📍 LOCATION: Skipping permission request - conditions not met (fallback logic)');
        return;
      }

      // Use the unified lifecycle manager for permission requests
      // This prevents multiple permission dialogs and implements proper debouncing
      debugPrint('📍 LOCATION: Requesting location permission from system (fallback request)');
      final result = await UnifiedAppLifecycleManager.instance.requestLocationPermission();

      debugPrint('📍 LOCATION: Homepage permission request result: $result');

      // Handle the result and refresh providers if permission granted
      await _handlePermissionResult(result);
    } on Exception catch (e) {
      debugPrint('❌ LOCATION: Error requesting location permission on homepage: $e');
    }
  }

  /// Handle permission request result and refresh providers if needed
  ///
  /// Implements Context7 MCP best practices for post-permission handling:
  /// - Refreshes location providers when permission is granted
  /// - Provides appropriate user feedback
  /// - Handles different permission states gracefully
  Future<void> _handlePermissionResult(models.LocationPermissionStatus result) async {
    if (result.isLocationAvailable) {
      debugPrint('✅ LOCATION: Location permission granted on homepage');

      // Refresh location providers to immediately start using location
      await _refreshLocationProviders();

      // Context7 MCP: Trigger immediate location-dependent features refresh
      await _triggerLocationDependentFeaturesRefresh();

      // Show success feedback to user
      if (mounted) {
        showInfoSnackBar('Location access granted! Finding nearby masjids...');
      }
    } else if (result.areServicesDisabled) {
      debugPrint('⚠️ LOCATION: Location services are disabled');

      // Show informative message to user
      if (mounted) {
        showWarningSnackBar('Please enable location services in your device settings');
      }
    } else if (result.isPermanentlyDenied) {
      debugPrint('❌ LOCATION: Location permission permanently denied');

      // Show message with option to open settings
      if (mounted) {
        showErrorSnackBar('Location permission denied. Enable it in app settings to find nearby masjids');
      }
    } else {
      debugPrint('❌ LOCATION: Location permission denied: $result');

      // Show gentle message for temporary denial
      if (mounted) {
        showInfoSnackBar('Location access needed to find nearby masjids');
      }
    }
  }

  /// Context7 MCP: Trigger immediate refresh of location-dependent features
  ///
  /// This ensures that features like nearby masjids and current location button
  /// work immediately after permission is granted for the first time.
  Future<void> _triggerLocationDependentFeaturesRefresh() async {
    try {
      debugPrint('📍 LOCATION: Triggering location-dependent features refresh');

      // Invalidate nearby masjids provider to trigger immediate recalculation
      ref.invalidate(nearbyMasjidsProvider);

      // Invalidate location readiness providers
      ref.invalidate(isLocationServiceReadyProvider);

      // Wait a moment for providers to refresh
      await Future.delayed(const Duration(milliseconds: 200));

      // Trigger immediate location fetch through master provider
      final locationManager = ref.read(masterLocationManagerProvider.notifier);
      await locationManager.getCurrentLocation(useCache: false);

      debugPrint('✅ LOCATION: Location-dependent features refresh completed');
    } on Exception catch (e) {
      debugPrint('❌ LOCATION: Error refreshing location-dependent features: $e');
    }
  }

  /// Refresh location-related providers to immediately use granted permission
  /// Enhanced with Context7 location provider coordination
  Future<void> _refreshLocationProviders() async {
    try {
      debugPrint('📍 LOCATION: Refreshing master location providers after permission grant');

      // Context7 MCP: Refresh master location manager to detect new permission
      final locationManager = ref.read(masterLocationManagerProvider.notifier);

      // Trigger immediate location fetch
      await locationManager.getCurrentLocation(useCache: false);

      // Context7 MCP: Invalidate master location providers to refresh their state
      ref.invalidate(masterLocationManagerProvider);

      // Invalidate nearby masjids providers to trigger location-based search
      ref.invalidate(nearbyMasjidsProvider);

      // Context7 MCP: Invalidate location selectors to refresh location state
      ref.invalidate(currentLocationSelectorProvider);

      debugPrint('✅ LOCATION: Location providers refreshed successfully');
    } on Exception catch (e) {
      debugPrint('❌ LOCATION: Error refreshing location providers: $e');
    }
  }

  @override
  void dispose() {
    // Dispose the controller
    _controller.dispose();

    // Location lifecycle manager is now handled by UnifiedAppLifecycleManager

    // Enhanced location manager is disposed by the provider
    // Lifecycle management is now handled by UnifiedAppLifecycleManager

    // Context7 MCP: Dispose permission state change handler
    PermissionStateChangeHandler.instance.dispose();

    // Clean up the notifier when the widget is disposed
    masjidCounts.dispose();

    // The MemorySafetyMixin will automatically dispose the search controller
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Initialize home page navigation tracking
    ref.watch(homePageInitializerProvider);

    // Context7 MCP: Set up nearby masjids listener in build method (Riverpod best practice)
    // This ensures proper lifecycle management and prevents debugDoingBuild assertion errors
    ref.listen(nearbyMasjidsProvider, (previous, next) {
      next.whenData((nearbyMasjids) {
        // Update the nearest masjid ID whenever nearby masjids data is available
        if (nearbyMasjids.isNotEmpty && mounted) {
          final nearestMasjid = nearbyMasjids.first.masjid;
          setState(() {
            _nearestMasjidId = nearestMasjid.id;
          });
        }

        // Only select nearest masjid if conditions are met
        if (!_hasSelectedRandomMasjid &&
            _selectedMasjid == null &&
            !_userClosedRandomCard &&
            nearbyMasjids.isNotEmpty) {
          // Check if search is not active
          final searchQuery = ref.read(homeSearchQueryProvider);
          if (searchQuery.trim().isEmpty) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _selectNearestMasjidIfNeeded();
            });
          }
        }
      });
    });

    return SizedBox.expand(
      child: Stack(
        children: [
          // Main content
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top bar with menu icon and search
              HomeTopBar(searchController: _searchController),

              // Next prayer time card
              const HomePrayerCard(),

              // Google Maps widget - now takes up more space without the section title above
              Expanded(
                child: Container(
                  margin: const EdgeInsets.fromLTRB(16.0, 2.0, 16.0, 2.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withAlpha(76),
                        spreadRadius: 1,
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: OptimizedMapWidget(
                    onMasjidCountsUpdated: (active, inactive) {
                      masjidCounts.value = {'active': active, 'inactive': inactive};
                    },
                    // Pass the masjid counts to display in the overlay
                    masjidCounts: masjidCounts,
                    // Handle masjid tap to show card at bottom
                    onMasjidTapped: (masjid) {
                      setState(() {
                        _selectedMasjid = masjid;
                      });
                    },
                    // Pass selected masjid and overlay properties
                    selectedMasjid: _selectedMasjid,
                    isNearestMasjid: _selectedMasjid?.id == _nearestMasjidId,
                    onOverlayClose: () {
                      setState(() {
                        // If this was the nearest selected masjid, mark that user closed it
                        if (_hasSelectedRandomMasjid && _selectedMasjid != null) {
                          _userClosedRandomCard = true;
                          debugPrint('📍 User closed nearest masjid card');
                        }
                        _selectedMasjid = null;
                      });
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void handleRetry() {
    // Retry location services refresh
    _refreshLocationServicesOnResume();

    // Retry selecting nearest masjid
    _selectNearestMasjidIfNeeded();

    // Retry location permission request if needed
    _requestLocationPermissionIfNeeded();
  }
}
