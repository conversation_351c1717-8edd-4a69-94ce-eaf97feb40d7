import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_islamic_icons/flutter_islamic_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/constants/hive_constants.dart';
import '../../../../core/providers/first_launch_provider.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/storage/hive_service.dart';
import '../../../../core/utils/l10n.dart';
import '../../../../core/utils/language_helper.dart';
import '../../../../shared/widgets/app_safe_area.dart';
import '../../../prayer_times/domain/providers/custom_calculation_method_provider.dart';

/// Landing page shown only on first launch
class LandingPageFixed extends ConsumerStatefulWidget {
  /// Constructor
  const LandingPageFixed({super.key});

  @override
  ConsumerState<LandingPageFixed> createState() => _LandingPageFixedState();
}

class _LandingPageFixedState extends ConsumerState<LandingPageFixed> {
  // Selected language and calculation method
  String _selectedLanguage = 'en';
  CustomCalculationMethod _selectedMethod = CustomCalculationMethod.jafari;
  bool _isProcessing = false;

  /// Complete the onboarding process following Context7 MCP best practices
  ///
  /// This method implements proper async state management with:
  /// - State synchronization coordination
  /// - Timeout protection
  /// - Resilience patterns with fallback mechanisms
  /// - Comprehensive error handling
  Future<void> _completeOnboarding() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      debugPrint('🎓 ONBOARDING: Starting onboarding completion process...');

      // Phase 1: Save user preferences with timeout protection
      await _saveUserPreferencesWithTimeout();

      // Phase 2: Request notification permissions (non-blocking)
      _requestNotificationPermissionsAsync();

      // Phase 3: Mark onboarding as completed with state synchronization
      await _markOnboardingCompletedWithSync();

      // Phase 4: Navigate with proper state coordination
      await _navigateToHomeWithCoordination();

      debugPrint('✅ ONBOARDING: Onboarding completed successfully');
    } on Exception catch (e) {
      debugPrint('❌ ONBOARDING: Error during onboarding completion: $e');
      await _handleOnboardingError(e);
    }
  }

  /// Save user preferences with timeout protection - Context7 MCP pattern
  Future<void> _saveUserPreferencesWithTimeout() async {
    try {
      debugPrint('🎓 ONBOARDING: Saving user preferences with timeout...');

      // Use Future.any for timeout protection
      await Future.any([
        _saveUserPreferences(),
        Future.delayed(const Duration(seconds: 10), () {
          throw TimeoutException('User preferences save timed out', const Duration(seconds: 10));
        }),
      ]);

      debugPrint('✅ ONBOARDING: User preferences saved successfully');
    } catch (e) {
      debugPrint('❌ ONBOARDING: Failed to save user preferences: $e');
      rethrow;
    }
  }

  /// Save user preferences
  Future<void> _saveUserPreferences() async {
    // Save the selected language with proper async handling
    await _saveLanguageDirectly(_selectedLanguage);
    debugPrint('✅ Language saved: $_selectedLanguage');

    // Save the calculation method directly to SharedPreferences
    await _saveCalculationMethodDirectly(_selectedMethod);
    debugPrint('✅ Calculation method saved: ${_selectedMethod.name}');
  }

  /// Save language directly to avoid provider loading issues
  Future<void> _saveLanguageDirectly(String languageCode) async {
    try {
      debugPrint('🎓 ONBOARDING: Saving language directly: $languageCode');

      // Save to SharedPreferences directly
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('language_code', languageCode);
      await prefs.setString('locale_code', languageCode);

      debugPrint('✅ Language saved directly to SharedPreferences: $languageCode');
    } catch (e) {
      debugPrint('❌ Error saving language directly: $e');
      rethrow;
    }
  }

  /// Request notification permissions asynchronously (non-blocking)
  void _requestNotificationPermissionsAsync() {
    debugPrint('🔔 ONBOARDING: Starting async notification permission request...');

    // Run in background without blocking onboarding completion
    unawaited(_performAsyncNotificationRequest());
  }

  /// Perform async notification request with proper error handling
  Future<void> _performAsyncNotificationRequest() async {
    try {
      await _requestNotificationPermissions();
      debugPrint('✅ ONBOARDING: Notification permissions requested successfully');
    } on Exception catch (e) {
      debugPrint('⚠️ ONBOARDING: Notification permission request failed (non-blocking): $e');
      // This is non-critical, so we don't block onboarding completion
    }
  }

  /// Mark onboarding as completed with state synchronization
  Future<void> _markOnboardingCompletedWithSync() async {
    try {
      debugPrint('🎓 ONBOARDING: Marking onboarding as completed with sync...');

      // Mark first launch as completed
      await _setFirstLaunchCompletedDirectly();
      debugPrint('✅ First launch marked as completed');

      // Wait for state synchronization - Context7 MCP pattern
      await _waitForStateSynchronization();
      debugPrint('✅ State synchronization completed');
    } catch (e) {
      debugPrint('❌ ONBOARDING: Failed to mark onboarding as completed: $e');
      rethrow;
    }
  }

  /// Wait for state synchronization across providers
  Future<void> _waitForStateSynchronization() async {
    try {
      // Invalidate the first launch provider to force refresh
      ref.invalidate(firstLaunchProvider);

      // Wait for the provider to refresh with timeout
      await ref
          .refresh(firstLaunchProvider.future)
          .timeout(
            const Duration(seconds: 5),
            onTimeout: () {
              debugPrint('⚠️ ONBOARDING: State synchronization timed out');
              return true; // Default to first launch if timeout
            },
          );

      // Add delay to ensure all dependent providers are updated
      await Future.delayed(const Duration(milliseconds: 750));
    } on Exception catch (e) {
      debugPrint('⚠️ ONBOARDING: State synchronization failed: $e');
      // Continue anyway - this is not critical for navigation
    }
  }

  /// Navigate to home with proper coordination - Context7 MCP pattern
  Future<void> _navigateToHomeWithCoordination() async {
    if (!mounted) return;

    try {
      debugPrint('🎓 ONBOARDING: Attempting coordinated navigation to home...');

      // Use timeout for navigation attempt
      await Future.any([
        _performNavigation(),
        Future.delayed(const Duration(seconds: 5), () {
          throw TimeoutException('Navigation timed out', const Duration(seconds: 5));
        }),
      ]);

      debugPrint('✅ ONBOARDING: Navigation completed successfully');
    } on Exception catch (e) {
      debugPrint('❌ ONBOARDING: Navigation failed: $e');
      await _handleNavigationFailure(e);
    }
  }

  /// Perform the actual navigation
  Future<void> _performNavigation() async {
    if (!mounted) return;

    // Use GoRouter for navigation
    GoRouter.of(context).go('/home');

    // Wait a moment to ensure navigation is processed
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// Handle navigation failure with fallback mechanisms
  Future<void> _handleNavigationFailure(Object error) async {
    if (!mounted) return;

    debugPrint('🔄 ONBOARDING: Attempting fallback navigation...');

    try {
      // Fallback 1: Try Navigator.pushReplacementNamed
      await Navigator.of(context).pushReplacementNamed('/home');
      debugPrint('✅ ONBOARDING: Fallback navigation succeeded');
    } on Exception catch (fallbackError) {
      debugPrint('❌ ONBOARDING: Fallback navigation also failed: $fallbackError');

      // Fallback 2: Reset processing state and show error
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });

        _showNavigationError();
      }
    }
  }

  /// Handle onboarding errors with user feedback
  Future<void> _handleOnboardingError(Exception error) async {
    if (!mounted) return;

    // Reset processing state
    setState(() {
      _isProcessing = false;
    });

    // Show error message with retry option
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${context.l10n?.errorOccurred ?? 'Error'}: ${error.toString().split('\n')[0]}'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: context.l10n?.retry ?? 'Retry',
          textColor: Colors.white,
          onPressed: _completeOnboarding,
        ),
      ),
    );
  }

  /// Show navigation error to user
  void _showNavigationError() {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.l10n?.errorOccurred ?? 'Navigation failed. Please restart the app.'),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 7),
        action: SnackBarAction(
          label: context.l10n?.retry ?? 'Retry',
          textColor: Colors.white,
          onPressed: _completeOnboarding,
        ),
      ),
    );
  }

  // Set first launch completed directly using multiple storage methods
  // This ensures at least one method succeeds
  Future<void> _setFirstLaunchCompletedDirectly() async {
    var success = false;
    var errorMessage = '';

    debugPrint('🔧 LANDING PAGE: Starting to set first launch completed...');

    // Try using the provider first
    try {
      await ref.read(firstLaunchNotifierProvider).setFirstLaunchCompleted();
      debugPrint('✅ LANDING PAGE: First launch status set to false via provider');
      success = true;
    } on Exception catch (e) {
      errorMessage += 'Provider method failed: $e\n';
      debugPrint('❌ LANDING PAGE: Error setting first launch via provider: $e');
    }

    // Try using Hive directly as a backup
    try {
      // Make sure the box is open first
      final box = await HiveService.openBox<bool>(HiveConstants.settingsBox);
      await box.put(HiveConstants.firstLaunchKey, false);
      debugPrint('✅ LANDING PAGE: First launch status set to false directly in Hive box');
      success = true;
    } on Exception catch (e) {
      errorMessage += 'Hive direct method failed: $e\n';
      debugPrint('❌ LANDING PAGE: Error setting first launch directly in Hive: $e');
    }

    // Try using SharedPreferences as a final fallback
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('first_launch', false);
      debugPrint('✅ LANDING PAGE: First launch status set to false in SharedPreferences');
      success = true;
    } on Exception catch (e) {
      errorMessage += 'SharedPreferences method failed: $e\n';
      debugPrint('❌ LANDING PAGE: Error setting first launch in SharedPreferences: $e');
    }

    // If all methods failed, throw an error
    if (!success) {
      debugPrint('🔴 LANDING PAGE: All methods to set first launch failed!');
      throw Exception('All methods to set first launch failed: $errorMessage');
    }

    debugPrint('🔄 LANDING PAGE: Invalidating first launch provider...');
    // Force refresh the first launch provider
    ref.invalidate(firstLaunchProvider);
    debugPrint('✅ LANDING PAGE: First launch setup completed successfully!');
  }

  // Save calculation method directly to SharedPreferences
  // This avoids the circular dependency issue
  Future<void> _saveCalculationMethodDirectly(CustomCalculationMethod method) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('custom_calculation_method', method.index);
      debugPrint('Saved calculation method directly: ${method.name}');
    } on Exception catch (e) {
      debugPrint('Error saving calculation method directly: $e');
      rethrow;
    }
  }

  // Update language immediately when selected
  Future<void> _updateLanguage(String language) async {
    if (_selectedLanguage != language) {
      setState(() {
        _selectedLanguage = language;
      });
      // Update app language immediately
      unawaited(LanguageHelper.changeLanguage(ref, language));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC), // Light gray background for modern look
      body: AppSafeArea.fullScreen(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 16.0), // More compact padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 24), // Reduced top spacing
              // Compact logo section with moderate size
              Container(
                width: 90, // Reduced from 120 for more compact design
                height: 90, // Reduced from 120 for more compact design
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF2196F3), Color(0xFF1976D2), Color(0xFF0D47A1)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.25), // Slightly reduced shadow
                      blurRadius: 16, // Reduced shadow blur
                      offset: const Offset(0, 6), // Reduced shadow offset
                      spreadRadius: 1, // Reduced spread
                    ),
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.08), // Lighter secondary shadow
                      blurRadius: 32, // Reduced secondary shadow
                      offset: const Offset(0, 12), // Reduced secondary offset
                      spreadRadius: 2, // Reduced secondary spread
                    ),
                  ],
                ),
                child: const Icon(
                  FlutterIslamicIcons.mosque,
                  size: 45, // Reduced from 60 for more compact design
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 24), // Reduced spacing
              // Modern title with gradient text effect
              ShaderMask(
                shaderCallback: (bounds) => const LinearGradient(
                  colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ).createShader(bounds),
                child: Text(
                  context.l10n?.welcomeToMasajidAlBahrain ??
                      (_selectedLanguage == 'en' ? 'Welcome to Masajid AlBahrain' : 'مرحباً بك في مساجد البحرين'),
                  style: const TextStyle(
                    fontSize: 24, // Reduced from 28 for more compact design
                    fontWeight: FontWeight.bold,
                    color: Colors.white, // This will be masked by the gradient
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 28), // Reduced from 40 for more compact design
              // Modern language selection card
              _buildModernSection(
                title:
                    context.l10n?.pleaseSelectPreferredLanguage ??
                    (_selectedLanguage == 'en'
                        ? 'Please select your preferred language'
                        : 'الرجاء اختيار اللغة المفضلة'),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildModernLanguageOption(
                        language: 'en',
                        title: 'English',
                        isSelected: _selectedLanguage == 'en',
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildModernLanguageOption(
                        language: 'ar',
                        title: 'العربية',
                        isSelected: _selectedLanguage == 'ar',
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24), // Reduced from 32 for more compact design
              // Modern calculation method selection card
              _buildModernSection(
                title:
                    context.l10n?.pleaseChooseCalculationMethod ??
                    (_selectedLanguage == 'en'
                        ? 'Please choose the preferred calculation method'
                        : 'الرجاء اختيار طريقة الحساب المفضلة'),
                child: Column(
                  children: [
                    _buildModernCalculationMethodOption(
                      method: CustomCalculationMethod.jafari,
                      title:
                          context.l10n?.masajidAlBahrainJafari ??
                          (_selectedLanguage == 'en' ? 'Masajid Al Bahrain - JAFARI' : 'مساجد البحرين - جعفري'),
                      description:
                          context.l10n?.jafariCalculationDescription ??
                          (_selectedLanguage == 'en'
                              ? 'Calculation method used by Jafari mosques in Bahrain'
                              : 'طريقة الحساب المستخدمة في المساجد الجعفرية في البحرين'),
                      isSelected: _selectedMethod == CustomCalculationMethod.jafari,
                    ),
                    const SizedBox(height: 12), // Reduced from 16 for more compact design
                    _buildModernCalculationMethodOption(
                      method: CustomCalculationMethod.sunni,
                      title:
                          context.l10n?.masajidAlBahrainSunni ??
                          (_selectedLanguage == 'en' ? 'Masajid Al Bahrain - SUNNI' : 'مساجد البحرين - سني'),
                      description:
                          context.l10n?.sunniCalculationDescription ??
                          (_selectedLanguage == 'en'
                              ? 'Calculation method used by Sunni mosques in Bahrain'
                              : 'طريقة الحساب المستخدمة في المساجد السنية في البحرين'),
                      isSelected: _selectedMethod == CustomCalculationMethod.sunni,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24), // Reduced from 32 for more compact design
              // Modern footer section
              Container(
                padding: const EdgeInsets.all(20), // Reduced from 24 for more compact design
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 20, offset: const Offset(0, 4)),
                  ],
                ),
                child: Column(
                  children: [
                    Text(
                      context.l10n?.optionsCanBeChangedLater ??
                          (_selectedLanguage == 'en'
                              ? 'Options can be changed later in the app'
                              : 'يمكن تغيير الخيارات لاحقاً في التطبيق'),
                      style: TextStyle(fontSize: 14, color: Colors.grey.shade600, fontStyle: FontStyle.italic),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20), // Reduced from 24 for more compact design
                    // Modern gradient button
                    Container(
                      height: 52, // Reduced from 56 for more compact design
                      width: double.infinity,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(28),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withValues(alpha: 0.3),
                            blurRadius: 15,
                            offset: const Offset(0, 6),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        onPressed: _isProcessing ? null : _completeOnboarding,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(28)),
                        ),
                        child: _isProcessing
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2.5),
                              )
                            : Text(
                                context.l10n?.next ?? (_selectedLanguage == 'en' ? 'Next' : 'متابعة'),
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ), // Reduced from 18 for more compact design
                              ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24), // Reduced from 32 for more compact design
            ],
          ),
        ),
      ),
    );
  }

  // Build a modern section with card-like design
  Widget _buildModernSection({required String title, required Widget child}) {
    return Container(
      padding: const EdgeInsets.all(20), // Reduced from 24 for more compact design
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 20, offset: const Offset(0, 4))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade800,
            ), // Reduced from 18 for more compact design
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16), // Reduced from 20 for more compact design
          child,
        ],
      ),
    );
  }

  // Build a modern language option with enhanced design
  Widget _buildModernLanguageOption({required String language, required String title, required bool isSelected}) {
    return GestureDetector(
      onTap: () => _updateLanguage(language),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 18), // Reduced for more compact design
        decoration: BoxDecoration(
          gradient: isSelected
              ? const LinearGradient(
                  colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected ? null : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: isSelected ? Colors.transparent : Colors.grey.shade200, width: 2),
          boxShadow: isSelected
              ? [BoxShadow(color: Colors.blue.withValues(alpha: 0.3), blurRadius: 15, offset: const Offset(0, 6))]
              : [BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 10, offset: const Offset(0, 2))],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 15, // Reduced from 16 for more compact design
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                color: isSelected ? Colors.white : Colors.grey.shade700,
              ),
            ),
            if (isSelected) ...[
              const SizedBox(width: 8),
              const Icon(Icons.check_circle, color: Colors.white, size: 20),
            ],
          ],
        ),
      ),
    );
  }

  // Build a modern calculation method option with enhanced design
  Widget _buildModernCalculationMethodOption({
    required CustomCalculationMethod method,
    required String title,
    required String description,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedMethod = method;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(18), // Reduced from 20 for more compact design
        decoration: BoxDecoration(
          gradient: isSelected
              ? const LinearGradient(
                  colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected ? null : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: isSelected ? Colors.transparent : Colors.grey.shade200, width: 2),
          boxShadow: isSelected
              ? [BoxShadow(color: Colors.blue.withValues(alpha: 0.3), blurRadius: 15, offset: const Offset(0, 6))]
              : [BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 10, offset: const Offset(0, 2))],
        ),
        child: Row(
          children: [
            // Radio button indicator
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: isSelected ? Colors.white : Colors.grey.shade400, width: 2),
                color: isSelected ? Colors.white : Colors.transparent,
              ),
              child: isSelected ? const Icon(Icons.check, size: 16, color: Color(0xFF2196F3)) : null,
            ),
            const SizedBox(width: 16),
            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 15, // Reduced from 16 for more compact design
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                      color: isSelected ? Colors.white : Colors.grey.shade800,
                    ),
                  ),
                  const SizedBox(height: 3), // Reduced from 4 for more compact design
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 13, // Reduced from 14 for more compact design
                      color: isSelected ? Colors.white.withValues(alpha: 0.9) : Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Request notification permissions during onboarding
  /// This ensures the system permission dialog is shown to the user
  /// Following Context7 MCP best practices for robust permission handling
  Future<void> _requestNotificationPermissions() async {
    try {
      debugPrint('🔔 ONBOARDING: Starting notification permission request...');

      // Use multiple timeout layers for maximum resilience - Context7 MCP pattern
      final result = await Future.any([
        _performNotificationPermissionRequestWithRetry(),
        Future.delayed(const Duration(seconds: 15), () {
          debugPrint('⏰ ONBOARDING: Notification permission request timed out after 15s');
          return false;
        }),
      ]);

      debugPrint('🔔 ONBOARDING: Notification permission request completed with result: $result');
    } on Exception catch (e, stackTrace) {
      debugPrint('❌ ONBOARDING: Error requesting notification permissions: $e');
      debugPrint('Stack trace: $stackTrace');
      // Continue with onboarding even if notification setup fails
      // This ensures the user can still use the app - Context7 MCP resilience pattern
    }
  }

  /// Perform notification permission request with retry mechanism
  Future<bool> _performNotificationPermissionRequestWithRetry() async {
    const maxRetries = 2;
    var attempt = 0;

    while (attempt < maxRetries) {
      try {
        attempt++;
        debugPrint('🔔 ONBOARDING: Notification permission attempt $attempt/$maxRetries');

        final result = await _performNotificationPermissionRequest().timeout(
          const Duration(seconds: 8),
          onTimeout: () {
            debugPrint('⏰ ONBOARDING: Single permission request timed out');
            return false;
          },
        );

        if (result) {
          debugPrint('✅ ONBOARDING: Notification permission granted on attempt $attempt');
          return true;
        }

        debugPrint('⚠️ ONBOARDING: Notification permission denied on attempt $attempt');

        // Wait before retry
        if (attempt < maxRetries) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } catch (e) {
        debugPrint('❌ ONBOARDING: Notification permission attempt $attempt failed: $e');

        if (attempt >= maxRetries) {
          rethrow;
        }

        // Wait before retry
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }

    debugPrint('⚠️ ONBOARDING: All notification permission attempts failed');
    return false;
  }

  /// Perform the actual notification permission request
  /// Separated for better error handling and testing
  Future<bool> _performNotificationPermissionRequest() async {
    try {
      // Use the existing notification service from the app's service locator
      // instead of creating a new instance - Context7 MCP best practice
      final notificationService = NotificationService();

      // Initialize with timeout
      final initResult = await notificationService.initialize().timeout(const Duration(seconds: 5));

      debugPrint('🔔 ONBOARDING: Notification service initialized: $initResult');

      // Request permissions with timeout - Context7 MCP best practice
      final granted = await notificationService.requestPermissions().timeout(const Duration(seconds: 5));

      if (granted) {
        debugPrint('✅ ONBOARDING: Notification permissions granted');
      } else {
        debugPrint('❌ ONBOARDING: Notification permissions denied');
      }

      return granted;
    } on Exception catch (e) {
      debugPrint('❌ ONBOARDING: Error in permission request: $e');
      return false;
    }
  }
}
