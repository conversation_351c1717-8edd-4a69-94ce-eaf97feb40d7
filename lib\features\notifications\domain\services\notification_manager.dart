import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/notifications/providers/unified_notification_provider.dart';
import '../../../../core/providers/app_settings_selectors.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/services/prayer_notification_scheduler.dart';
import '../../../../core/utils/logger.dart';
import '../../../prayer_times/presentation/providers/prayer_times_provider.dart';

/// Simple notification manager following Context7 MCP best practices
class NotificationManager {
  /// Constructor
  NotificationManager(this._ref);

  /// Riverpod reference
  final Ref _ref;

  /// Notification service instance
  final NotificationService _notificationService = NotificationService();

  /// Prayer notification scheduler
  final PrayerNotificationScheduler _scheduler = PrayerNotificationScheduler();

  /// Whether notifications are available
  bool _notificationsAvailable = false;

  /// Getter for notifications availability
  bool get notificationsAvailable => _notificationsAvailable;

  /// Initialize the notification manager
  Future<void> initialize() async {
    try {
      AppLogger.debug('NotificationManager: Starting initialization');

      // Initialize notification service
      final initialized = await _notificationService.initialize();
      if (!initialized) {
        AppLogger.error('NotificationManager: Failed to initialize notification service');
        return;
      }

      _notificationsAvailable = true;
      AppLogger.debug('NotificationManager: Initialization complete');
    } on Exception catch (e) {
      _notificationsAvailable = false;
      AppLogger.error('NotificationManager: Initialization failed - $e');
    }
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    try {
      AppLogger.debug('NotificationManager: Requesting permissions');

      if (!_notificationsAvailable) {
        AppLogger.warning('NotificationManager: Service not available');
        return false;
      }

      final granted = await _notificationService.requestPermissions();
      AppLogger.debug('NotificationManager: Permissions granted: $granted');

      return granted;
    } on Exception catch (e) {
      AppLogger.error('NotificationManager: Permission request failed - $e');
      return false;
    }
  }

  /// Schedule prayer time notifications
  Future<void> schedulePrayerTimeNotifications() async {
    try {
      AppLogger.debug('NotificationManager: Scheduling prayer time notifications');

      if (!_notificationsAvailable) {
        AppLogger.warning('NotificationManager: Service not available');
        return;
      }

      // Get current settings
      // Context7 MCP: Use unified notification settings provider
      final notificationSettingsState = await _ref.read(unifiedNotificationSettingsProvider.future);

      // Only proceed if notifications are enabled
      if (!notificationSettingsState.generalSettings.globallyEnabled) {
        AppLogger.debug('NotificationManager: Notifications disabled');
        return;
      }

      // Get prayer times
      final prayerTimes = _ref.read(allPrayerTimesProvider);

      // Get language code
      final languageCode = _ref.read(currentLanguageCodeProvider);

      // Context7 MCP: Extract the correct NotificationSettings from the state
      // The scheduler expects NotificationSettings, not NotificationSettingsState
      final notificationSettings = notificationSettingsState.generalSettings;

      // Schedule notifications
      await _scheduler.schedulePrayerNotifications(
        prayerTimes: prayerTimes,
        settings: notificationSettings,
        languageCode: languageCode,
      );

      AppLogger.debug('NotificationManager: Prayer time notifications scheduled');
    } on Exception catch (e) {
      AppLogger.error('NotificationManager: Failed to schedule notifications - $e');
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      AppLogger.debug('NotificationManager: Cancelling all notifications');

      if (!_notificationsAvailable) {
        AppLogger.warning('NotificationManager: Service not available');
        return;
      }

      await _scheduler.cancelAllNotifications();
      AppLogger.debug('NotificationManager: All notifications cancelled');
    } on Exception catch (e) {
      AppLogger.error('NotificationManager: Failed to cancel notifications - $e');
    }
  }

  /// Show a test notification
  Future<void> showTestNotification() async {
    try {
      AppLogger.debug('NotificationManager: Showing test notification');

      if (!_notificationsAvailable) {
        AppLogger.warning('NotificationManager: Service not available');
        return;
      }

      final languageCode = _ref.read(currentLanguageCodeProvider);
      final title = languageCode == 'ar' ? 'إشعار تجريبي' : 'Test Notification';
      final body = languageCode == 'ar'
          ? 'هذا إشعار تجريبي للتأكد من عمل النظام'
          : 'This is a test notification to verify the system is working';

      await _notificationService.showNotification(id: 999, title: title, body: body, payload: 'test');

      AppLogger.debug('NotificationManager: Test notification shown');
    } on Exception catch (e) {
      AppLogger.error('NotificationManager: Failed to show test notification - $e');
    }
  }

  /// Get pending notifications count
  Future<int> getPendingNotificationsCount() async {
    try {
      if (!_notificationsAvailable) {
        return 0;
      }

      return await _scheduler.getPendingNotificationsCount();
    } on Exception catch (e) {
      AppLogger.error('NotificationManager: Failed to get pending count - $e');
      return 0;
    }
  }

  /// Debug notification system
  Future<Map<String, dynamic>> debugNotificationSystem() async {
    try {
      final systemStatus = await _notificationService.getNotificationSystemStatus();
      final pendingCount = await getPendingNotificationsCount();

      return {...systemStatus, 'manager_available': _notificationsAvailable, 'pending_count': pendingCount};
    } on Exception catch (e) {
      AppLogger.error('NotificationManager: Failed to debug system - $e');
      return {'error': e.toString()};
    }
  }
}

/// Provider for the notification manager
final notificationManagerProvider = Provider<NotificationManager>((ref) {
  return NotificationManager(ref);
});
