import 'dart:convert';
import 'dart:typed_data';

import '../errors/app_error.dart';
import '../interfaces/unified_storage_service_interface.dart';
import '../utils/app_logger.dart';
import '../utils/result.dart';
import 'storage_repository_interface.dart';

/// Context7 MCP: Unified Storage Repository Implementation
///
/// Implements the repository pattern following Context7 MCP best practices.
/// This class provides a clean abstraction over the UnifiedStorageService,
/// implementing caching, error handling, and performance optimization.
///
/// **Architecture:**
/// ```
/// Business Logic Layer
///        ↓
/// Repository Interface (this class)
///        ↓
/// Storage Service Layer
///        ↓
/// Physical Storage (Hive, Supabase, etc.)
/// ```
///
/// **Key Features:**
/// - ✅ Repository pattern implementation
/// - ✅ Comprehensive caching with TTL
/// - ✅ Performance metrics and monitoring
/// - ✅ Batch operations optimization
/// - ✅ Transaction support
/// - ✅ Error handling with Result pattern
/// - ✅ Structured logging with metadata
class UnifiedStorageRepository implements StorageRepositoryInterface {
  /// Creates a new UnifiedStorageRepository
  ///
  /// **Parameters:**
  /// - [storageService]: The underlying storage service
  /// - [config]: Repository configuration options
  UnifiedStorageRepository({
    required UnifiedStorageServiceInterface storageService,
    StorageRepositoryConfig config = const StorageRepositoryConfig(),
  }) : _storageService = storageService,
       _config = config {
    _initializeRepository();
  }

  final UnifiedStorageServiceInterface _storageService;
  final StorageRepositoryConfig _config;

  // Cache management
  final Map<String, _CacheEntry> _cache = {};
  final Map<String, DateTime> _lastAccess = {};

  // Performance metrics
  int _totalOperations = 0;
  int _cacheHits = 0;
  int _cacheMisses = 0;
  final Map<String, int> _operationCounts = {};
  final Map<String, Duration> _operationTimes = {};

  /// Initialize the repository
  void _initializeRepository() {
    AppLogger.info(
      'UnifiedStorageRepository initialized',
      metadata: {
        'caching_enabled': _config.enableCaching,
        'cache_size': _config.cacheSize,
        'cache_ttl_minutes': _config.cacheTtl.inMinutes,
        'compression_enabled': _config.enableCompression,
        'encryption_enabled': _config.enableEncryption,
      },
    );
  }

  // ============================================================================
  // BASIC CRUD OPERATIONS
  // ============================================================================

  @override
  Future<Result<void>> store<T>(String key, T value, {Map<String, dynamic>? metadata}) async {
    return _executeWithMetrics('store', () async {
      try {
        // Store in underlying service
        final result = await _storageService.store(
          key,
          value,
          type: StorageType.regular,
          options: StorageOptions(
            compress: _config.enableCompression,
            encrypt: _config.enableEncryption,
            metadata: metadata,
          ),
        );

        if (result.isSuccess) {
          // Update cache if enabled
          if (_config.enableCaching) {
            _updateCache(key, value, metadata);
          }

          AppLogger.debug(
            'Data stored successfully',
            metadata: {
              'key': key,
              'type': T.toString(),
              'cached': _config.enableCaching,
              'metadata_provided': metadata != null,
            },
          );
        }

        return result;
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Storage operation failed', e, stackTrace, 'repository_store');
        return Result.failure(AppError.storage('Failed to store data: $e'));
      }
    });
  }

  @override
  Future<Result<T?>> retrieve<T>(String key) async {
    return _executeWithMetrics('retrieve', () async {
      try {
        // Check cache first if enabled
        if (_config.enableCaching) {
          final cachedValue = _getFromCache<T>(key);
          if (cachedValue != null) {
            _cacheHits++;
            AppLogger.debug('Cache hit for key', metadata: {'key': key, 'type': T.toString()});
            return Result.success(cachedValue);
          }
          _cacheMisses++;
        }

        // Retrieve from underlying service
        final result = await _storageService.retrieve<T>(key, type: StorageType.regular);

        if (result.isSuccess && result.valueOrNull != null) {
          // Update cache if enabled
          if (_config.enableCaching) {
            _updateCache(key, result.valueOrNull as T, null);
          }

          AppLogger.debug(
            'Data retrieved successfully',
            metadata: {'key': key, 'type': T.toString(), 'from_cache': false},
          );
        }

        return result;
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Retrieve operation failed', e, stackTrace, 'repository_retrieve');
        return Result.failure(AppError.storage('Failed to retrieve data: $e'));
      }
    });
  }

  @override
  Future<Result<bool>> exists(String key) async {
    return _executeWithMetrics('exists', () async {
      try {
        // Check cache first if enabled
        if (_config.enableCaching && _cache.containsKey(key)) {
          final entry = _cache[key]!;
          if (!_isCacheEntryExpired(entry)) {
            return const Result.success(true);
          }
        }

        // Check underlying service
        final result = await _storageService.exists(key, type: StorageType.regular);

        AppLogger.debug('Existence check completed', metadata: {'key': key, 'exists': result.valueOrNull ?? false});

        return result;
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Exists operation failed', e, stackTrace, 'repository_exists');
        return Result.failure(AppError.storage('Failed to check existence: $e'));
      }
    });
  }

  @override
  Future<Result<void>> remove(String key) async {
    return _executeWithMetrics('remove', () async {
      try {
        // Remove from underlying service
        final result = await _storageService.removeData(key, type: StorageType.regular);

        if (result.isSuccess) {
          // Remove from cache if enabled
          if (_config.enableCaching) {
            _cache.remove(key);
            _lastAccess.remove(key);
          }

          AppLogger.debug('Data removed successfully', metadata: {'key': key, 'cache_cleared': _config.enableCaching});
        }

        return result;
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Remove operation failed', e, stackTrace, 'repository_remove');
        return Result.failure(AppError.storage('Failed to remove data: $e'));
      }
    });
  }

  @override
  Future<Result<void>> clear() async {
    return _executeWithMetrics('clear', () async {
      try {
        // Clear cache
        if (_config.enableCaching) {
          _cache.clear();
          _lastAccess.clear();
        }

        // Note: UnifiedStorageService doesn't have a clear method
        // This would need to be implemented based on specific requirements
        AppLogger.warning('Clear operation not fully implemented', metadata: {'cache_cleared': _config.enableCaching});

        return const Result.success(null);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Clear operation failed', e, stackTrace, 'repository_clear');
        return Result.failure(AppError.storage('Failed to clear data: $e'));
      }
    });
  }

  // ============================================================================
  // BATCH OPERATIONS
  // ============================================================================

  @override
  Future<Result<void>> storeBatch(Map<String, dynamic> data, {Map<String, dynamic>? metadata}) async {
    return _executeWithMetrics('storeBatch', () async {
      try {
        // Process in batches to avoid overwhelming the system
        final keys = data.keys.toList();
        final batchSize = _config.batchSize;

        for (var i = 0; i < keys.length; i += batchSize) {
          final batchKeys = keys.skip(i).take(batchSize);
          final futures = batchKeys.map((key) => store(key, data[key], metadata: metadata));

          final results = await Future.wait(futures);

          // Check if any operation failed
          for (final result in results) {
            if (result.isFailure) {
              return Result.failure(result.errorOrNull!);
            }
          }
        }

        AppLogger.info(
          'Batch store completed successfully',
          metadata: {
            'total_items': data.length,
            'batch_size': batchSize,
            'batches_processed': (keys.length / batchSize).ceil(),
          },
        );

        return const Result.success(null);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Batch store operation failed', e, stackTrace, 'repository_storeBatch');
        return Result.failure(AppError.storage('Failed to store batch data: $e'));
      }
    });
  }

  @override
  Future<Result<Map<String, dynamic>>> retrieveBatch(List<String> keys) async {
    return _executeWithMetrics('retrieveBatch', () async {
      try {
        final result = <String, dynamic>{};
        final batchSize = _config.batchSize;

        for (var i = 0; i < keys.length; i += batchSize) {
          final batchKeys = keys.skip(i).take(batchSize);
          final futures = batchKeys.map((key) => retrieve<dynamic>(key));

          final results = await Future.wait(futures);

          for (var j = 0; j < results.length; j++) {
            final key = batchKeys.elementAt(j);
            final retrieveResult = results[j];

            if (retrieveResult.isSuccess && retrieveResult.valueOrNull != null) {
              result[key] = retrieveResult.valueOrNull!;
            }
          }
        }

        AppLogger.info(
          'Batch retrieve completed successfully',
          metadata: {'requested_keys': keys.length, 'found_items': result.length, 'batch_size': batchSize},
        );

        return Result.success(result);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Batch retrieve operation failed', e, stackTrace, 'repository_retrieveBatch');
        return Result.failure(AppError.storage('Failed to retrieve batch data: $e'));
      }
    });
  }

  @override
  Future<Result<void>> removeBatch(List<String> keys) async {
    return _executeWithMetrics('removeBatch', () async {
      try {
        final batchSize = _config.batchSize;

        for (var i = 0; i < keys.length; i += batchSize) {
          final batchKeys = keys.skip(i).take(batchSize);
          final futures = batchKeys.map((key) => remove(key));

          final results = await Future.wait(futures);

          // Check if any operation failed
          for (final result in results) {
            if (result.isFailure) {
              return Result.failure(result.errorOrNull!);
            }
          }
        }

        AppLogger.info(
          'Batch remove completed successfully',
          metadata: {
            'removed_keys': keys.length,
            'batch_size': batchSize,
            'batches_processed': (keys.length / batchSize).ceil(),
          },
        );

        return const Result.success(null);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Batch remove operation failed', e, stackTrace, 'repository_removeBatch');
        return Result.failure(AppError.storage('Failed to remove batch data: $e'));
      }
    });
  }

  // ============================================================================
  // QUERY OPERATIONS
  // ============================================================================

  @override
  Future<Result<List<String>>> getAllKeys() async {
    return _executeWithMetrics('getAllKeys', () async {
      try {
        // For now, return cached keys if caching is enabled
        if (_config.enableCaching) {
          final keys = _cache.keys.where((key) {
            final entry = _cache[key]!;
            return !_isCacheEntryExpired(entry);
          }).toList();

          AppLogger.debug('Retrieved keys from cache', metadata: {'key_count': keys.length});

          return Result.success(keys);
        }

        // This would need to be implemented based on the underlying storage
        AppLogger.warning('getAllKeys not fully implemented for non-cached storage');
        return const Result.success(<String>[]);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Get all keys operation failed', e, stackTrace, 'repository_getAllKeys');
        return Result.failure(AppError.storage('Failed to get all keys: $e'));
      }
    });
  }

  @override
  Future<Result<List<String>>> getKeysMatching(String pattern) async {
    return _executeWithMetrics('getKeysMatching', () async {
      try {
        final allKeysResult = await getAllKeys();
        if (allKeysResult.isFailure) {
          return Result.failure(allKeysResult.errorOrNull!);
        }

        final allKeys = allKeysResult.valueOrNull!;
        final regex = RegExp(pattern.replaceAll('*', '.*'));
        final matchingKeys = allKeys.where((key) => regex.hasMatch(key)).toList();

        AppLogger.debug(
          'Pattern matching completed',
          metadata: {'pattern': pattern, 'total_keys': allKeys.length, 'matching_keys': matchingKeys.length},
        );

        return Result.success(matchingKeys);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Pattern matching operation failed', e, stackTrace, 'repository_getKeysMatching');
        return Result.failure(AppError.storage('Failed to match keys: $e'));
      }
    });
  }

  @override
  Future<Result<int?>> getSize(String key) async {
    return _executeWithMetrics('getSize', () async {
      try {
        // Check if data exists first
        final existsResult = await exists(key);
        if (existsResult.isFailure || !existsResult.valueOrNull!) {
          return const Result.success(null);
        }

        // For cached data, estimate size
        if (_config.enableCaching && _cache.containsKey(key)) {
          final entry = _cache[key]!;
          if (!_isCacheEntryExpired(entry)) {
            final serialized = jsonEncode(entry.value);
            return Result.success(serialized.length);
          }
        }

        // This would need to be implemented based on the underlying storage
        AppLogger.debug('Size calculation not fully implemented');
        return const Result.success(null);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Get size operation failed', e, stackTrace, 'repository_getSize');
        return Result.failure(AppError.storage('Failed to get size: $e'));
      }
    });
  }

  @override
  Future<Result<Map<String, dynamic>?>> getMetadata(String key) async {
    return _executeWithMetrics('getMetadata', () async {
      try {
        // Check cache first
        if (_config.enableCaching && _cache.containsKey(key)) {
          final entry = _cache[key]!;
          if (!_isCacheEntryExpired(entry)) {
            return Result.success(entry.metadata);
          }
        }

        // This would need to be implemented based on the underlying storage
        AppLogger.debug('Metadata retrieval not fully implemented');
        return const Result.success(null);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Get metadata operation failed', e, stackTrace, 'repository_getMetadata');
        return Result.failure(AppError.storage('Failed to get metadata: $e'));
      }
    });
  }

  // ============================================================================
  // FILE OPERATIONS
  // ============================================================================

  @override
  Future<Result<void>> storeBinary(
    String key,
    Uint8List data, {
    String? contentType,
    Map<String, dynamic>? metadata,
  }) async {
    return _executeWithMetrics('storeBinary', () async {
      try {
        final result = await _storageService.store(
          key,
          data,
          type: StorageType.file,
          options: StorageOptions(
            compress: _config.enableCompression,
            encrypt: _config.enableEncryption,
            metadata: {...?metadata, 'content_type': contentType, 'data_type': 'binary', 'size_bytes': data.length},
          ),
        );

        if (result.isSuccess) {
          AppLogger.debug(
            'Binary data stored successfully',
            metadata: {'key': key, 'size_bytes': data.length, 'content_type': contentType},
          );
        }

        return result;
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Store binary operation failed', e, stackTrace, 'repository_storeBinary');
        return Result.failure(AppError.storage('Failed to store binary data: $e'));
      }
    });
  }

  @override
  Future<Result<Uint8List?>> retrieveBinary(String key) async {
    return _executeWithMetrics('retrieveBinary', () async {
      try {
        final result = await _storageService.retrieve<Uint8List>(key, type: StorageType.file);

        if (result.isSuccess) {
          AppLogger.debug(
            'Binary data retrieved successfully',
            metadata: {'key': key, 'size_bytes': result.valueOrNull?.length ?? 0},
          );
        }

        return result;
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Retrieve binary operation failed', e, stackTrace, 'repository_retrieveBinary');
        return Result.failure(AppError.storage('Failed to retrieve binary data: $e'));
      }
    });
  }

  // ============================================================================
  // TRANSACTION SUPPORT
  // ============================================================================

  @override
  Future<Result<T>> transaction<T>(Future<Result<T>> Function(StorageRepositoryInterface repo) operations) async {
    return _executeWithMetrics('transaction', () async {
      try {
        // Simple transaction implementation - could be enhanced with rollback
        final result = await operations(this);

        AppLogger.debug(
          'Transaction completed',
          metadata: {'success': result.isSuccess, 'error': result.isFailure ? result.errorOrNull.toString() : null},
        );

        return result;
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Transaction failed', e, stackTrace, 'repository_transaction');
        return Result.failure(AppError.storage('Transaction failed: $e'));
      }
    });
  }

  // ============================================================================
  // CACHE MANAGEMENT
  // ============================================================================

  @override
  Future<Result<void>> invalidateCache(String key) async {
    return _executeWithMetrics('invalidateCache', () async {
      try {
        _cache.remove(key);
        _lastAccess.remove(key);

        AppLogger.debug('Cache invalidated for key', metadata: {'key': key});

        return const Result.success(null);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Cache invalidation failed', e, stackTrace, 'repository_invalidateCache');
        return Result.failure(AppError.storage('Failed to invalidate cache: $e'));
      }
    });
  }

  @override
  Future<Result<void>> clearCache() async {
    return _executeWithMetrics('clearCache', () async {
      try {
        final cacheSize = _cache.length;
        _cache.clear();
        _lastAccess.clear();

        AppLogger.info('Cache cleared successfully', metadata: {'cleared_items': cacheSize});

        return const Result.success(null);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Cache clear failed', e, stackTrace, 'repository_clearCache');
        return Result.failure(AppError.storage('Failed to clear cache: $e'));
      }
    });
  }

  @override
  Future<Result<Map<String, dynamic>>> getCacheStats() async {
    return _executeWithMetrics('getCacheStats', () async {
      try {
        final stats = {
          'cache_enabled': _config.enableCaching,
          'cache_size': _cache.length,
          'max_cache_size': _config.cacheSize,
          'cache_hits': _cacheHits,
          'cache_misses': _cacheMisses,
          'hit_rate': _totalOperations > 0 ? _cacheHits / _totalOperations : 0.0,
          'expired_entries': _cache.values.where(_isCacheEntryExpired).length,
        };

        return Result.success(stats);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Get cache stats failed', e, stackTrace, 'repository_getCacheStats');
        return Result.failure(AppError.storage('Failed to get cache stats: $e'));
      }
    });
  }

  // ============================================================================
  // REPOSITORY METADATA
  // ============================================================================

  @override
  Future<Result<Map<String, dynamic>>> getStats() async {
    return _executeWithMetrics('getStats', () async {
      try {
        final stats = {
          'total_operations': _totalOperations,
          'operation_counts': Map.from(_operationCounts),
          'operation_times': _operationTimes.map((k, v) => MapEntry(k, v.inMilliseconds)),
          'cache_stats': (await getCacheStats()).valueOrNull,
          'config': {
            'caching_enabled': _config.enableCaching,
            'compression_enabled': _config.enableCompression,
            'encryption_enabled': _config.enableEncryption,
            'batch_size': _config.batchSize,
          },
        };

        return Result.success(stats);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Get stats failed', e, stackTrace, 'repository_getStats');
        return Result.failure(AppError.storage('Failed to get stats: $e'));
      }
    });
  }

  @override
  Future<Result<Map<String, dynamic>>> getHealth() async {
    return _executeWithMetrics('getHealth', () async {
      try {
        final health = {
          'status': 'healthy',
          'cache_enabled': _config.enableCaching,
          'cache_utilization': _config.enableCaching ? _cache.length / _config.cacheSize : 0.0,
          'total_operations': _totalOperations,
          'error_rate': 0.0, // Would need to track errors
          'last_operation': DateTime.now().toIso8601String(),
        };

        return Result.success(health);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Get health failed', e, stackTrace, 'repository_getHealth');
        return Result.failure(AppError.storage('Failed to get health: $e'));
      }
    });
  }

  @override
  Future<Result<void>> optimize() async {
    return _executeWithMetrics('optimize', () async {
      try {
        // Clean up expired cache entries
        if (_config.enableCaching) {
          final expiredKeys = _cache.keys.where((key) {
            final entry = _cache[key]!;
            return _isCacheEntryExpired(entry);
          }).toList();

          for (final key in expiredKeys) {
            _cache.remove(key);
            _lastAccess.remove(key);
          }

          AppLogger.info(
            'Repository optimization completed',
            metadata: {'expired_entries_removed': expiredKeys.length, 'remaining_cache_size': _cache.length},
          );
        }

        return const Result.success(null);
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Optimization failed', e, stackTrace, 'repository_optimize');
        return Result.failure(AppError.storage('Failed to optimize: $e'));
      }
    });
  }

  @override
  Future<Result<void>> backup(String destination) async {
    return _executeWithMetrics('backup', () async {
      try {
        // This would need to be implemented based on requirements
        AppLogger.warning('Backup operation not implemented');
        return Result.failure(AppError.storage('Backup operation not implemented'));
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Backup failed', e, stackTrace, 'repository_backup');
        return Result.failure(AppError.storage('Failed to backup: $e'));
      }
    });
  }

  @override
  Future<Result<void>> restore(String source) async {
    return _executeWithMetrics('restore', () async {
      try {
        // This would need to be implemented based on requirements
        AppLogger.warning('Restore operation not implemented');
        return Result.failure(AppError.storage('Restore operation not implemented'));
      } on Exception catch (e, stackTrace) {
        AppLogger.error('Restore failed', e, stackTrace, 'repository_restore');
        return Result.failure(AppError.storage('Failed to restore: $e'));
      }
    });
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// Execute an operation with performance metrics tracking
  Future<Result<T>> _executeWithMetrics<T>(String operationName, Future<Result<T>> Function() operation) async {
    final stopwatch = Stopwatch()..start();
    _totalOperations++;
    _operationCounts[operationName] = (_operationCounts[operationName] ?? 0) + 1;

    try {
      final result = await operation();
      stopwatch.stop();

      _operationTimes[operationName] = stopwatch.elapsed;

      if (_config.enableMetrics) {
        AppLogger.debug(
          'Operation completed',
          metadata: {
            'operation': operationName,
            'duration_ms': stopwatch.elapsedMilliseconds,
            'success': result.isSuccess,
            'total_operations': _totalOperations,
          },
        );
      }

      return result;
    } on Exception {
      stopwatch.stop();
      _operationTimes[operationName] = stopwatch.elapsed;
      rethrow;
    }
  }

  /// Update cache with new data
  void _updateCache<T>(String key, T value, Map<String, dynamic>? metadata) {
    if (!_config.enableCaching) return;

    // Remove oldest entries if cache is full
    if (_cache.length >= _config.cacheSize) {
      _evictOldestCacheEntry();
    }

    _cache[key] = _CacheEntry(value: value, timestamp: DateTime.now(), metadata: metadata);
    _lastAccess[key] = DateTime.now();
  }

  /// Get data from cache if available and not expired
  T? _getFromCache<T>(String key) {
    if (!_config.enableCaching || !_cache.containsKey(key)) {
      return null;
    }

    final entry = _cache[key]!;
    if (_isCacheEntryExpired(entry)) {
      _cache.remove(key);
      _lastAccess.remove(key);
      return null;
    }

    _lastAccess[key] = DateTime.now();
    return entry.value as T?;
  }

  /// Check if a cache entry has expired
  bool _isCacheEntryExpired(_CacheEntry entry) {
    final now = DateTime.now();
    return now.difference(entry.timestamp) > _config.cacheTtl;
  }

  /// Evict the oldest cache entry based on last access time
  void _evictOldestCacheEntry() {
    if (_lastAccess.isEmpty) return;

    String? oldestKey;
    DateTime? oldestTime;

    for (final entry in _lastAccess.entries) {
      if (oldestTime == null || entry.value.isBefore(oldestTime)) {
        oldestTime = entry.value;
        oldestKey = entry.key;
      }
    }

    if (oldestKey != null) {
      _cache.remove(oldestKey);
      _lastAccess.remove(oldestKey);
    }
  }
}

/// Context7 MCP: Cache Entry Model
///
/// Represents a cached item with timestamp and metadata
class _CacheEntry {
  const _CacheEntry({required this.value, required this.timestamp, this.metadata});

  /// The cached value
  final dynamic value;

  /// When the entry was created
  final DateTime timestamp;

  /// Optional metadata associated with the entry
  final Map<String, dynamic>? metadata;
}
