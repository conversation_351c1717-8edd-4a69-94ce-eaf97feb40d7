import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/notifications/providers/unified_notification_provider.dart';
import '../../../../core/settings/notification/notification_settings_state.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/app_safe_area.dart';
// Context7 MCP: Use unified notification provider for settings
// import '../../../../core/settings/providers/unified_notification_settings_provider.dart' as unified_settings;

import '../widgets/main_notification_toggle.dart';
import '../widgets/minutes_before_section.dart';
import '../widgets/notification_settings_header.dart';
import '../widgets/notification_timing_section.dart';
import '../widgets/prayer_notifications_section.dart';

/// Page for notification settings
class NotificationSettingsPage extends ConsumerWidget {
  /// Constructor
  const NotificationSettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Context7 MCP: Get notification settings from unified provider
    final notificationSettingsAsync = ref.watch(unifiedNotificationSettingsProvider);

    // Check if the current language is Arabic
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    // Check if the current theme is dark
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return notificationSettingsAsync.when(
      loading: () => Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: const Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error loading settings: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.invalidate(unifiedNotificationSettingsProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
      data: (notificationSettings) => Scaffold(
        // Use the same background color as Duas page (theme surface color)
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: AppSafeArea.fullScreen(
          backgroundColor: Theme.of(context).colorScheme.surface,
          child: Column(
            children: [
              // Custom header matching about page design
              NotificationSettingsHeader(isRtl: isArabic),
              // Main content
              Expanded(
                // ignore: use_decorated_box - Container has both decoration and child
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.blue[50]!, // Light blue background to match sidebar theme
                        isDarkMode ? AppColors.surfaceDark : AppColors.surfaceLight,
                      ],
                      stops: const [0.0, 0.3],
                    ),
                  ),
                  child: ListView(
                    padding: const EdgeInsets.all(12.0), // Reduced from 16.0
                    children: [
                      // Main notification toggle
                      const MainNotificationToggle(),

                      if (notificationSettings.generalSettings.globallyEnabled) ...[
                        const SizedBox(height: 12), // Reduced from 16
                        // Notification timing option
                        NotificationTimingSection(isArabic: isArabic),

                        // Context7 MCP: Show minutes before setting if timing is configured
                        if (notificationSettings.globalTiming != NotificationTiming.exactTime) ...[
                          const SizedBox(height: 12), // Reduced from 16
                          // Minutes before setting
                          MinutesBeforeSection(isArabic: isArabic, isDarkMode: isDarkMode),
                        ],

                        const SizedBox(height: 12), // Reduced from 16
                        // Prayer notifications
                        PrayerNotificationsSection(isArabic: isArabic, isDarkMode: isDarkMode),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
