import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/permission_models.dart';
import '../models/troubleshooting_models.dart';
import '../services/permission_troubleshooting_service.dart';

/// Troubleshooting Wizard Widget
///
/// **Task 3.3.4: Create permission troubleshooting guides and helpers**
///
/// Interactive troubleshooting wizard that guides users through resolving
/// notification permission issues following Context7 MCP UI patterns.
///
/// Features:
/// - Step-by-step troubleshooting guidance
/// - Interactive progress tracking
/// - Automated diagnosis and resolution
/// - Platform-specific instructions
/// - Accessibility-friendly interface
/// - Real-time validation and feedback
/// - Quick fix suggestions
/// - Support information generation
class TroubleshootingWizardWidget extends ConsumerStatefulWidget {
  final List<PermissionNotificationType> deniedTypes;
  final VoidCallback? onCompleted;
  final VoidCallback? onCancelled;

  const TroubleshootingWizardWidget({super.key, required this.deniedTypes, this.onCompleted, this.onCancelled});

  @override
  ConsumerState<TroubleshootingWizardWidget> createState() => _TroubleshootingWizardWidgetState();
}

class _TroubleshootingWizardWidgetState extends ConsumerState<TroubleshootingWizardWidget>
    with TickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<double> _fadeAnimation;

  TroubleshootingGuide? _guide;

  int _currentStepIndex = 0;
  bool _isLoading = true;
  bool _isRunningDiagnosis = false;
  String? _error;
  final Set<String> _completedSteps = {};

  late final PermissionTroubleshootingService _troubleshootingService;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));

    // Initialize troubleshooting service
    _troubleshootingService = PermissionTroubleshootingService(ref: ref);

    // Load troubleshooting guide
    _loadTroubleshootingGuide();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Load troubleshooting guide
  Future<void> _loadTroubleshootingGuide() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Generate comprehensive troubleshooting guide
      final guide = await _troubleshootingService.getTroubleshootingGuide(
        deniedTypes: widget.deniedTypes,
        includeSystemInfo: true,
        includeCommonSolutions: true,
        includePlatformSpecific: true,
        level: TroubleshootingLevel.comprehensive,
      );

      setState(() {
        _guide = guide;
        _isLoading = false;
      });

      // Start animation
      _animationController.forward();
    } on Exception catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  /// Run automated diagnosis
  Future<void> _runAutomatedDiagnosis() async {
    try {
      setState(() {
        _isRunningDiagnosis = true;
      });

      final diagnosis = await _troubleshootingService.runAutomatedDiagnosis(
        checkSystemSettings: true,
        checkAppSettings: true,
        checkDeviceCompatibility: true,
        checkNetworkConnectivity: true,
      );

      setState(() {
        _isRunningDiagnosis = false;
      });

      // Show diagnosis results
      _showDiagnosisResults(diagnosis);
    } on Exception catch (e) {
      setState(() {
        _isRunningDiagnosis = false;
      });

      _showErrorDialog('Diagnosis failed: $e');
    }
  }

  /// Attempt auto-resolution
  Future<void> _attemptAutoResolution() async {
    try {
      final result = await _troubleshootingService.attemptAutoResolution(widget.deniedTypes);

      _showAutoResolutionResults(result);
    } on Exception catch (e) {
      _showErrorDialog('Auto-resolution failed: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Troubleshooting'),
        leading: IconButton(icon: const Icon(Icons.close), onPressed: widget.onCancelled),
        actions: [if (_guide != null) IconButton(icon: const Icon(Icons.help_outline), onPressed: _showHelpDialog)],
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// Build main body
  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    } else if (_error != null) {
      return _buildErrorState();
    } else if (_guide != null) {
      return _buildTroubleshootingContent();
    } else {
      return const Center(child: Text('No troubleshooting guide available'));
    }
  }

  /// Build loading state
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Analyzing permission issues...'),
          SizedBox(height: 8),
          Text('This may take a few moments', style: TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'Failed to load troubleshooting guide',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton(onPressed: _loadTroubleshootingGuide, child: const Text('Retry')),
        ],
      ),
    );
  }

  /// Build troubleshooting content
  Widget _buildTroubleshootingContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildGuideHeader(),
            const SizedBox(height: 24),
            _buildQuickActions(),
            const SizedBox(height: 24),
            _buildProgressIndicator(),
            const SizedBox(height: 24),
            _buildCurrentStep(),
            const SizedBox(height: 24),
            _buildCommonSolutions(),
            const SizedBox(height: 24),
            _buildPlatformGuidance(),
          ],
        ),
      ),
    );
  }

  /// Build guide header
  Widget _buildGuideHeader() {
    final guide = _guide!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getSeverityIcon(guide.analysis.severity),
                  color: _getSeverityColor(guide.analysis.severity),
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(guide.title, style: Theme.of(context).textTheme.headlineSmall),
                      const SizedBox(height: 4),
                      Text(guide.description, style: Theme.of(context).textTheme.bodyMedium),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildGuideStats(),
          ],
        ),
      ),
    );
  }

  /// Build guide statistics
  Widget _buildGuideStats() {
    final guide = _guide!;

    return Row(
      children: [
        _buildStatChip(icon: Icons.timer, label: 'Est. Time', value: _formatDuration(guide.estimatedCompletionTime)),
        const SizedBox(width: 8),
        _buildStatChip(
          icon: Icons.trending_up,
          label: 'Success Rate',
          value: '${(guide.successProbability * 100).round()}%',
        ),
        const SizedBox(width: 8),
        _buildStatChip(icon: Icons.assignment, label: 'Steps', value: '${guide.steps.length}'),
      ],
    );
  }

  /// Build stat chip
  Widget _buildStatChip({required IconData icon, required String label, required String value}) {
    return Chip(avatar: Icon(icon, size: 16), label: Text('$label: $value'), labelStyle: const TextStyle(fontSize: 12));
  }

  /// Build quick actions
  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Quick Actions', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isRunningDiagnosis ? null : _runAutomatedDiagnosis,
                  icon: _isRunningDiagnosis
                      ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
                      : const Icon(Icons.search),
                  label: Text(_isRunningDiagnosis ? 'Running...' : 'Run Diagnosis'),
                ),
                OutlinedButton.icon(
                  onPressed: _attemptAutoResolution,
                  icon: const Icon(Icons.auto_fix_high),
                  label: const Text('Auto Fix'),
                ),
                OutlinedButton.icon(
                  onPressed: _showQuickFixes,
                  icon: const Icon(Icons.flash_on),
                  label: const Text('Quick Fixes'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build progress indicator
  Widget _buildProgressIndicator() {
    final guide = _guide!;
    final totalSteps = guide.steps.length;
    final completedCount = _completedSteps.length;
    final progress = totalSteps > 0 ? completedCount / totalSteps : 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Progress', style: Theme.of(context).textTheme.titleMedium),
                Text('$completedCount of $totalSteps steps', style: Theme.of(context).textTheme.bodySmall),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(value: progress, backgroundColor: Colors.grey[300]),
          ],
        ),
      ),
    );
  }

  /// Build current step
  Widget _buildCurrentStep() {
    final guide = _guide!;

    if (_currentStepIndex >= guide.steps.length) {
      return _buildCompletionCard();
    }

    final currentStep = guide.steps[_currentStepIndex];
    final isCompleted = _completedSteps.contains(currentStep.id);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: isCompleted ? Colors.green : Colors.blue,
                  child: Icon(isCompleted ? Icons.check : Icons.play_arrow, color: Colors.white),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Step ${_currentStepIndex + 1}: ${currentStep.title}',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      Text(currentStep.description, style: Theme.of(context).textTheme.bodyMedium),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStepInstructions(currentStep),
            const SizedBox(height: 16),
            _buildStepActions(currentStep),
          ],
        ),
      ),
    );
  }

  /// Build step instructions
  Widget _buildStepInstructions(TroubleshootingStep step) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Instructions:', style: Theme.of(context).textTheme.titleSmall),
        const SizedBox(height: 8),
        ...step.instructions.asMap().entries.map((entry) {
          final index = entry.key;
          final instruction = entry.value;
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${index + 1}. '),
                Expanded(child: Text(instruction)),
              ],
            ),
          );
        }),
        if (step.warning != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              border: Border.all(color: Colors.orange),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                const Icon(Icons.warning, color: Colors.orange),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(step.warning!, style: const TextStyle(color: Colors.orange)),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Build step actions
  Widget _buildStepActions(TroubleshootingStep step) {
    final isCompleted = _completedSteps.contains(step.id);

    return Row(
      children: [
        if (!isCompleted) ...[
          ElevatedButton(onPressed: () => _markStepCompleted(step.id), child: const Text('Mark as Completed')),
          const SizedBox(width: 8),
          OutlinedButton(onPressed: _skipCurrentStep, child: const Text('Skip')),
        ] else ...[
          const Icon(Icons.check_circle, color: Colors.green),
          const SizedBox(width: 8),
          const Text('Completed', style: TextStyle(color: Colors.green)),
          const Spacer(),
          TextButton(onPressed: () => _markStepIncomplete(step.id), child: const Text('Mark as Incomplete')),
        ],
      ],
    );
  }

  /// Build completion card
  Widget _buildCompletionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Icon(Icons.celebration, size: 64, color: Colors.green),
            const SizedBox(height: 16),
            Text('Troubleshooting Complete!', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            const Text(
              'You have completed all troubleshooting steps. Your notification permissions should now be working correctly.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(onPressed: widget.onCompleted, child: const Text('Finish')),
          ],
        ),
      ),
    );
  }

  /// Build common solutions
  Widget _buildCommonSolutions() {
    final guide = _guide!;

    if (guide.commonSolutions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Common Solutions', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 12),
            ...guide.commonSolutions.map((solution) => _buildSolutionTile(solution)),
          ],
        ),
      ),
    );
  }

  /// Build solution tile
  Widget _buildSolutionTile(TroubleshootingSolution solution) {
    return ExpansionTile(
      leading: Icon(
        solution.isHighlyEffective ? Icons.star : Icons.lightbulb,
        color: solution.isHighlyEffective ? Colors.amber : Colors.blue,
      ),
      title: Text(solution.title),
      subtitle: Text('${(solution.successRate * 100).round()}% success rate • ${solution.difficulty.name} difficulty'),
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(solution.description),
              const SizedBox(height: 8),
              ...solution.steps.asMap().entries.map((entry) {
                final index = entry.key;
                final step = entry.value;
                return Padding(padding: const EdgeInsets.only(bottom: 4), child: Text('${index + 1}. $step'));
              }),
            ],
          ),
        ),
      ],
    );
  }

  /// Build platform guidance
  Widget _buildPlatformGuidance() {
    final guide = _guide!;

    if (guide.platformGuidance.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Platform-Specific Guidance', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 12),
            ...guide.platformGuidance.map((guidance) => _buildGuidanceTile(guidance)),
          ],
        ),
      ),
    );
  }

  /// Build guidance tile
  Widget _buildGuidanceTile(PlatformGuidance guidance) {
    return ExpansionTile(
      leading: Icon(_getPlatformIcon(guidance.platform)),
      title: Text('${guidance.platform} ${guidance.version}'),
      subtitle: Text(guidance.guidance),
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (guidance.specificSteps.isNotEmpty) ...[
                Text('Specific Steps:', style: Theme.of(context).textTheme.titleSmall),
                const SizedBox(height: 4),
                ...guidance.specificSteps.map((step) => Text('• $step')),
                const SizedBox(height: 8),
              ],
              if (guidance.commonIssues.isNotEmpty) ...[
                Text('Common Issues:', style: Theme.of(context).textTheme.titleSmall),
                const SizedBox(height: 4),
                ...guidance.commonIssues.map((issue) => Text('• $issue')),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// Build bottom bar
  Widget _buildBottomBar() {
    final guide = _guide;
    if (guide == null) return const SizedBox.shrink();

    return BottomAppBar(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            if (_currentStepIndex > 0)
              TextButton.icon(
                onPressed: _previousStep,
                icon: const Icon(Icons.arrow_back),
                label: const Text('Previous'),
              ),
            const Spacer(),
            if (_currentStepIndex < guide.steps.length - 1)
              ElevatedButton.icon(
                onPressed: _nextStep,
                icon: const Icon(Icons.arrow_forward),
                label: const Text('Next'),
              ),
          ],
        ),
      ),
    );
  }

  // ============================================================================
  // ACTION METHODS
  // ============================================================================

  /// Mark step as completed
  void _markStepCompleted(String stepId) {
    setState(() {
      _completedSteps.add(stepId);
    });

    // Auto-advance to next step
    if (_currentStepIndex < _guide!.steps.length - 1) {
      _nextStep();
    }
  }

  /// Mark step as incomplete
  void _markStepIncomplete(String stepId) {
    setState(() {
      _completedSteps.remove(stepId);
    });
  }

  /// Skip current step
  void _skipCurrentStep() {
    if (_currentStepIndex < _guide!.steps.length - 1) {
      _nextStep();
    }
  }

  /// Go to next step
  void _nextStep() {
    if (_currentStepIndex < _guide!.steps.length - 1) {
      setState(() {
        _currentStepIndex++;
      });
    }
  }

  /// Go to previous step
  void _previousStep() {
    if (_currentStepIndex > 0) {
      setState(() {
        _currentStepIndex--;
      });
    }
  }

  /// Show quick fixes
  void _showQuickFixes() {
    final quickFixes = _troubleshootingService.getQuickFixes(widget.deniedTypes);

    showModalBottomSheet(context: context, builder: (context) => _buildQuickFixesSheet(quickFixes));
  }

  /// Build quick fixes sheet
  Widget _buildQuickFixesSheet(List<QuickFix> quickFixes) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Quick Fixes', style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 16),
          ...quickFixes.map(
            (fix) => ListTile(
              leading: Icon(
                fix.isVeryQuick ? Icons.flash_on : Icons.build,
                color: fix.isHighPriority ? Colors.red : Colors.blue,
              ),
              title: Text(fix.title),
              subtitle: Text(fix.description),
              trailing: Text(_formatDuration(fix.estimatedTime)),
              onTap: () {
                Navigator.pop(context);
                _showQuickFixDetails(fix);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Show quick fix details
  void _showQuickFixDetails(QuickFix fix) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(fix.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(fix.description),
            const SizedBox(height: 16),
            Text('Action:', style: Theme.of(context).textTheme.titleSmall),
            const SizedBox(height: 4),
            Text(fix.action),
            const SizedBox(height: 16),
            Text('Estimated time: ${_formatDuration(fix.estimatedTime)}', style: Theme.of(context).textTheme.bodySmall),
          ],
        ),
        actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('Close'))],
      ),
    );
  }

  /// Show diagnosis results
  void _showDiagnosisResults(TroubleshootingDiagnosis diagnosis) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Diagnosis Results'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(diagnosis.summary),
              const SizedBox(height: 16),
              if (diagnosis.issues.isNotEmpty) ...[
                Text('Issues Found:', style: Theme.of(context).textTheme.titleSmall),
                const SizedBox(height: 8),
                ...diagnosis.issues.map(
                  (issue) => ListTile(
                    leading: Text(issue.severityIcon),
                    title: Text(issue.title),
                    subtitle: Text(issue.description),
                    dense: true,
                  ),
                ),
              ],
              if (diagnosis.recommendations.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text('Recommendations:', style: Theme.of(context).textTheme.titleSmall),
                const SizedBox(height: 8),
                ...diagnosis.recommendations.map((rec) => Text('• $rec')),
              ],
            ],
          ),
        ),
        actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('Close'))],
      ),
    );
  }

  /// Show auto-resolution results
  void _showAutoResolutionResults(AutoResolutionResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Auto-Resolution Results'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(result.summary),
            const SizedBox(height: 16),
            if (result.resolvedPermissions.isNotEmpty) ...[
              Text('Resolved Permissions:', style: Theme.of(context).textTheme.titleSmall),
              ...result.resolvedPermissions.map((perm) => Text('✅ ${perm.name}')),
              const SizedBox(height: 8),
            ],
            if (result.failedPermissions.isNotEmpty) ...[
              Text('Failed Permissions:', style: Theme.of(context).textTheme.titleSmall),
              ...result.failedPermissions.map((perm) => Text('❌ ${perm.name}')),
            ],
          ],
        ),
        actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('Close'))],
      ),
    );
  }

  /// Show help dialog
  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Troubleshooting Help'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('This troubleshooting wizard helps you resolve notification permission issues step by step.'),
              SizedBox(height: 16),
              Text('Features:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• Step-by-step guidance'),
              Text('• Automated diagnosis'),
              Text('• Quick fixes'),
              Text('• Platform-specific instructions'),
              Text('• Progress tracking'),
              SizedBox(height: 16),
              Text('Tips:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• Follow steps in order for best results'),
              Text('• Use "Run Diagnosis" to identify issues automatically'),
              Text('• Try "Auto Fix" for common problems'),
              Text('• Check platform-specific guidance for your device'),
            ],
          ),
        ),
        actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('Got it'))],
      ),
    );
  }

  /// Show error dialog
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('OK'))],
      ),
    );
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /// Get severity icon
  IconData _getSeverityIcon(TroubleshootingSeverity severity) {
    switch (severity) {
      case TroubleshootingSeverity.critical:
        return Icons.error;
      case TroubleshootingSeverity.high:
        return Icons.warning;
      case TroubleshootingSeverity.medium:
        return Icons.info;
      case TroubleshootingSeverity.low:
        return Icons.check_circle;
    }
  }

  /// Get severity color
  Color _getSeverityColor(TroubleshootingSeverity severity) {
    switch (severity) {
      case TroubleshootingSeverity.critical:
        return Colors.red;
      case TroubleshootingSeverity.high:
        return Colors.orange;
      case TroubleshootingSeverity.medium:
        return Colors.amber;
      case TroubleshootingSeverity.low:
        return Colors.green;
    }
  }

  /// Get platform icon
  IconData _getPlatformIcon(String platform) {
    switch (platform.toLowerCase()) {
      case 'android':
        return Icons.android;
      case 'ios':
        return Icons.phone_iphone;
      default:
        return Icons.devices;
    }
  }

  /// Format duration
  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m';
    } else {
      return '${duration.inSeconds}s';
    }
  }
}
