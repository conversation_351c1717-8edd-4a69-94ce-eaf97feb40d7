import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';

/// Migration Tracking Service
///
/// **Task 4.1.1: Implement backward compatibility providers for gradual migration**
///
/// This service tracks the usage of legacy providers and provides migration
/// analytics following Context7 MCP patterns for comprehensive migration monitoring.
///
/// Features:
/// - Legacy provider usage tracking
/// - Migration progress analytics
/// - Deprecation warning management
/// - Migration completion detection
/// - Performance impact monitoring
/// - Migration guidance recommendations
/// - Automated migration reports
/// - Developer dashboard integration
class MigrationTrackingService {
  static final MigrationTrackingService _instance = MigrationTrackingService._internal();
  factory MigrationTrackingService() => _instance;
  MigrationTrackingService._internal();

  final Map<String, LegacyProviderUsage> _legacyUsageStats = {};
  final Map<String, UnifiedProviderUsage> _unifiedUsageStats = {};
  final List<MigrationEvent> _migrationEvents = [];
  final StreamController<MigrationEvent> _eventController = StreamController.broadcast();

  /// Stream of migration events
  Stream<MigrationEvent> get migrationEvents => _eventController.stream;

  /// Track legacy provider usage
  ///
  /// **Context7 MCP Implementation:**
  /// - Single responsibility: Focused on usage tracking
  /// - Open/closed principle: Extensible for new provider types
  /// - Dependency inversion: Uses abstract tracking interfaces
  /// - Interface segregation: Specific tracking methods for different usage types
  ///
  /// **Usage:**
  /// ```dart
  /// MigrationTrackingService().trackLegacyUsage(
  ///   'legacyPrayerNotificationProvider',
  ///   'enableNotifications',
  ///   stackTrace: StackTrace.current,
  /// );
  /// ```
  void trackLegacyUsage(
    String providerName,
    String methodName, {
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) {
    final usageKey = '$providerName.$methodName';
    final now = DateTime.now();

    // Update usage statistics
    _legacyUsageStats.update(
      usageKey,
      (existing) => existing.copyWith(
        usageCount: existing.usageCount + 1,
        lastUsed: now,
        stackTraces: [...existing.stackTraces, if (stackTrace != null) stackTrace],
      ),
      ifAbsent: () => LegacyProviderUsage(
        providerName: providerName,
        methodName: methodName,
        usageCount: 1,
        firstUsed: now,
        lastUsed: now,
        stackTraces: stackTrace != null ? [stackTrace] : [],
        metadata: metadata ?? {},
      ),
    );

    // Create migration event
    final event = MigrationEvent(
      type: MigrationEventType.legacyUsage,
      providerName: providerName,
      methodName: methodName,
      timestamp: now,
      metadata: metadata,
    );

    _migrationEvents.add(event);
    _eventController.add(event);

    // Log deprecation warning in debug mode
    if (kDebugMode) {
      _logDeprecationWarning(providerName, methodName, stackTrace);
    }
  }

  /// Track unified provider usage
  void trackUnifiedUsage(
    String providerName,
    String methodName, {
    Map<String, dynamic>? metadata,
  }) {
    final usageKey = '$providerName.$methodName';
    final now = DateTime.now();

    // Update usage statistics
    _unifiedUsageStats.update(
      usageKey,
      (existing) => existing.copyWith(
        usageCount: existing.usageCount + 1,
        lastUsed: now,
      ),
      ifAbsent: () => UnifiedProviderUsage(
        providerName: providerName,
        methodName: methodName,
        usageCount: 1,
        firstUsed: now,
        lastUsed: now,
        metadata: metadata ?? {},
      ),
    );

    // Create migration event
    final event = MigrationEvent(
      type: MigrationEventType.unifiedUsage,
      providerName: providerName,
      methodName: methodName,
      timestamp: now,
      metadata: metadata,
    );

    _migrationEvents.add(event);
    _eventController.add(event);
  }

  /// Get migration progress report
  MigrationProgressReport getMigrationProgressReport() {
    final totalLegacyUsage = _legacyUsageStats.values
        .fold<int>(0, (sum, usage) => sum + usage.usageCount);
    
    final totalUnifiedUsage = _unifiedUsageStats.values
        .fold<int>(0, (sum, usage) => sum + usage.usageCount);

    final totalUsage = totalLegacyUsage + totalUnifiedUsage;
    final migrationPercentage = totalUsage > 0 
        ? (totalUnifiedUsage / totalUsage * 100).round()
        : 0;

    final activeLegacyProviders = _legacyUsageStats.keys
        .map((key) => key.split('.').first)
        .toSet()
        .toList();

    final recommendations = _generateMigrationRecommendations();

    return MigrationProgressReport(
      migrationPercentage: migrationPercentage,
      totalLegacyUsage: totalLegacyUsage,
      totalUnifiedUsage: totalUnifiedUsage,
      activeLegacyProviders: activeLegacyProviders,
      legacyUsageStats: Map.from(_legacyUsageStats),
      unifiedUsageStats: Map.from(_unifiedUsageStats),
      recommendations: recommendations,
      reportGeneratedAt: DateTime.now(),
    );
  }

  /// Get most used legacy providers
  List<LegacyProviderUsage> getMostUsedLegacyProviders({int limit = 10}) {
    final sortedUsage = _legacyUsageStats.values.toList()
      ..sort((a, b) => b.usageCount.compareTo(a.usageCount));
    
    return sortedUsage.take(limit).toList();
  }

  /// Get migration hotspots (areas needing immediate attention)
  List<MigrationHotspot> getMigrationHotspots() {
    final hotspots = <MigrationHotspot>[];

    // Find providers with high usage that haven't been migrated
    for (final usage in _legacyUsageStats.values) {
      if (usage.usageCount > 10) { // Threshold for high usage
        final severity = _calculateHotspotSeverity(usage);
        hotspots.add(MigrationHotspot(
          providerName: usage.providerName,
          methodName: usage.methodName,
          usageCount: usage.usageCount,
          severity: severity,
          lastUsed: usage.lastUsed,
          recommendation: _getHotspotRecommendation(usage),
        ));
      }
    }

    // Sort by severity and usage count
    hotspots.sort((a, b) {
      final severityComparison = b.severity.index.compareTo(a.severity.index);
      if (severityComparison != 0) return severityComparison;
      return b.usageCount.compareTo(a.usageCount);
    });

    return hotspots;
  }

  /// Generate migration timeline
  List<MigrationTimelineEntry> getMigrationTimeline({
    Duration? timeRange,
  }) {
    final cutoffTime = timeRange != null 
        ? DateTime.now().subtract(timeRange)
        : DateTime.fromMillisecondsSinceEpoch(0);

    final relevantEvents = _migrationEvents
        .where((event) => event.timestamp.isAfter(cutoffTime))
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    final timeline = <MigrationTimelineEntry>[];
    final dailyStats = <String, MigrationDayStats>{};

    // Group events by day
    for (final event in relevantEvents) {
      final dayKey = '${event.timestamp.year}-${event.timestamp.month}-${event.timestamp.day}';
      
      dailyStats.update(
        dayKey,
        (existing) => existing.copyWith(
          legacyUsageCount: existing.legacyUsageCount + 
              (event.type == MigrationEventType.legacyUsage ? 1 : 0),
          unifiedUsageCount: existing.unifiedUsageCount + 
              (event.type == MigrationEventType.unifiedUsage ? 1 : 0),
        ),
        ifAbsent: () => MigrationDayStats(
          date: DateTime(event.timestamp.year, event.timestamp.month, event.timestamp.day),
          legacyUsageCount: event.type == MigrationEventType.legacyUsage ? 1 : 0,
          unifiedUsageCount: event.type == MigrationEventType.unifiedUsage ? 1 : 0,
        ),
      );
    }

    // Convert to timeline entries
    for (final stats in dailyStats.values) {
      final totalUsage = stats.legacyUsageCount + stats.unifiedUsageCount;
      final migrationPercentage = totalUsage > 0 
          ? (stats.unifiedUsageCount / totalUsage * 100).round()
          : 0;

      timeline.add(MigrationTimelineEntry(
        date: stats.date,
        legacyUsageCount: stats.legacyUsageCount,
        unifiedUsageCount: stats.unifiedUsageCount,
        migrationPercentage: migrationPercentage,
      ));
    }

    timeline.sort((a, b) => a.date.compareTo(b.date));
    return timeline;
  }

  /// Export migration data for analysis
  String exportMigrationData() {
    final data = {
      'exportedAt': DateTime.now().toIso8601String(),
      'legacyUsageStats': _legacyUsageStats.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
      'unifiedUsageStats': _unifiedUsageStats.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
      'migrationEvents': _migrationEvents.map((event) => event.toJson()).toList(),
      'progressReport': getMigrationProgressReport().toJson(),
    };

    return jsonEncode(data);
  }

  /// Clear migration data (for testing or reset)
  void clearMigrationData() {
    _legacyUsageStats.clear();
    _unifiedUsageStats.clear();
    _migrationEvents.clear();
  }

  /// Dispose resources
  void dispose() {
    _eventController.close();
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// Log deprecation warning
  void _logDeprecationWarning(
    String providerName,
    String methodName,
    StackTrace? stackTrace,
  ) {
    final warning = '''
⚠️ DEPRECATION WARNING: $providerName.$methodName is deprecated.
   
   Migration Guide: https://docs.masajid-albahrain.com/migration
   
   Replace with unified provider:
   - Old: ref.read($providerName.notifier).$methodName()
   - New: ref.read(unifiedNotificationSettingsProvider.notifier).updateSettings()
   
   This provider will be removed in v2.0.0
''';

    debugPrint(warning);

    if (stackTrace != null && kDebugMode) {
      debugPrint('Stack trace:\n$stackTrace');
    }
  }

  /// Generate migration recommendations
  List<MigrationRecommendation> _generateMigrationRecommendations() {
    final recommendations = <MigrationRecommendation>[];

    // Analyze usage patterns and generate recommendations
    final highUsageProviders = _legacyUsageStats.values
        .where((usage) => usage.usageCount > 5)
        .toList()
      ..sort((a, b) => b.usageCount.compareTo(a.usageCount));

    for (final usage in highUsageProviders.take(5)) {
      recommendations.add(MigrationRecommendation(
        priority: _calculateRecommendationPriority(usage),
        title: 'Migrate ${usage.providerName}',
        description: 'This provider has ${usage.usageCount} usages and should be migrated to the unified provider.',
        providerName: usage.providerName,
        methodName: usage.methodName,
        migrationSteps: _getMigrationSteps(usage.providerName, usage.methodName),
        estimatedEffort: _estimateMigrationEffort(usage),
      ));
    }

    return recommendations;
  }

  /// Calculate hotspot severity
  MigrationHotspotSeverity _calculateHotspotSeverity(LegacyProviderUsage usage) {
    if (usage.usageCount > 100) {
      return MigrationHotspotSeverity.critical;
    } else if (usage.usageCount > 50) {
      return MigrationHotspotSeverity.high;
    } else if (usage.usageCount > 20) {
      return MigrationHotspotSeverity.medium;
    } else {
      return MigrationHotspotSeverity.low;
    }
  }

  /// Get hotspot recommendation
  String _getHotspotRecommendation(LegacyProviderUsage usage) {
    return 'Replace ${usage.providerName}.${usage.methodName} with unified provider equivalent. '
           'This method has been used ${usage.usageCount} times and should be prioritized for migration.';
  }

  /// Calculate recommendation priority
  MigrationRecommendationPriority _calculateRecommendationPriority(LegacyProviderUsage usage) {
    if (usage.usageCount > 50) {
      return MigrationRecommendationPriority.high;
    } else if (usage.usageCount > 20) {
      return MigrationRecommendationPriority.medium;
    } else {
      return MigrationRecommendationPriority.low;
    }
  }

  /// Get migration steps for specific provider/method
  List<String> _getMigrationSteps(String providerName, String methodName) {
    // This would contain specific migration steps for each provider/method combination
    return [
      'Replace $providerName with unifiedNotificationSettingsProvider',
      'Update method call from $methodName to appropriate unified method',
      'Test functionality to ensure compatibility',
      'Remove deprecated import statements',
    ];
  }

  /// Estimate migration effort
  MigrationEffort _estimateMigrationEffort(LegacyProviderUsage usage) {
    if (usage.usageCount > 50) {
      return MigrationEffort.high;
    } else if (usage.usageCount > 20) {
      return MigrationEffort.medium;
    } else {
      return MigrationEffort.low;
    }
  }
}

// ============================================================================
// DATA MODELS
// ============================================================================

/// Legacy Provider Usage Statistics
class LegacyProviderUsage {
  final String providerName;
  final String methodName;
  final int usageCount;
  final DateTime firstUsed;
  final DateTime lastUsed;
  final List<StackTrace> stackTraces;
  final Map<String, dynamic> metadata;

  const LegacyProviderUsage({
    required this.providerName,
    required this.methodName,
    required this.usageCount,
    required this.firstUsed,
    required this.lastUsed,
    required this.stackTraces,
    required this.metadata,
  });

  LegacyProviderUsage copyWith({
    String? providerName,
    String? methodName,
    int? usageCount,
    DateTime? firstUsed,
    DateTime? lastUsed,
    List<StackTrace>? stackTraces,
    Map<String, dynamic>? metadata,
  }) {
    return LegacyProviderUsage(
      providerName: providerName ?? this.providerName,
      methodName: methodName ?? this.methodName,
      usageCount: usageCount ?? this.usageCount,
      firstUsed: firstUsed ?? this.firstUsed,
      lastUsed: lastUsed ?? this.lastUsed,
      stackTraces: stackTraces ?? this.stackTraces,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'providerName': providerName,
      'methodName': methodName,
      'usageCount': usageCount,
      'firstUsed': firstUsed.toIso8601String(),
      'lastUsed': lastUsed.toIso8601String(),
      'stackTraceCount': stackTraces.length,
      'metadata': metadata,
    };
  }
}

/// Unified Provider Usage Statistics
class UnifiedProviderUsage {
  final String providerName;
  final String methodName;
  final int usageCount;
  final DateTime firstUsed;
  final DateTime lastUsed;
  final Map<String, dynamic> metadata;

  const UnifiedProviderUsage({
    required this.providerName,
    required this.methodName,
    required this.usageCount,
    required this.firstUsed,
    required this.lastUsed,
    required this.metadata,
  });

  UnifiedProviderUsage copyWith({
    String? providerName,
    String? methodName,
    int? usageCount,
    DateTime? firstUsed,
    DateTime? lastUsed,
    Map<String, dynamic>? metadata,
  }) {
    return UnifiedProviderUsage(
      providerName: providerName ?? this.providerName,
      methodName: methodName ?? this.methodName,
      usageCount: usageCount ?? this.usageCount,
      firstUsed: firstUsed ?? this.firstUsed,
      lastUsed: lastUsed ?? this.lastUsed,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'providerName': providerName,
      'methodName': methodName,
      'usageCount': usageCount,
      'firstUsed': firstUsed.toIso8601String(),
      'lastUsed': lastUsed.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// Migration Event
class MigrationEvent {
  final MigrationEventType type;
  final String providerName;
  final String methodName;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const MigrationEvent({
    required this.type,
    required this.providerName,
    required this.methodName,
    required this.timestamp,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'providerName': providerName,
      'methodName': methodName,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// Migration Event Type
enum MigrationEventType {
  legacyUsage,
  unifiedUsage,
  migrationCompleted,
  deprecationWarning,
}

/// Migration Progress Report
class MigrationProgressReport {
  final int migrationPercentage;
  final int totalLegacyUsage;
  final int totalUnifiedUsage;
  final List<String> activeLegacyProviders;
  final Map<String, LegacyProviderUsage> legacyUsageStats;
  final Map<String, UnifiedProviderUsage> unifiedUsageStats;
  final List<MigrationRecommendation> recommendations;
  final DateTime reportGeneratedAt;

  const MigrationProgressReport({
    required this.migrationPercentage,
    required this.totalLegacyUsage,
    required this.totalUnifiedUsage,
    required this.activeLegacyProviders,
    required this.legacyUsageStats,
    required this.unifiedUsageStats,
    required this.recommendations,
    required this.reportGeneratedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'migrationPercentage': migrationPercentage,
      'totalLegacyUsage': totalLegacyUsage,
      'totalUnifiedUsage': totalUnifiedUsage,
      'activeLegacyProviders': activeLegacyProviders,
      'recommendationCount': recommendations.length,
      'reportGeneratedAt': reportGeneratedAt.toIso8601String(),
    };
  }
}

/// Migration Hotspot
class MigrationHotspot {
  final String providerName;
  final String methodName;
  final int usageCount;
  final MigrationHotspotSeverity severity;
  final DateTime lastUsed;
  final String recommendation;

  const MigrationHotspot({
    required this.providerName,
    required this.methodName,
    required this.usageCount,
    required this.severity,
    required this.lastUsed,
    required this.recommendation,
  });
}

/// Migration Hotspot Severity
enum MigrationHotspotSeverity {
  low,
  medium,
  high,
  critical,
}

/// Migration Timeline Entry
class MigrationTimelineEntry {
  final DateTime date;
  final int legacyUsageCount;
  final int unifiedUsageCount;
  final int migrationPercentage;

  const MigrationTimelineEntry({
    required this.date,
    required this.legacyUsageCount,
    required this.unifiedUsageCount,
    required this.migrationPercentage,
  });
}

/// Migration Day Stats
class MigrationDayStats {
  final DateTime date;
  final int legacyUsageCount;
  final int unifiedUsageCount;

  const MigrationDayStats({
    required this.date,
    required this.legacyUsageCount,
    required this.unifiedUsageCount,
  });

  MigrationDayStats copyWith({
    DateTime? date,
    int? legacyUsageCount,
    int? unifiedUsageCount,
  }) {
    return MigrationDayStats(
      date: date ?? this.date,
      legacyUsageCount: legacyUsageCount ?? this.legacyUsageCount,
      unifiedUsageCount: unifiedUsageCount ?? this.unifiedUsageCount,
    );
  }
}

/// Migration Recommendation
class MigrationRecommendation {
  final MigrationRecommendationPriority priority;
  final String title;
  final String description;
  final String providerName;
  final String methodName;
  final List<String> migrationSteps;
  final MigrationEffort estimatedEffort;

  const MigrationRecommendation({
    required this.priority,
    required this.title,
    required this.description,
    required this.providerName,
    required this.methodName,
    required this.migrationSteps,
    required this.estimatedEffort,
  });
}

/// Migration Recommendation Priority
enum MigrationRecommendationPriority {
  low,
  medium,
  high,
}

/// Migration Effort
enum MigrationEffort {
  low,
  medium,
  high,
}
