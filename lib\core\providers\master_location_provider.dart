// ignore_for_file: invalid_annotation_target

import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../errors/app_error.dart';
import '../logging/app_logger.dart';
import '../models/location_data.dart';
import '../models/location_permission_models.dart';
import '../services/location/master_location_service.dart';
import '../utils/result.dart';

part 'master_location_provider.freezed.dart';
part 'master_location_provider.g.dart';

// ============================================================================
// Context7 MCP: Master Location State Management
// ============================================================================

/// Context7 MCP: Master Location State
///
/// Unified state model for all location functionality following
/// Context7 MCP best practices for state management.
@freezed
abstract class MasterLocationState with _$MasterLocationState {
  const factory MasterLocationState({
    // Core location data
    LocationData? currentLocation,
    LocationData? lastKnownLocation,

    // Permission state
    @Default(LocationPermissionStatus.unableToDetermine) LocationPermissionStatus permissionStatus,

    // Service state
    @Default(false) bool isInitialized,
    @Default(false) bool isLoading,
    @Default(false) bool isTrackingActive,
    @Default(false) bool isPaused,

    // Error state
    AppError? error,

    // Performance metrics
    @Default({}) Map<String, dynamic> performanceMetrics,
    @Default({}) Map<String, dynamic> cacheStats,

    // Stream state
    @Default(false) bool isStreamActive,
    @Default({}) Map<String, dynamic> streamSettings,

    // Analytics
    @Default({}) Map<String, dynamic> healthStatus,
  }) = _MasterLocationState;

  const MasterLocationState._();

  /// Check if location is available
  bool get hasLocation => currentLocation != null;

  /// Check if permission is granted
  bool get hasPermission => permissionStatus == LocationPermissionStatus.granted;

  /// Check if service is ready for use
  bool get isReady => isInitialized && !isLoading && hasPermission;

  /// Check if there's an active error
  bool get hasError => error != null;

  /// Get location accuracy if available
  double? get locationAccuracy => currentLocation?.accuracy;

  /// Get cache hit rate from performance metrics
  double get cacheHitRate {
    final hitRateStr = performanceMetrics['hit_rate_percentage'] as String?;
    return hitRateStr != null ? double.tryParse(hitRateStr) ?? 0.0 : 0.0;
  }

  /// Get health score from health status
  int get healthScore {
    return healthStatus['health_score'] as int? ?? 0;
  }
}

// ============================================================================
// Context7 MCP: Master Location Provider
// ============================================================================

/// Context7 MCP: Master Location Manager Provider
///
/// Unified location provider that consolidates all location functionality
/// using the MasterLocationService following Context7 MCP patterns.
///
/// Context7 MCP: Uses keepAlive to prevent auto-disposal and singleton pattern
/// to avoid LateInitializationError from multiple build() calls.
@Riverpod(keepAlive: true)
class MasterLocationManager extends _$MasterLocationManager {
  MasterLocationService? _locationService;
  StreamSubscription<LocationData>? _locationSubscription;
  StreamSubscription<LocationPermissionStatus>? _permissionSubscription;
  Timer? _performanceTimer;
  bool _isInitialized = false;

  @override
  MasterLocationState build() {
    AppLogger.info('MasterLocationManager: Initializing...');

    // Context7 MCP: Prevent multiple initialization with singleton pattern
    if (_isInitialized && _locationService != null) {
      AppLogger.debug('MasterLocationManager: Already initialized, returning existing state');
      return state;
    }

    // Initialize the master location service only once
    _locationService ??= MasterLocationService.instance;

    // Setup cleanup
    ref.onDispose(_cleanup);

    // Initialize service and setup streams
    if (!_isInitialized) {
      _initializeService();
      _isInitialized = true;
    }

    return const MasterLocationState();
  }

  /// Initialize the master location service
  Future<void> _initializeService() async {
    try {
      // Context7 MCP: Null safety check for location service
      final locationService = _locationService;
      if (locationService == null) {
        final error = AppError.initialization('Location service not available');
        state = state.copyWith(error: error, isInitialized: false);
        return;
      }

      // Initialize the service
      final initResult = await locationService.initialize();
      if (initResult.isFailure) {
        state = state.copyWith(error: initResult.errorOrNull, isInitialized: false);
        return;
      }

      // Setup permission stream
      _setupPermissionStream();

      // Setup performance monitoring
      _setupPerformanceMonitoring();

      // Check initial permission status
      await _checkInitialPermissionStatus();

      // Update state
      state = state.copyWith(isInitialized: true, error: null);

      AppLogger.info('MasterLocationManager: Successfully initialized');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MasterLocationManager: Failed to initialize', error: e, stackTrace: stackTrace);
      state = state.copyWith(
        error: AppError.initialization(
          'Failed to initialize location manager',
          originalError: e.toString(),
          stackTrace: stackTrace,
        ),
        isInitialized: false,
      );
    }
  }

  /// Setup permission status stream
  void _setupPermissionStream() {
    final locationService = _locationService;
    if (locationService == null) {
      AppLogger.error('MasterLocationManager: Cannot setup permission stream - location service is null');
      return;
    }

    _permissionSubscription = locationService.getPermissionStream().listen(
      (permissionStatus) {
        AppLogger.debug('MasterLocationManager: Permission status changed: $permissionStatus');
        final previousStatus = state.permissionStatus;

        // Update state with new permission status
        state = state.copyWith(permissionStatus: permissionStatus);

        // Context7 MCP: Handle permission grant transition
        if (previousStatus != LocationPermissionStatus.granted &&
            permissionStatus == LocationPermissionStatus.granted) {
          AppLogger.info('MasterLocationManager: Permission granted - triggering immediate location refresh');
          _handlePermissionGranted();
        }
      },
      onError: (error) {
        AppLogger.error('MasterLocationManager: Permission stream error: $error');
        state = state.copyWith(error: AppError.permission('Permission stream error: $error'));
      },
    );
  }

  /// Context7 MCP: Handle permission granted event
  ///
  /// This method ensures immediate location service availability when permission
  /// transitions from denied to granted, preventing race conditions.
  void _handlePermissionGranted() {
    // Trigger immediate location fetch in background
    Future.microtask(() async {
      try {
        AppLogger.info('MasterLocationManager: Starting immediate location fetch after permission grant');
        await getCurrentLocation(useCache: false);
        AppLogger.info('MasterLocationManager: Immediate location fetch completed successfully');
      } on Exception catch (e) {
        AppLogger.error('MasterLocationManager: Error during immediate location fetch: $e');
      }
    });
  }

  /// Setup performance monitoring timer
  void _setupPerformanceMonitoring() {
    _performanceTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _updatePerformanceMetrics();
    });
  }

  /// Check initial permission status
  Future<void> _checkInitialPermissionStatus() async {
    final locationService = _locationService;
    if (locationService == null) {
      AppLogger.error('MasterLocationManager: Cannot check permission status - location service is null');
      return;
    }

    final permissionResult = await locationService.checkPermissionStatus();
    if (permissionResult.isSuccess) {
      state = state.copyWith(permissionStatus: permissionResult.valueOrNull!);
    }
  }

  /// Update performance metrics in state
  void _updatePerformanceMetrics() {
    try {
      final locationService = _locationService;
      if (locationService == null) {
        AppLogger.debug('MasterLocationManager: Cannot update performance metrics - location service is null');
        return;
      }

      final performanceMetrics = locationService.performanceMetrics;
      final healthStatus = locationService.healthStatus;

      state = state.copyWith(
        performanceMetrics: performanceMetrics,
        healthStatus: healthStatus,
        isTrackingActive: locationService.isTrackingActive,
      );
    } on Exception catch (e) {
      AppLogger.error('MasterLocationManager: Failed to update performance metrics: $e');
    }
  }

  // ==================== Public API Methods ====================

  /// Get current location with caching
  Future<Result<LocationData>> getCurrentLocation({
    double accuracy = 10.0,
    Duration timeout = const Duration(seconds: 30),
    bool useCache = true,
  }) async {
    if (!state.isInitialized) {
      return const Result.failure(AppError.validation('Location manager not initialized'));
    }

    final locationService = _locationService;
    if (locationService == null) {
      return const Result.failure(AppError.validation('Location service not available'));
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await locationService.getCurrentLocation(accuracy: accuracy, timeout: timeout, useCache: useCache);

      if (result.isSuccess) {
        state = state.copyWith(currentLocation: result.valueOrNull, isLoading: false, error: null);
      } else {
        state = state.copyWith(isLoading: false, error: result.errorOrNull);
      }

      return result;
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MasterLocationManager: Failed to get current location', error: e, stackTrace: stackTrace);
      final error = AppError.location('Failed to get current location: $e');
      state = state.copyWith(isLoading: false, error: error);
      return Result.failure(error);
    }
  }

  /// Start location tracking stream
  Future<Result<void>> startLocationTracking({
    double accuracy = 10.0,
    double distanceFilter = 10.0,
    bool enableBackgroundMode = false,
  }) async {
    if (!state.isInitialized) {
      return const Result.failure(AppError.validation('Location manager not initialized'));
    }

    final locationService = _locationService;
    if (locationService == null) {
      return const Result.failure(AppError.validation('Location service not available'));
    }

    try {
      // Stop existing subscription
      await _locationSubscription?.cancel();

      // Create new location stream
      final locationStream = locationService.getLocationStream(
        accuracy: accuracy,
        distanceFilter: distanceFilter,
        enableBackgroundMode: enableBackgroundMode,
      );

      // Subscribe to location updates
      _locationSubscription = locationStream.listen(
        (location) {
          AppLogger.debug('MasterLocationManager: Location update received');
          state = state.copyWith(currentLocation: location, lastKnownLocation: location, isStreamActive: true);
        },
        onError: (error) {
          AppLogger.error('MasterLocationManager: Location stream error: $error');
          state = state.copyWith(error: AppError.location('Location stream error: $error'), isStreamActive: false);
        },
        onDone: () {
          AppLogger.info('MasterLocationManager: Location stream completed');
          state = state.copyWith(isStreamActive: false);
        },
      );

      state = state.copyWith(
        isStreamActive: true,
        streamSettings: {
          'accuracy': accuracy,
          'distance_filter': distanceFilter,
          'background_mode': enableBackgroundMode,
        },
      );

      AppLogger.info('MasterLocationManager: Location tracking started');
      return const Result.success(null);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MasterLocationManager: Failed to start location tracking', error: e, stackTrace: stackTrace);
      return Result.failure(AppError.location('Failed to start location tracking: $e'));
    }
  }

  /// Stop location tracking
  Future<Result<void>> stopLocationTracking() async {
    try {
      await _locationSubscription?.cancel();
      _locationSubscription = null;

      state = state.copyWith(isStreamActive: false, streamSettings: {});

      AppLogger.info('MasterLocationManager: Location tracking stopped');
      return const Result.success(null);
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MasterLocationManager: Failed to stop location tracking', error: e, stackTrace: stackTrace);
      return Result.failure(AppError.location('Failed to stop location tracking: $e'));
    }
  }

  /// Request location permission
  Future<Result<LocationPermissionStatus>> requestPermission({
    bool showRationale = true,
    bool openSettingsOnDenial = true,
  }) async {
    if (!state.isInitialized) {
      return const Result.failure(AppError.validation('Location manager not initialized'));
    }

    final locationService = _locationService;
    if (locationService == null) {
      return const Result.failure(AppError.validation('Location service not available'));
    }

    try {
      final result = await locationService.requestPermission(
        showRationale: showRationale,
        openSettingsOnDenial: openSettingsOnDenial,
      );

      if (result.isSuccess) {
        state = state.copyWith(permissionStatus: result.valueOrNull!);
      }

      return result;
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MasterLocationManager: Failed to request permission', error: e, stackTrace: stackTrace);
      return Result.failure(AppError.permission('Failed to request permission: $e'));
    }
  }

  /// Request permission proactively based on app context
  Future<Result<LocationPermissionStatus>> requestPermissionProactively({
    required bool isFirstLaunch,
    required bool tutorialCompleted,
    bool showRationale = true,
  }) async {
    if (!state.isInitialized) {
      return const Result.failure(AppError.validation('Location manager not initialized'));
    }

    final locationService = _locationService;
    if (locationService == null) {
      return const Result.failure(AppError.validation('Location service not available'));
    }

    return locationService.requestPermissionProactively(
      isFirstLaunch: isFirstLaunch,
      tutorialCompleted: tutorialCompleted,
      showRationale: showRationale,
    );
  }

  /// Clear location cache
  Future<Result<void>> clearCache({String? key}) async {
    if (!state.isInitialized) {
      return const Result.failure(AppError.validation('Location manager not initialized'));
    }

    final locationService = _locationService;
    if (locationService == null) {
      return const Result.failure(AppError.validation('Location service not available'));
    }

    return locationService.clearCache(key: key);
  }

  /// Pause location services
  Future<Result<void>> pause() async {
    if (!state.isInitialized) {
      return const Result.failure(AppError.validation('Location manager not initialized'));
    }

    final locationService = _locationService;
    if (locationService == null) {
      return const Result.failure(AppError.validation('Location service not available'));
    }

    final result = await locationService.pause();
    if (result.isSuccess) {
      state = state.copyWith(isPaused: true);
    }
    return result;
  }

  /// Resume location services
  Future<Result<void>> resume() async {
    if (!state.isInitialized) {
      return const Result.failure(AppError.validation('Location manager not initialized'));
    }

    final locationService = _locationService;
    if (locationService == null) {
      return const Result.failure(AppError.validation('Location service not available'));
    }

    final result = await locationService.resume();
    if (result.isSuccess) {
      state = state.copyWith(isPaused: false);
    }
    return result;
  }

  /// Export analytics data
  Map<String, dynamic> exportAnalyticsData() {
    if (!state.isInitialized) {
      return {'error': 'Location manager not initialized'};
    }

    final locationService = _locationService;
    if (locationService == null) {
      return {'error': 'Location service not available'};
    }

    return locationService.exportAnalyticsData();
  }

  // ==================== Cleanup ====================

  /// Cleanup resources
  void _cleanup() {
    AppLogger.info('MasterLocationManager: Cleaning up...');

    _locationSubscription?.cancel();
    _permissionSubscription?.cancel();
    _performanceTimer?.cancel();

    _locationService?.dispose();

    AppLogger.info('MasterLocationManager: Cleanup completed');
  }
}

// ============================================================================
// Context7 MCP: Selector Providers
// ============================================================================

/// Current location selector
@riverpod
LocationData? currentLocationSelector(Ref ref) {
  return ref.watch(masterLocationManagerProvider).currentLocation;
}

/// Permission status selector
@riverpod
LocationPermissionStatus permissionStatusSelector(Ref ref) {
  return ref.watch(masterLocationManagerProvider).permissionStatus;
}

/// Service ready status selector
@riverpod
bool isLocationServiceReady(Ref ref) {
  return ref.watch(masterLocationManagerProvider).isReady;
}

/// Location tracking status selector
@riverpod
bool isLocationTrackingActive(Ref ref) {
  return ref.watch(masterLocationManagerProvider).isStreamActive;
}

/// Performance metrics selector
@riverpod
Map<String, dynamic> locationPerformanceMetrics(Ref ref) {
  return ref.watch(masterLocationManagerProvider).performanceMetrics;
}

/// Health status selector
@riverpod
Map<String, dynamic> locationHealthStatus(Ref ref) {
  return ref.watch(masterLocationManagerProvider).healthStatus;
}
